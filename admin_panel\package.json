{"name": "tiktok-clone-admin", "version": "1.0.0", "description": "لوحة التحكم الإدارية لـ TikTok Clone", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "react-router-dom": "^6.18.0", "react-query": "^3.39.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@mui/x-data-grid": "^6.18.1", "@mui/x-charts": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "recharts": "^2.8.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "framer-motion": "^10.16.5", "react-spring": "^9.7.3", "react-hook-form": "^7.47.0", "yup": "^1.3.3", "@hookform/resolvers": "^3.3.2", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "react-infinite-scroll-component": "^6.1.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "react-dropzone": "^14.2.3", "react-color": "^2.19.3", "react-ace": "^10.1.0", "ace-builds": "^1.32.2", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.14.201", "@types/uuid": "^9.0.7", "@types/file-saver": "^2.0.7", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0"}, "proxy": "http://localhost:5000"}