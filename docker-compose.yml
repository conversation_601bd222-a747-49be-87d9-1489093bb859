version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: tiktok_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: tiktok_admin_2024
      MONGO_INITDB_DATABASE: tiktok_clone
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - tiktok_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tiktok_redis
    restart: unless-stopped
    command: redis-server --requirepass tiktok_redis_2024
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tiktok_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tiktok_backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: *****************************************************************************
      REDIS_URL: redis://:tiktok_redis_2024@redis:6379
      JWT_SECRET: tiktok_jwt_secret_super_secure_2024
      JWT_REFRESH_SECRET: tiktok_refresh_secret_super_secure_2024
      CORS_ORIGIN: http://localhost:3000,http://localhost:3001
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    networks:
      - tiktok_network

  # Web App (React)
  web_app:
    build:
      context: ./web_app
      dockerfile: Dockerfile
    container_name: tiktok_web
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_SOCKET_URL: http://localhost:5000
      REACT_APP_VERSION: 1.0.0
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - tiktok_network

  # Admin Panel
  admin_panel:
    build:
      context: ./admin_panel
      dockerfile: Dockerfile
    container_name: tiktok_admin
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_VERSION: 1.0.0
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - tiktok_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: tiktok_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - web_app
      - admin_panel
    networks:
      - tiktok_network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  tiktok_network:
    driver: bridge
