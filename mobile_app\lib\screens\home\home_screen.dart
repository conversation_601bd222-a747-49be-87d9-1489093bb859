// =====================================================
// الشاشة الرئيسية
// =====================================================

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/app_controller.dart';

class HomeScreen extends StatelessWidget {
  final AppController appController = Get.find<AppController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => _buildBody()),
      bottomNavigationBar: Obx(() => _buildBottomNavBar()),
    );
  }

  Widget _buildBody() {
    switch (appController.currentIndex.value) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildSearchTab();
      case 2:
        return _buildCreateTab();
      case 3:
        return _buildInboxTab();
      case 4:
        return _buildProfileTab();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.home, size: 64),
          SizedBox(height: 16),
          Text('الصفحة الرئيسية', style: Get.textTheme.headlineMedium),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  Widget _buildSearchTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 64),
          SizedBox(height: 16),
          Text('البحث', style: Get.textTheme.headlineMedium),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  Widget _buildCreateTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.add_circle, size: 64),
          SizedBox(height: 16),
          Text('إنشاء', style: Get.textTheme.headlineMedium),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  Widget _buildInboxTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox, size: 64),
          SizedBox(height: 16),
          Text('صندوق الوارد', style: Get.textTheme.headlineMedium),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  Widget _buildProfileTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person, size: 64),
          SizedBox(height: 16),
          Text('الملف الشخصي', style: Get.textTheme.headlineMedium),
          Text('قيد التطوير'),
        ],
      ),
    );
  }

  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: appController.currentIndex.value,
      onTap: appController.changeTab,
      items: [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.search),
          label: 'بحث',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.add_circle),
          label: 'إنشاء',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.inbox),
          label: 'الوارد',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'الملف',
        ),
      ],
    );
  }
}
