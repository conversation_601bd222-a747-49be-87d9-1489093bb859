// =====================================================
// Comment Routes - مسارات التعليقات
// =====================================================

const express = require('express');
const { body, query } = require('express-validator');
const commentController = require('../controllers/commentController');
const auth = require('../middleware/auth');
const optionalAuth = require('../middleware/optionalAuth');
const rateLimit = require('../middleware/rateLimit');

const router = express.Router();

// معدل محدود للتعليقات
const commentLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 comments per minute
  message: 'تم تجاوز عدد التعليقات المسموحة في الدقيقة'
});

// الحصول على تعليقات الفيديو
router.get('/video/:videoId', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100'),
  
  query('sortBy')
    .optional()
    .isIn(['recent', 'popular', 'oldest'])
    .withMessage('ترتيب التعليقات غير صحيح'),
  
  query('includeReplies')
    .optional()
    .isBoolean()
    .withMessage('includeReplies يجب أن يكون true أو false')
], commentController.getVideoComments);

// إضافة تعليق جديد
router.post('/video/:videoId', auth, commentLimiter, [
  body('content')
    .notEmpty()
    .withMessage('محتوى التعليق مطلوب')
    .isLength({ min: 1, max: 1000 })
    .withMessage('محتوى التعليق يجب أن يكون بين 1 و 1000 حرف')
    .trim(),
  
  body('parentId')
    .optional()
    .isMongoId()
    .withMessage('معرف التعليق الأب غير صحيح')
], commentController.addComment);

// الحصول على ردود التعليق
router.get('/:commentId/replies', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 50'),
  
  query('sortBy')
    .optional()
    .isIn(['oldest', 'popular'])
    .withMessage('ترتيب الردود غير صحيح')
], commentController.getCommentReplies);

// الإعجاب بالتعليق
router.post('/:commentId/like', auth, commentController.likeComment);

// إلغاء الإعجاب بالتعليق
router.delete('/:commentId/like', auth, commentController.likeComment);

// تحديث التعليق
router.put('/:commentId', auth, [
  body('content')
    .notEmpty()
    .withMessage('محتوى التعليق مطلوب')
    .isLength({ min: 1, max: 1000 })
    .withMessage('محتوى التعليق يجب أن يكون بين 1 و 1000 حرف')
    .trim()
], commentController.updateComment);

// حذف التعليق
router.delete('/:commentId', auth, commentController.deleteComment);

// تثبيت التعليق
router.post('/:commentId/pin', auth, commentController.pinComment);

// إلغاء تثبيت التعليق
router.delete('/:commentId/pin', auth, commentController.pinComment);

// إخفاء التعليق
router.post('/:commentId/hide', auth, commentController.hideComment);

// إظهار التعليق
router.delete('/:commentId/hide', auth, commentController.hideComment);

// البحث في التعليقات
router.get('/search', [
  query('q')
    .notEmpty()
    .withMessage('نص البحث مطلوب')
    .isLength({ min: 1, max: 100 })
    .withMessage('نص البحث يجب أن يكون بين 1 و 100 حرف'),
  
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100'),
  
  query('videoId')
    .optional()
    .isMongoId()
    .withMessage('معرف الفيديو غير صحيح')
], commentController.searchComments);

// الإبلاغ عن التعليق
router.post('/:commentId/report', auth, [
  body('reason')
    .notEmpty()
    .withMessage('سبب الإبلاغ مطلوب')
    .isIn([
      'spam', 'harassment', 'hate_speech', 'violence',
      'inappropriate', 'fake_news', 'scam', 'other'
    ])
    .withMessage('سبب الإبلاغ غير صحيح'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('وصف الإبلاغ يجب أن يكون أقل من 500 حرف')
    .trim()
], async (req, res) => {
  try {
    // الإبلاغ عن التعليق
    res.json({ message: 'الإبلاغ عن التعليق قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الإبلاغ عن التعليق' });
  }
});

// تقييم التعليق (مفيد/غير مفيد)
router.post('/:commentId/rate', auth, [
  body('rating')
    .notEmpty()
    .withMessage('التقييم مطلوب')
    .isIn(['helpful', 'not_helpful'])
    .withMessage('التقييم يجب أن يكون helpful أو not_helpful')
], async (req, res) => {
  try {
    // تقييم التعليق
    res.json({ message: 'تقييم التعليق قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تقييم التعليق' });
  }
});

// الحصول على إحصائيات التعليق
router.get('/:commentId/stats', auth, async (req, res) => {
  try {
    // إحصائيات التعليق
    res.json({ message: 'إحصائيات التعليق قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب إحصائيات التعليق' });
  }
});

// الحصول على تعليقات المستخدم
router.get('/user/:userId', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], async (req, res) => {
  try {
    // تعليقات المستخدم
    res.json({ message: 'تعليقات المستخدم قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب تعليقات المستخدم' });
  }
});

// الحصول على التعليقات المثبتة
router.get('/video/:videoId/pinned', optionalAuth, async (req, res) => {
  try {
    // التعليقات المثبتة
    res.json({ message: 'التعليقات المثبتة قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب التعليقات المثبتة' });
  }
});

// الحصول على أفضل التعليقات
router.get('/video/:videoId/top', optionalAuth, [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 50')
], async (req, res) => {
  try {
    // أفضل التعليقات
    res.json({ message: 'أفضل التعليقات قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب أفضل التعليقات' });
  }
});

// الحصول على التعليقات الحديثة
router.get('/recent', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], async (req, res) => {
  try {
    // التعليقات الحديثة
    res.json({ message: 'التعليقات الحديثة قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب التعليقات الحديثة' });
  }
});

module.exports = router;
