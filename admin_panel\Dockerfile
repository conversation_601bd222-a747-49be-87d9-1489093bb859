# =====================================================
# Dockerfile for TikTok Clone Admin Panel
# =====================================================

# Build stage
FROM node:18-alpine as build

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --silent

# Copy source code
COPY . .

# Build the admin panel
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built admin panel from build stage
COPY --from=build /app/build /usr/share/nginx/html

# Copy custom nginx config for admin
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
