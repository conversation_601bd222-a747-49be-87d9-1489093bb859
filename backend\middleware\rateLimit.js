// =====================================================
// Rate Limiting Middleware - وسطاء تحديد المعدل
// =====================================================

const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const Redis = require('ioredis');
const { createErrorResponse } = require('../utils/response');

// إنشاء اتصال Redis
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true
});

// معالج الأخطاء لـ Redis
redis.on('error', (err) => {
  console.error('Redis connection error:', err);
});

redis.on('connect', () => {
  console.log('Connected to Redis for rate limiting');
});

// إنشاء store للـ Redis
const redisStore = new RedisStore({
  sendCommand: (...args) => redis.call(...args),
});

// دالة إنشاء rate limiter مخصص
const createRateLimiter = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // limit each IP to 100 requests per windowMs
    message = 'تم تجاوز عدد الطلبات المسموحة',
    standardHeaders = true,
    legacyHeaders = false,
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    keyGenerator = null,
    skip = null,
    onLimitReached = null,
    store = redisStore
  } = options;

  return rateLimit({
    windowMs,
    max,
    standardHeaders,
    legacyHeaders,
    skipSuccessfulRequests,
    skipFailedRequests,
    store,
    keyGenerator: keyGenerator || ((req) => {
      // استخدام IP + User ID إذا كان متاحاً
      const ip = req.ip || req.connection.remoteAddress;
      const userId = req.user?.id || 'anonymous';
      return `${ip}:${userId}`;
    }),
    skip: skip || ((req) => {
      // تخطي المشرفين
      return req.user?.roles?.includes('admin') || req.user?.roles?.includes('super_admin');
    }),
    message: (req, res) => {
      return createErrorResponse(message, null, 'RATE_LIMIT_EXCEEDED');
    },
    onLimitReached: onLimitReached || ((req, res, options) => {
      console.warn(`Rate limit exceeded for ${req.ip} on ${req.path}`);
      
      // تسجيل محاولة تجاوز الحد
      if (req.user) {
        // يمكن إضافة تسجيل في قاعدة البيانات هنا
        console.warn(`User ${req.user.id} exceeded rate limit`);
      }
    })
  });
};

// Rate limiters مختلفة للاستخدامات المختلفة

// عام - للطلبات العادية
const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per 15 minutes
  message: 'تم تجاوز عدد الطلبات المسموحة. يرجى المحاولة لاحقاً'
});

// تسجيل الدخول
const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 login attempts per 15 minutes
  message: 'تم تجاوز عدد محاولات تسجيل الدخول المسموحة',
  skipSuccessfulRequests: true,
  keyGenerator: (req) => req.ip // استخدام IP فقط للمصادقة
});

// إنشاء الحساب
const registerLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 registrations per hour
  message: 'تم تجاوز عدد محاولات إنشاء الحساب المسموحة',
  keyGenerator: (req) => req.ip
});

// رفع الملفات
const uploadLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 uploads per hour
  message: 'تم تجاوز عدد مرات الرفع المسموحة في الساعة'
});

// رفع الفيديوهات
const videoUploadLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 video uploads per hour
  message: 'تم تجاوز عدد مرات رفع الفيديوهات المسموحة في الساعة'
});

// التعليقات
const commentLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 comments per minute
  message: 'تم تجاوز عدد التعليقات المسموحة في الدقيقة'
});

// الإعجابات
const likeLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 likes per minute
  message: 'تم تجاوز عدد الإعجابات المسموحة في الدقيقة'
});

// المتابعة
const followLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 20, // 20 follow actions per minute
  message: 'تم تجاوز عدد عمليات المتابعة المسموحة في الدقيقة'
});

// البحث
const searchLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 searches per minute
  message: 'تم تجاوز عدد عمليات البحث المسموحة في الدقيقة'
});

// API العامة
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 API calls per 15 minutes
  message: 'تم تجاوز عدد استدعاءات API المسموحة'
});

// إعادة تعيين كلمة المرور
const passwordResetLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset attempts per hour
  message: 'تم تجاوز عدد محاولات إعادة تعيين كلمة المرور المسموحة',
  keyGenerator: (req) => req.ip
});

// إرسال البريد الإلكتروني
const emailLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 emails per hour
  message: 'تم تجاوز عدد رسائل البريد الإلكتروني المسموحة في الساعة'
});

// الإبلاغ
const reportLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 reports per hour
  message: 'تم تجاوز عدد التقارير المسموحة في الساعة'
});

// المشاركة
const shareLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 20, // 20 shares per minute
  message: 'تم تجاوز عدد المشاركات المسموحة في الدقيقة'
});

// تحديث الملف الشخصي
const profileUpdateLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 profile updates per hour
  message: 'تم تجاوز عدد تحديثات الملف الشخصي المسموحة في الساعة'
});

// Rate limiter للمشرفين (أكثر تساهلاً)
const adminLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5000, // 5000 requests per 15 minutes for admins
  message: 'تم تجاوز عدد الطلبات المسموحة للمشرفين',
  skip: () => false // لا تخطي أي طلبات
});

// دالة للحصول على معلومات الحد الحالي
const getRateLimitInfo = async (key) => {
  try {
    const current = await redis.get(key);
    const ttl = await redis.ttl(key);
    
    return {
      current: parseInt(current) || 0,
      remaining: ttl > 0 ? ttl : 0
    };
  } catch (error) {
    console.error('Error getting rate limit info:', error);
    return null;
  }
};

// دالة لإعادة تعيين الحد لمستخدم معين
const resetRateLimit = async (key) => {
  try {
    await redis.del(key);
    return true;
  } catch (error) {
    console.error('Error resetting rate limit:', error);
    return false;
  }
};

// دالة لحظر IP مؤقتاً
const temporaryBan = (ip, duration = 60 * 60 * 1000) => { // 1 hour default
  return createRateLimiter({
    windowMs: duration,
    max: 0, // No requests allowed
    message: 'تم حظرك مؤقتاً بسبب تجاوز الحدود المسموحة',
    keyGenerator: () => ip,
    skip: () => false
  });
};

// وسطاء ديناميكي يختار الحد المناسب حسب المسار
const dynamicLimiter = (req, res, next) => {
  const path = req.path;
  const method = req.method;
  
  // اختيار الحد المناسب حسب المسار
  if (path.includes('/auth/login')) {
    return authLimiter(req, res, next);
  } else if (path.includes('/auth/register')) {
    return registerLimiter(req, res, next);
  } else if (path.includes('/upload')) {
    return uploadLimiter(req, res, next);
  } else if (path.includes('/comment')) {
    return commentLimiter(req, res, next);
  } else if (path.includes('/like')) {
    return likeLimiter(req, res, next);
  } else if (path.includes('/follow')) {
    return followLimiter(req, res, next);
  } else if (path.includes('/search')) {
    return searchLimiter(req, res, next);
  } else if (path.includes('/admin')) {
    return adminLimiter(req, res, next);
  } else {
    return generalLimiter(req, res, next);
  }
};

module.exports = {
  // Rate limiters
  generalLimiter,
  authLimiter,
  registerLimiter,
  uploadLimiter,
  videoUploadLimiter,
  commentLimiter,
  likeLimiter,
  followLimiter,
  searchLimiter,
  apiLimiter,
  passwordResetLimiter,
  emailLimiter,
  reportLimiter,
  shareLimiter,
  profileUpdateLimiter,
  adminLimiter,
  dynamicLimiter,
  
  // Utility functions
  createRateLimiter,
  getRateLimitInfo,
  resetRateLimit,
  temporaryBan,
  
  // Default export
  default: createRateLimiter
};
