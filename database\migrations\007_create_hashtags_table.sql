-- =====================================================
-- Migration: Create Hashtags Table
-- =====================================================

CREATE TABLE IF NOT EXISTS hashtags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    
    -- Stats
    usage_count INT DEFAULT 0,
    videos_count INT DEFAULT 0,
    trending_score DECIMAL(5,2) DEFAULT 0,
    
    -- Metadata
    description TEXT,
    category VARCHAR(50),
    language VARCHAR(10) DEFAULT 'ar',
    
    -- Status
    is_trending BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_banned BOOLEAN DEFAULT FALSE,
    is_sensitive BOOLEAN DEFAULT FALSE,
    
    -- Moderation
    moderation_status ENUM('approved', 'pending', 'rejected') DEFAULT 'approved',
    moderated_by BIGINT,
    moderated_at TIMESTAMP NULL,
    
    -- AI Analysis
    ai_category VARCHAR(100),
    ai_sentiment VARCHAR(20),
    ai_toxicity_score DECIMAL(3,2),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (moderated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_name (name),
    INDEX idx_usage_count (usage_count),
    INDEX idx_trending_score (trending_score),
    INDEX idx_is_trending (is_trending),
    INDEX idx_is_featured (is_featured),
    INDEX idx_category (category),
    INDEX idx_language (language),
    FULLTEXT INDEX idx_search (name, display_name, description)
);
