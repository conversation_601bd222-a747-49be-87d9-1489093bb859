// =====================================================
// تحكم التطبيق الرئيسي
// =====================================================

import 'package:get/get.dart';
import 'package:flutter/material.dart';

class AppController extends GetxController {
  static AppController get instance => Get.find<AppController>();
  
  final RxBool isDarkMode = false.obs;
  final RxString currentLanguage = 'ar'.obs;
  final RxInt currentIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  void _loadSettings() {
    // TODO: Load settings from storage
  }

  void changeTheme() {
    isDarkMode.value = !isDarkMode.value;
    Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
  }

  void changeLanguage(String languageCode) {
    currentLanguage.value = languageCode;
    Get.updateLocale(Locale(languageCode));
  }

  void changeTab(int index) {
    currentIndex.value = index;
  }
}
