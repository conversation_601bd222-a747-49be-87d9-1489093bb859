// =====================================================
// useTheme Hook - هوك التصميم
// =====================================================

import { useSelector, useDispatch } from 'react-redux';
import { useCallback } from 'react';
import {
  selectIsDarkMode,
  selectLanguage,
  toggleDarkMode,
  setDarkMode,
  setLanguage,
} from '../store/slices/uiSlice';

export const useTheme = () => {
  const dispatch = useDispatch();
  const isDarkMode = useSelector(selectIsDarkMode);
  const language = useSelector(selectLanguage);

  const toggleTheme = useCallback(() => {
    dispatch(toggleDarkMode());
  }, [dispatch]);

  const setTheme = useCallback((isDark) => {
    dispatch(setDarkMode(isDark));
  }, [dispatch]);

  const changeLanguage = useCallback((lang) => {
    dispatch(setLanguage(lang));
  }, [dispatch]);

  return {
    isDarkMode,
    language,
    toggleTheme,
    setTheme,
    changeLanguage,
  };
};
