// =====================================================
// لوحة التحكم الإدارية - TikTok Clone Admin Panel
// =====================================================

import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, GlobalStyles } from '@mui/material';
import { Toaster } from 'react-hot-toast';

// Store
import { store } from './store/store';

// Components
import LoadingSpinner from './components/common/LoadingSpinner';
import ErrorBoundary from './components/common/ErrorBoundary';
import AdminLayout from './components/layout/AdminLayout';

// Pages
import LoginPage from './pages/auth/LoginPage';
import DashboardPage from './pages/DashboardPage';
import UsersPage from './pages/UsersPage';
import VideosPage from './pages/VideosPage';
import FeaturesPage from './pages/FeaturesPage';
import AnalyticsPage from './pages/AnalyticsPage';
import SettingsPage from './pages/SettingsPage';
import ReportsPage from './pages/ReportsPage';
import NotFoundPage from './pages/NotFoundPage';

// Hooks
import { useAuth } from './hooks/useAuth';
import { useTheme as useAppTheme } from './hooks/useTheme';

// Services
import { initializeAdminApp } from './services/adminService';

// Styles
import './styles/admin.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Create Admin Theme
const createAdminTheme = (isDark) => createTheme({
  direction: 'rtl',
  palette: {
    mode: isDark ? 'dark' : 'light',
    primary: {
      main: '#FF0050',
      light: '#FF4081',
      dark: '#C51162',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#25F4EE',
      light: '#64FFDA',
      dark: '#00BCD4',
      contrastText: '#000000',
    },
    success: {
      main: '#00D9FF',
      light: '#4FC3F7',
      dark: '#0288D1',
    },
    warning: {
      main: '#FFB800',
      light: '#FFD54F',
      dark: '#F57C00',
    },
    error: {
      main: '#FF3040',
      light: '#FF6B6B',
      dark: '#D32F2F',
    },
    info: {
      main: '#007AFF',
      light: '#42A5F5',
      dark: '#1976D2',
    },
    background: {
      default: isDark ? '#0A0A0A' : '#F8F9FA',
      paper: isDark ? '#161823' : '#FFFFFF',
    },
    text: {
      primary: isDark ? '#FFFFFF' : '#161823',
      secondary: isDark ? '#A1A2A7' : '#69707D',
    },
  },
  typography: {
    fontFamily: '"Cairo", "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    h1: { fontSize: '2.5rem', fontWeight: 700 },
    h2: { fontSize: '2rem', fontWeight: 600 },
    h3: { fontSize: '1.75rem', fontWeight: 600 },
    h4: { fontSize: '1.5rem', fontWeight: 500 },
    h5: { fontSize: '1.25rem', fontWeight: 500 },
    h6: { fontSize: '1rem', fontWeight: 500 },
    body1: { fontSize: '1rem', lineHeight: 1.6 },
    body2: { fontSize: '0.875rem', lineHeight: 1.5 },
  },
  shape: { borderRadius: 12 },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          padding: '8px 16px',
          fontSize: '0.875rem',
          fontWeight: 600,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});

// Global styles for admin
const adminGlobalStyles = (
  <GlobalStyles
    styles={{
      '*': { margin: 0, padding: 0, boxSizing: 'border-box' },
      html: { height: '100%', scrollBehavior: 'smooth' },
      body: { height: '100%', overflowX: 'hidden' },
      '#root': { height: '100%', display: 'flex', flexDirection: 'column' },
      // Admin specific scrollbar
      '::-webkit-scrollbar': { width: '12px', height: '12px' },
      '::-webkit-scrollbar-track': { background: 'rgba(0, 0, 0, 0.05)', borderRadius: '6px' },
      '::-webkit-scrollbar-thumb': { background: '#FF0050', borderRadius: '6px' },
      '::-webkit-scrollbar-thumb:hover': { background: '#FE2C55' },
    }}
  />
);

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  
  if (isLoading) {
    return <LoadingSpinner fullScreen message="جاري التحقق من الصلاحيات..." />;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  // Check if user has admin role
  if (!user?.roles?.includes('admin') && !user?.roles?.includes('super_admin')) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <h2>غير مصرح لك بالوصول</h2>
        <p>ليس لديك صلاحية للوصول إلى لوحة التحكم الإدارية</p>
      </div>
    );
  }
  
  return children;
};

// Public Route Component
const PublicRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <LoadingSpinner fullScreen />;
  }
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return children;
};

// Main Admin App Component
function App() {
  const { isDarkMode } = useAppTheme();
  const theme = createAdminTheme(isDarkMode);

  useEffect(() => {
    // Initialize admin app
    initializeAdminApp();
  }, []);

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            {adminGlobalStyles}
            
            <Router>
              <div className="admin-app">
                <Suspense fallback={<LoadingSpinner fullScreen />}>
                  <Routes>
                    {/* Public Routes */}
                    <Route 
                      path="/login" 
                      element={
                        <PublicRoute>
                          <LoginPage />
                        </PublicRoute>
                      } 
                    />
                    
                    {/* Protected Admin Routes */}
                    <Route 
                      path="/dashboard" 
                      element={
                        <ProtectedRoute>
                          <AdminLayout>
                            <DashboardPage />
                          </AdminLayout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/users" 
                      element={
                        <ProtectedRoute>
                          <AdminLayout>
                            <UsersPage />
                          </AdminLayout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/videos" 
                      element={
                        <ProtectedRoute>
                          <AdminLayout>
                            <VideosPage />
                          </AdminLayout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/features" 
                      element={
                        <ProtectedRoute>
                          <AdminLayout>
                            <FeaturesPage />
                          </AdminLayout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/analytics" 
                      element={
                        <ProtectedRoute>
                          <AdminLayout>
                            <AnalyticsPage />
                          </AdminLayout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/reports" 
                      element={
                        <ProtectedRoute>
                          <AdminLayout>
                            <ReportsPage />
                          </AdminLayout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/settings" 
                      element={
                        <ProtectedRoute>
                          <AdminLayout>
                            <SettingsPage />
                          </AdminLayout>
                        </ProtectedRoute>
                      } 
                    />
                    
                    {/* Default redirect */}
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />
                    
                    {/* 404 Page */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </Suspense>
                
                {/* Toast Notifications */}
                <Toaster
                  position="top-left"
                  reverseOrder={false}
                  gutter={8}
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: isDarkMode ? '#161823' : '#FFFFFF',
                      color: isDarkMode ? '#FFFFFF' : '#161823',
                      borderRadius: '12px',
                      padding: '16px',
                      fontSize: '14px',
                      fontFamily: 'Cairo, sans-serif',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                    },
                    success: { iconTheme: { primary: '#00D9FF', secondary: '#FFFFFF' } },
                    error: { iconTheme: { primary: '#FF3040', secondary: '#FFFFFF' } },
                  }}
                />
              </div>
            </Router>
          </ThemeProvider>
        </QueryClientProvider>
      </Provider>
    </ErrorBoundary>
  );
}

export default App;
