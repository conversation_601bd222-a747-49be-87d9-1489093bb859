# =====================================================
# Environment Variables - TikTok Clone Admin Panel
# =====================================================

# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_SOCKET_URL=http://localhost:5000

# App Configuration
REACT_APP_NAME="TikTok Clone Admin"
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION="TikTok Clone Admin Panel"

# Admin Features
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_REPORTS=true
REACT_APP_ENABLE_USER_MANAGEMENT=true
REACT_APP_ENABLE_CONTENT_MODERATION=true
REACT_APP_ENABLE_FEATURE_FLAGS=true

# Security
REACT_APP_SESSION_TIMEOUT=3600000
REACT_APP_REQUIRE_2FA=false
REACT_APP_IP_WHITELIST_ENABLED=false

# Analytics
REACT_APP_GOOGLE_ANALYTICS_ID=GA-ADMIN-XXXXXXXXX
REACT_APP_ADMIN_TRACKING=true

# Development
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug

# Build Configuration
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false

# Performance
REACT_APP_LAZY_LOADING=true
REACT_APP_CACHE_DURATION=1800
