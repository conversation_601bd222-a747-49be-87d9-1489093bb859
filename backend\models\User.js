// =====================================================
// User Model - نموذج المستخدم
// =====================================================

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30,
    match: /^[a-zA-Z0-9_]+$/
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  fullName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  phone: {
    type: String,
    trim: true,
    match: /^\+?[1-9]\d{1,14}$/
  },
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
  avatarUrl: {
    type: String,
    default: ''
  },
  coverUrl: {
    type: String,
    default: ''
  },
  birthDate: {
    type: Date
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other'],
    default: 'other'
  },
  location: {
    type: String,
    maxlength: 100
  },
  website: {
    type: String,
    maxlength: 255
  },
  
  // Social Stats
  followersCount: {
    type: Number,
    default: 0,
    min: 0
  },
  followingCount: {
    type: Number,
    default: 0,
    min: 0
  },
  videosCount: {
    type: Number,
    default: 0,
    min: 0
  },
  likesReceivedCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Account Status
  isVerified: {
    type: Boolean,
    default: false
  },
  isPrivate: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isBanned: {
    type: Boolean,
    default: false
  },
  
  // Preferences
  language: {
    type: String,
    default: 'ar',
    enum: ['ar', 'en', 'fr', 'es', 'de']
  },
  timezone: {
    type: String,
    default: 'Asia/Riyadh'
  },
  theme: {
    type: String,
    enum: ['light', 'dark', 'auto'],
    default: 'auto'
  },
  
  // Security
  emailVerifiedAt: {
    type: Date
  },
  phoneVerifiedAt: {
    type: Date
  },
  twoFactorEnabled: {
    type: Boolean,
    default: false
  },
  twoFactorSecret: {
    type: String
  },
  lastLoginAt: {
    type: Date
  },
  lastLoginIP: {
    type: String
  },
  
  // Roles and Permissions
  roles: [{
    type: String,
    enum: ['user', 'creator', 'moderator', 'admin', 'super_admin'],
    default: 'user'
  }],
  permissions: [{
    type: String
  }],
  
  // Refresh Tokens
  refreshTokens: [{
    token: String,
    createdAt: {
      type: Date,
      default: Date.now
    },
    expiresAt: Date,
    deviceInfo: String
  }],
  
  // Device Tokens for Push Notifications
  deviceTokens: [{
    token: String,
    platform: {
      type: String,
      enum: ['ios', 'android', 'web']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.twoFactorSecret;
      delete ret.refreshTokens;
      delete ret.deviceTokens;
      return ret;
    }
  }
});

// Indexes
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ phone: 1 });
userSchema.index({ isActive: 1 });
userSchema.index({ followersCount: -1 });
userSchema.index({ isVerified: 1 });
userSchema.index({ createdAt: -1 });

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to add refresh token
userSchema.methods.addRefreshToken = function(token, expiresAt, deviceInfo) {
  this.refreshTokens.push({
    token,
    expiresAt,
    deviceInfo
  });
  
  // Keep only last 5 refresh tokens
  if (this.refreshTokens.length > 5) {
    this.refreshTokens = this.refreshTokens.slice(-5);
  }
  
  return this.save();
};

// Method to remove refresh token
userSchema.methods.removeRefreshToken = function(token) {
  this.refreshTokens = this.refreshTokens.filter(rt => rt.token !== token);
  return this.save();
};

// Method to add device token
userSchema.methods.addDeviceToken = function(token, platform) {
  // Remove existing token for same device
  this.deviceTokens = this.deviceTokens.filter(dt => dt.token !== token);
  
  this.deviceTokens.push({
    token,
    platform
  });
  
  // Keep only last 10 device tokens
  if (this.deviceTokens.length > 10) {
    this.deviceTokens = this.deviceTokens.slice(-10);
  }
  
  return this.save();
};

// Virtual for age
userSchema.virtual('age').get(function() {
  if (!this.birthDate) return null;
  return Math.floor((Date.now() - this.birthDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
});

// Static method to find by email or username
userSchema.statics.findByEmailOrUsername = function(identifier) {
  return this.findOne({
    $or: [
      { email: identifier.toLowerCase() },
      { username: identifier }
    ]
  });
};

module.exports = mongoose.model('User', userSchema);
