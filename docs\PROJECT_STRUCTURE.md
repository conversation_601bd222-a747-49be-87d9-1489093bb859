# 🏗️ هيكل المشروع التفصيلي

## 📁 نظرة عامة على المجلدات

### `/backend` - الخادم الخلفي
```
backend/
├── src/
│   ├── controllers/         # تحكم في API endpoints
│   ├── models/             # نماذج قاعدة البيانات
│   ├── routes/             # مسارات API
│   ├── middleware/         # الوسطاء
│   ├── services/           # خدمات الأعمال
│   ├── utils/              # أدوات مساعدة
│   ├── config/             # إعدادات التطبيق
│   └── app.js              # تطبيق Express الرئيسي
├── uploads/                # ملفات المستخدمين المرفوعة
├── tests/                  # اختبارات الوحدة
├── package.json
└── server.js               # نقطة البداية
```

### `/mobile_app` - تطبيق Flutter
```
mobile_app/
├── lib/
│   ├── main.dart           # نقطة البداية
│   ├── screens/            # شاشات التطبيق
│   ├── widgets/            # مكونات قابلة للإعادة
│   ├── models/             # نماذج البيانات
│   ├── services/           # خدمات API
│   ├── providers/          # إدارة الحالة
│   ├── utils/              # أدوات مساعدة
│   ├── constants/          # الثوابت
│   └── themes/             # تصميم التطبيق
├── assets/                 # الصور والملفات
├── android/                # إعدادات أندرويد
├── ios/                    # إعدادات iOS
├── test/                   # الاختبارات
└── pubspec.yaml            # تبعيات Flutter
```

### `/web_app` - موقع الويب
```
web_app/
├── src/
│   ├── components/         # مكونات React
│   ├── pages/              # صفحات الموقع
│   ├── hooks/              # React hooks مخصصة
│   ├── services/           # خدمات API
│   ├── utils/              # أدوات مساعدة
│   ├── styles/             # ملفات CSS
│   ├── assets/             # الصور والملفات
│   └── App.js              # المكون الرئيسي
├── public/                 # ملفات عامة
├── package.json
└── index.html
```

### `/admin_panel` - لوحة التحكم الإدارية
```
admin_panel/
├── src/
│   ├── components/         # مكونات الإدارة
│   ├── pages/              # صفحات الإدارة
│   ├── services/           # خدمات API
│   ├── utils/              # أدوات مساعدة
│   ├── styles/             # تصميم الإدارة
│   └── App.js              # التطبيق الرئيسي
├── public/
├── package.json
└── index.html
```

### `/database` - قاعدة البيانات
```
database/
├── migrations/             # ملفات الهجرة
├── seeds/                  # بيانات أولية
├── schema/                 # مخططات الجداول
├── backups/                # نسخ احتياطية
└── config/                 # إعدادات قاعدة البيانات
```

### `/shared` - الملفات المشتركة
```
shared/
├── constants/              # ثوابت مشتركة
├── types/                  # أنواع البيانات
├── utils/                  # أدوات مشتركة
├── validators/             # التحقق من البيانات
└── api/                    # تعريفات API
```

### `/docs` - التوثيق
```
docs/
├── API.md                  # توثيق API
├── FEATURES.md             # توثيق الميزات
├── DEPLOYMENT.md           # دليل النشر
├── DEVELOPMENT.md          # دليل التطوير
└── DATABASE.md             # توثيق قاعدة البيانات
```

## 🔄 تدفق البيانات

1. **المستخدم** يتفاعل مع التطبيق/الموقع
2. **Frontend** يرسل طلبات إلى Backend API
3. **Backend** يعالج الطلبات ويتفاعل مع قاعدة البيانات
4. **قاعدة البيانات** ترجع البيانات المطلوبة
5. **Backend** يرسل الاستجابة إلى Frontend
6. **Frontend** يعرض البيانات للمستخدم

## 🛠️ التقنيات المستخدمة

- **Backend**: Node.js + Express.js + Socket.io
- **Database**: MySQL/PostgreSQL + Redis (للتخزين المؤقت)
- **Mobile**: Flutter + Dart
- **Web**: React.js + TypeScript
- **Admin**: React.js + Material-UI
- **Cloud**: Firebase/Supabase
- **AI**: OpenAI API + Replicate API
- **Payment**: Stripe + PayPal
- **Storage**: AWS S3/Firebase Storage
- **CDN**: CloudFlare
- **Monitoring**: Sentry + Google Analytics
