-- =====================================================
-- Migration: Create Likes Table
-- =====================================================

CREATE TABLE IF NOT EXISTS likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    video_id BIGINT NOT NULL,
    
    -- Like Type
    type ENUM('like', 'love', 'laugh', 'wow', 'sad', 'angry') DEFAULT 'like',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    
    -- Constraints
    UNIQUE KEY unique_like (user_id, video_id),
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);
