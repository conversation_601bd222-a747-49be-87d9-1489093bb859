// =====================================================
// Home Page Component - مكون الصفحة الرئيسية
// =====================================================

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  IconButton,
  Fab,
  useTheme,
  useMediaQuery,
  Skeleton,
  Alert
} from '@mui/material';
import {
  PlayArrow,
  Explore,
  TrendingUp,
  Star,
  Add,
  VideoLibrary,
  People,
  Analytics,
  Favorite
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { fetchTrendingVideos, fetchFeaturedVideos } from '../store/slices/videoSlice';
import { fetchUserStats } from '../store/slices/userSlice';
import VideoCard from './VideoCard';
import StatsCard from './StatsCard';
import TrendingSection from './TrendingSection';
import FeaturedSection from './FeaturedSection';
import HeroSection from './HeroSection';

const HomePage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const { isAuthenticated, user } = useSelector(state => state.auth);
  const { trendingVideos, featuredVideos, loading, error } = useSelector(state => state.videos);
  const { stats } = useSelector(state => state.user);

  const [activeSection, setActiveSection] = useState('hero');

  useEffect(() => {
    // Load initial data
    dispatch(fetchTrendingVideos({ limit: 12 }));
    dispatch(fetchFeaturedVideos({ limit: 8 }));
    
    if (isAuthenticated) {
      dispatch(fetchUserStats());
    }
  }, [dispatch, isAuthenticated]);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      navigate('/feed');
    } else {
      navigate('/auth/register');
    }
  };

  const handleExplore = () => {
    navigate('/discover');
  };

  const handleCreateVideo = () => {
    if (isAuthenticated) {
      navigate('/create');
    } else {
      navigate('/auth/login');
    }
  };

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.
        </Alert>
      </Container>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <HeroSection 
        onGetStarted={handleGetStarted}
        onExplore={handleExplore}
        isAuthenticated={isAuthenticated}
      />

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Statistics Section */}
        {!isAuthenticated && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Box sx={{ mb: 6 }}>
              <Typography 
                variant="h4" 
                component="h2" 
                align="center" 
                gutterBottom
                sx={{ fontWeight: 'bold', mb: 4 }}
              >
                إحصائيات مذهلة
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    icon={<People sx={{ fontSize: 40, color: 'primary.main' }} />}
                    title="المستخدمين النشطين"
                    value="2.5M+"
                    description="مستخدم نشط يومياً"
                    color="primary"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    icon={<VideoLibrary sx={{ fontSize: 40, color: 'secondary.main' }} />}
                    title="الفيديوهات المنشورة"
                    value="10M+"
                    description="فيديو تم نشره"
                    color="secondary"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    icon={<Analytics sx={{ fontSize: 40, color: 'success.main' }} />}
                    title="المشاهدات اليومية"
                    value="100M+"
                    description="مشاهدة يومياً"
                    color="success"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    icon={<Star sx={{ fontSize: 40, color: 'warning.main' }} />}
                    title="التقييم"
                    value="4.8/5"
                    description="تقييم المستخدمين"
                    color="warning"
                  />
                </Grid>
              </Grid>
            </Box>
          </motion.div>
        )}

        {/* Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Box sx={{ mb: 6 }}>
            <Typography 
              variant="h4" 
              component="h2" 
              align="center" 
              gutterBottom
              sx={{ fontWeight: 'bold', mb: 4 }}
            >
              الميزات الثورية
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6} lg={3}>
                <Card 
                  sx={{ 
                    height: '100%',
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8]
                    }
                  }}
                  data-testid="feature-card"
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Box sx={{ mb: 2 }}>
                      <Chip label="جديد" color="primary" size="small" />
                    </Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                      التوأم الإبداعي
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      نسخة ذكية تتعلم من أسلوبك وتساعدك في إنشاء محتوى مميز
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6} lg={3}>
                <Card 
                  sx={{ 
                    height: '100%',
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8]
                    }
                  }}
                  data-testid="feature-card"
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Box sx={{ mb: 2 }}>
                      <Chip label="شائع" color="secondary" size="small" />
                    </Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                      الرادار الاجتماعي
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      اكتشف المحتوى والأشخاص القريبين منك جغرافياً
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6} lg={3}>
                <Card 
                  sx={{ 
                    height: '100%',
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8]
                    }
                  }}
                  data-testid="feature-card"
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Box sx={{ mb: 2 }}>
                      <Chip label="ثوري" color="success" size="small" />
                    </Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                      توليد فيديو بالصوت
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      أنشئ فيديوهات مذهلة باستخدام الأوامر الصوتية فقط
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6} lg={3}>
                <Card 
                  sx={{ 
                    height: '100%',
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8]
                    }
                  }}
                  data-testid="feature-card"
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Box sx={{ mb: 2 }}>
                      <Chip label="مميز" color="warning" size="small" />
                    </Box>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                      وضع الفن الفوري
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      حول فيديوهاتك إلى أعمال فنية بلمسة واحدة
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </motion.div>

        {/* Trending Videos Section */}
        {trendingVideos.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <TrendingSection 
              videos={trendingVideos}
              loading={loading}
              onVideoClick={(video) => navigate(`/video/${video.id}`)}
            />
          </motion.div>
        )}

        {/* Featured Videos Section */}
        {featuredVideos.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <FeaturedSection 
              videos={featuredVideos}
              loading={loading}
              onVideoClick={(video) => navigate(`/video/${video.id}`)}
            />
          </motion.div>
        )}

        {/* Call to Action Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <Box 
            sx={{ 
              textAlign: 'center',
              py: 8,
              background: `linear-gradient(135deg, ${theme.palette.primary.main}20, ${theme.palette.secondary.main}20)`,
              borderRadius: 4,
              mt: 6
            }}
          >
            <Typography 
              variant="h3" 
              component="h2" 
              gutterBottom
              sx={{ fontWeight: 'bold', mb: 2 }}
            >
              جاهز لتجربة المستقبل؟
            </Typography>
            
            <Typography 
              variant="h6" 
              color="text.secondary" 
              sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}
            >
              انضم إلى ملايين المستخدمين واكتشف عالماً جديداً من الإبداع والتفاعل
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                startIcon={<PlayArrow />}
                onClick={handleGetStarted}
                sx={{ 
                  px: 4, 
                  py: 1.5,
                  fontSize: '1.1rem',
                  borderRadius: 3
                }}
              >
                ابدأ الآن مجاناً
              </Button>
              
              <Button
                variant="outlined"
                size="large"
                startIcon={<Explore />}
                onClick={handleExplore}
                sx={{ 
                  px: 4, 
                  py: 1.5,
                  fontSize: '1.1rem',
                  borderRadius: 3
                }}
              >
                استكشف المحتوى
              </Button>
            </Box>
          </Box>
        </motion.div>
      </Container>

      {/* Floating Action Button */}
      {isAuthenticated && (
        <Fab
          color="primary"
          aria-label="create video"
          onClick={handleCreateVideo}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1000
          }}
        >
          <Add />
        </Fab>
      )}
    </Box>
  );
};

export default HomePage;
