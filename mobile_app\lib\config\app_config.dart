// =====================================================
// App Configuration - TikTok Clone Flutter
// =====================================================

class AppConfig {
  // App Information
  static const String appName = 'TikTok Clone';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:5000/api';
  static const String socketUrl = 'http://localhost:5000';
  static const String wsUrl = 'ws://localhost:5000';
  
  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
  
  // Cache Configuration
  static const int cacheMaxAge = 3600; // 1 hour
  static const int maxCacheSize = 100 * 1024 * 1024; // 100 MB
  
  // File Upload Limits
  static const int maxImageSize = 10 * 1024 * 1024; // 10 MB
  static const int maxVideoSize = 100 * 1024 * 1024; // 100 MB
  static const int maxAudioSize = 20 * 1024 * 1024; // 20 MB
  
  // Video Configuration
  static const int maxVideoDuration = 180; // 3 minutes
  static const int minVideoDuration = 3; // 3 seconds
  static const List<String> supportedVideoFormats = ['mp4', 'mov', 'avi'];
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'gif'];
  
  // Features Flags
  static const bool enableLiveStreaming = true;
  static const bool enableMessaging = true;
  static const bool enableAIFeatures = true;
  static const bool enableAnalytics = true;
  static const bool enablePushNotifications = true;
  static const bool enableLocationServices = true;
  static const bool enableBiometricAuth = true;
  
  // Social Media Integration
  static const String googleClientId = 'your_google_client_id';
  static const String facebookAppId = 'your_facebook_app_id';
  static const String appleClientId = 'your_apple_client_id';
  
  // Firebase Configuration
  static const String firebaseProjectId = 'your_firebase_project_id';
  static const String firebaseApiKey = 'your_firebase_api_key';
  static const String firebaseAppId = 'your_firebase_app_id';
  
  // Analytics
  static const String mixpanelToken = 'your_mixpanel_token';
  static const bool enableCrashlytics = true;
  
  // Development Settings
  static const bool debugMode = true;
  static const bool enableLogging = true;
  static const String logLevel = 'debug';
  
  // Security
  static const bool enableSSLPinning = false; // Enable in production
  static const bool enableJailbreakDetection = false; // Enable in production
  static const bool enableScreenshotBlocking = false; // Enable for sensitive content
  
  // UI Configuration
  static const int animationDuration = 300; // milliseconds
  static const double borderRadius = 12.0;
  static const double elevation = 4.0;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Refresh Intervals
  static const int feedRefreshInterval = 30000; // 30 seconds
  static const int notificationCheckInterval = 60000; // 1 minute
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String cacheKey = 'app_cache';
  
  // Deep Links
  static const String deepLinkScheme = 'tiktokclone';
  static const String universalLinkDomain = 'tiktokclone.com';
  
  // Environment-specific configurations
  static bool get isProduction => const bool.fromEnvironment('dart.vm.product');
  static bool get isDevelopment => !isProduction;
  
  // Get environment-specific base URL
  static String get apiBaseUrl {
    if (isProduction) {
      return 'https://api.tiktokclone.com/api';
    } else {
      return baseUrl;
    }
  }
  
  // Get environment-specific socket URL
  static String get socketBaseUrl {
    if (isProduction) {
      return 'https://api.tiktokclone.com';
    } else {
      return socketUrl;
    }
  }
}
