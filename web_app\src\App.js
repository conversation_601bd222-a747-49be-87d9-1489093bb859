// =====================================================
// التطبيق الرئيسي - TikTok Clone Web
// =====================================================

import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, GlobalStyles } from '@mui/material';
import { Toaster } from 'react-hot-toast';

// Store
import { store } from './store/store';

// Components
import LoadingSpinner from './components/common/LoadingSpinner';
import ErrorBoundary from './components/common/ErrorBoundary';
import Layout from './components/layout/Layout';

// Pages
import HomePage from './pages/HomePage';
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import ProfilePage from './pages/ProfilePage';
import VideoPage from './pages/VideoPage';
import SearchPage from './pages/SearchPage';
import LivePage from './pages/LivePage';
import MessagesPage from './pages/MessagesPage';
import SettingsPage from './pages/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';

// Hooks
import { useAuth } from './hooks/useAuth';
import { useTheme as useAppTheme } from './hooks/useTheme';

// Services
import { initializeApp } from './services/appService';

// Styles
import './styles/global.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Create Material-UI theme
const createAppTheme = (isDark) => createTheme({
  direction: 'rtl',
  palette: {
    mode: isDark ? 'dark' : 'light',
    primary: {
      main: '#FF0050',
      light: '#FF4081',
      dark: '#C51162',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#25F4EE',
      light: '#64FFDA',
      dark: '#00BCD4',
      contrastText: '#000000',
    },
    background: {
      default: isDark ? '#000000' : '#FFFFFF',
      paper: isDark ? '#161823' : '#F8F8F8',
    },
    text: {
      primary: isDark ? '#FFFFFF' : '#161823',
      secondary: isDark ? '#A1A2A7' : '#69707D',
    },
  },
  typography: {
    fontFamily: '"Cairo", "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 12,
          padding: '10px 20px',
          fontSize: '1rem',
          fontWeight: 600,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
});

// Global styles
const globalStyles = (
  <GlobalStyles
    styles={{
      '*': {
        margin: 0,
        padding: 0,
        boxSizing: 'border-box',
      },
      html: {
        height: '100%',
        scrollBehavior: 'smooth',
      },
      body: {
        height: '100%',
        overflowX: 'hidden',
      },
      '#root': {
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      },
      // Custom scrollbar
      '::-webkit-scrollbar': {
        width: '8px',
      },
      '::-webkit-scrollbar-track': {
        background: 'rgba(0, 0, 0, 0.1)',
      },
      '::-webkit-scrollbar-thumb': {
        background: '#FF0050',
        borderRadius: '4px',
      },
      '::-webkit-scrollbar-thumb:hover': {
        background: '#FE2C55',
      },
    }}
  />
);

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return children;
};

// Public Route Component (redirect if authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }
  
  return children;
};

// Main App Component
function App() {
  const { isDarkMode } = useAppTheme();
  const theme = createAppTheme(isDarkMode);

  useEffect(() => {
    // Initialize app
    initializeApp();
  }, []);

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            {globalStyles}
            
            <Router>
              <div className="app">
                <Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    {/* Public Routes */}
                    <Route 
                      path="/login" 
                      element={
                        <PublicRoute>
                          <LoginPage />
                        </PublicRoute>
                      } 
                    />
                    <Route 
                      path="/register" 
                      element={
                        <PublicRoute>
                          <RegisterPage />
                        </PublicRoute>
                      } 
                    />
                    
                    {/* Protected Routes */}
                    <Route 
                      path="/" 
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <HomePage />
                          </Layout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/profile/:username" 
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <ProfilePage />
                          </Layout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/video/:id" 
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <VideoPage />
                          </Layout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/search" 
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <SearchPage />
                          </Layout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/live" 
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <LivePage />
                          </Layout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/messages" 
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <MessagesPage />
                          </Layout>
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/settings" 
                      element={
                        <ProtectedRoute>
                          <Layout>
                            <SettingsPage />
                          </Layout>
                        </ProtectedRoute>
                      } 
                    />
                    
                    {/* 404 Page */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </Suspense>
                
                {/* Toast Notifications */}
                <Toaster
                  position="top-center"
                  reverseOrder={false}
                  gutter={8}
                  containerClassName=""
                  containerStyle={{}}
                  toastOptions={{
                    className: '',
                    duration: 4000,
                    style: {
                      background: isDarkMode ? '#161823' : '#FFFFFF',
                      color: isDarkMode ? '#FFFFFF' : '#161823',
                      borderRadius: '12px',
                      padding: '16px',
                      fontSize: '14px',
                      fontFamily: 'Cairo, sans-serif',
                    },
                    success: {
                      iconTheme: {
                        primary: '#00D9FF',
                        secondary: '#FFFFFF',
                      },
                    },
                    error: {
                      iconTheme: {
                        primary: '#FF3040',
                        secondary: '#FFFFFF',
                      },
                    },
                  }}
                />
              </div>
            </Router>
          </ThemeProvider>
        </QueryClientProvider>
      </Provider>
    </ErrorBoundary>
  );
}

export default App;
