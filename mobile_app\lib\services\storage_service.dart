// =====================================================
// خدمة التخزين المحلي
// =====================================================

import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../constants/app_constants.dart';

class StorageService extends GetxService {
  static StorageService get instance => Get.find<StorageService>();
  
  late SharedPreferences _prefs;
  late Box _secureBox;
  late Box _cacheBox;
  late Box _settingsBox;

  // تهيئة الخدمة
  Future<StorageService> init() async {
    try {
      // تهيئة SharedPreferences
      _prefs = await SharedPreferences.getInstance();
      
      // تهيئة Hive boxes
      _secureBox = await Hive.openBox('secure_storage');
      _cacheBox = await Hive.openBox('cache_storage');
      _settingsBox = await Hive.openBox('settings_storage');
      
      print('✅ تم تهيئة خدمة التخزين بنجاح');
      return this;
      
    } catch (e) {
      print('❌ خطأ في تهيئة خدمة التخزين: $e');
      rethrow;
    }
  }

  // =====================================================
  // العمليات الأساسية للنصوص
  // =====================================================
  
  // حفظ نص
  Future<bool> setString(String key, String value) async {
    try {
      return await _prefs.setString(key, value);
    } catch (e) {
      print('خطأ في حفظ النص: $e');
      return false;
    }
  }
  
  // قراءة نص
  String? getString(String key, {String? defaultValue}) {
    try {
      return _prefs.getString(key) ?? defaultValue;
    } catch (e) {
      print('خطأ في قراءة النص: $e');
      return defaultValue;
    }
  }
  
  // حفظ رقم صحيح
  Future<bool> setInt(String key, int value) async {
    try {
      return await _prefs.setInt(key, value);
    } catch (e) {
      print('خطأ في حفظ الرقم: $e');
      return false;
    }
  }
  
  // قراءة رقم صحيح
  int? getInt(String key, {int? defaultValue}) {
    try {
      return _prefs.getInt(key) ?? defaultValue;
    } catch (e) {
      print('خطأ في قراءة الرقم: $e');
      return defaultValue;
    }
  }
  
  // حفظ رقم عشري
  Future<bool> setDouble(String key, double value) async {
    try {
      return await _prefs.setDouble(key, value);
    } catch (e) {
      print('خطأ في حفظ الرقم العشري: $e');
      return false;
    }
  }
  
  // قراءة رقم عشري
  double? getDouble(String key, {double? defaultValue}) {
    try {
      return _prefs.getDouble(key) ?? defaultValue;
    } catch (e) {
      print('خطأ في قراءة الرقم العشري: $e');
      return defaultValue;
    }
  }
  
  // حفظ قيمة منطقية
  Future<bool> setBool(String key, bool value) async {
    try {
      return await _prefs.setBool(key, value);
    } catch (e) {
      print('خطأ في حفظ القيمة المنطقية: $e');
      return false;
    }
  }
  
  // قراءة قيمة منطقية
  bool? getBool(String key, {bool? defaultValue}) {
    try {
      return _prefs.getBool(key) ?? defaultValue;
    } catch (e) {
      print('خطأ في قراءة القيمة المنطقية: $e');
      return defaultValue;
    }
  }
  
  // حفظ قائمة نصوص
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      return await _prefs.setStringList(key, value);
    } catch (e) {
      print('خطأ في حفظ قائمة النصوص: $e');
      return false;
    }
  }
  
  // قراءة قائمة نصوص
  List<String>? getStringList(String key, {List<String>? defaultValue}) {
    try {
      return _prefs.getStringList(key) ?? defaultValue;
    } catch (e) {
      print('خطأ في قراءة قائمة النصوص: $e');
      return defaultValue;
    }
  }

  // =====================================================
  // العمليات المتقدمة للكائنات
  // =====================================================
  
  // حفظ كائن JSON
  Future<bool> setObject(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      print('خطأ في حفظ الكائن: $e');
      return false;
    }
  }
  
  // قراءة كائن JSON
  Map<String, dynamic>? getObject(String key) {
    try {
      final jsonString = getString(key);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      print('خطأ في قراءة الكائن: $e');
      return null;
    }
  }
  
  // حفظ قائمة كائنات
  Future<bool> setObjectList(String key, List<Map<String, dynamic>> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      print('خطأ في حفظ قائمة الكائنات: $e');
      return false;
    }
  }
  
  // قراءة قائمة كائنات
  List<Map<String, dynamic>>? getObjectList(String key) {
    try {
      final jsonString = getString(key);
      if (jsonString != null) {
        final List<dynamic> jsonList = jsonDecode(jsonString);
        return jsonList.cast<Map<String, dynamic>>();
      }
      return null;
    } catch (e) {
      print('خطأ في قراءة قائمة الكائنات: $e');
      return null;
    }
  }

  // =====================================================
  // التخزين الآمن (Hive)
  // =====================================================
  
  // حفظ في التخزين الآمن
  Future<void> setSecure(String key, dynamic value) async {
    try {
      await _secureBox.put(key, value);
    } catch (e) {
      print('خطأ في حفظ البيانات الآمنة: $e');
    }
  }
  
  // قراءة من التخزين الآمن
  T? getSecure<T>(String key, {T? defaultValue}) {
    try {
      return _secureBox.get(key, defaultValue: defaultValue) as T?;
    } catch (e) {
      print('خطأ في قراءة البيانات الآمنة: $e');
      return defaultValue;
    }
  }
  
  // حذف من التخزين الآمن
  Future<void> deleteSecure(String key) async {
    try {
      await _secureBox.delete(key);
    } catch (e) {
      print('خطأ في حذف البيانات الآمنة: $e');
    }
  }

  // =====================================================
  // التخزين المؤقت (Cache)
  // =====================================================
  
  // حفظ في التخزين المؤقت مع انتهاء صلاحية
  Future<void> setCache(String key, dynamic value, {Duration? expiry}) async {
    try {
      final cacheData = {
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiry': expiry?.inMilliseconds,
      };
      await _cacheBox.put(key, cacheData);
    } catch (e) {
      print('خطأ في حفظ التخزين المؤقت: $e');
    }
  }
  
  // قراءة من التخزين المؤقت
  T? getCache<T>(String key) {
    try {
      final cacheData = _cacheBox.get(key) as Map<dynamic, dynamic>?;
      if (cacheData == null) return null;
      
      final timestamp = cacheData['timestamp'] as int;
      final expiry = cacheData['expiry'] as int?;
      
      // التحقق من انتهاء الصلاحية
      if (expiry != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp > expiry) {
          _cacheBox.delete(key);
          return null;
        }
      }
      
      return cacheData['value'] as T?;
    } catch (e) {
      print('خطأ في قراءة التخزين المؤقت: $e');
      return null;
    }
  }
  
  // مسح التخزين المؤقت المنتهي الصلاحية
  Future<void> clearExpiredCache() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final keysToDelete = <dynamic>[];
      
      for (final key in _cacheBox.keys) {
        final cacheData = _cacheBox.get(key) as Map<dynamic, dynamic>?;
        if (cacheData != null) {
          final timestamp = cacheData['timestamp'] as int;
          final expiry = cacheData['expiry'] as int?;
          
          if (expiry != null && now - timestamp > expiry) {
            keysToDelete.add(key);
          }
        }
      }
      
      for (final key in keysToDelete) {
        await _cacheBox.delete(key);
      }
      
      print('تم مسح ${keysToDelete.length} عنصر منتهي الصلاحية');
    } catch (e) {
      print('خطأ في مسح التخزين المؤقت: $e');
    }
  }

  // =====================================================
  // الإعدادات
  // =====================================================
  
  // حفظ إعداد
  Future<void> setSetting(String key, dynamic value) async {
    try {
      await _settingsBox.put(key, value);
    } catch (e) {
      print('خطأ في حفظ الإعداد: $e');
    }
  }
  
  // قراءة إعداد
  T? getSetting<T>(String key, {T? defaultValue}) {
    try {
      return _settingsBox.get(key, defaultValue: defaultValue) as T?;
    } catch (e) {
      print('خطأ في قراءة الإعداد: $e');
      return defaultValue;
    }
  }

  // =====================================================
  // عمليات التنظيف
  // =====================================================
  
  // حذف مفتاح محدد
  Future<bool> remove(String key) async {
    try {
      return await _prefs.remove(key);
    } catch (e) {
      print('خطأ في حذف المفتاح: $e');
      return false;
    }
  }
  
  // مسح جميع البيانات
  Future<bool> clear() async {
    try {
      await _prefs.clear();
      await _secureBox.clear();
      await _cacheBox.clear();
      await _settingsBox.clear();
      return true;
    } catch (e) {
      print('خطأ في مسح البيانات: $e');
      return false;
    }
  }
  
  // مسح بيانات المستخدم فقط
  Future<void> clearUserData() async {
    try {
      await remove(AppConstants.userTokenKey);
      await remove(AppConstants.refreshTokenKey);
      await remove(AppConstants.userDataKey);
      await deleteSecure(AppConstants.userTokenKey);
      await deleteSecure(AppConstants.refreshTokenKey);
      print('تم مسح بيانات المستخدم');
    } catch (e) {
      print('خطأ في مسح بيانات المستخدم: $e');
    }
  }
  
  // التحقق من وجود مفتاح
  bool hasKey(String key) {
    try {
      return _prefs.containsKey(key);
    } catch (e) {
      print('خطأ في التحقق من المفتاح: $e');
      return false;
    }
  }
  
  // الحصول على جميع المفاتيح
  Set<String> getAllKeys() {
    try {
      return _prefs.getKeys();
    } catch (e) {
      print('خطأ في الحصول على المفاتيح: $e');
      return <String>{};
    }
  }
}
