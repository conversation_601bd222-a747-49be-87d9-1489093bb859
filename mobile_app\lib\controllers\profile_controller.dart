// =====================================================
// Profile Controller - متحكم الملف الشخصي
// =====================================================

import 'dart:io';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../models/user_model.dart';
import '../models/video_model.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';

class ProfileController extends GetxController with GetTickerProviderStateMixin {
  final ApiService _apiService = Get.find<ApiService>();
  final StorageService _storageService = Get.find<StorageService>();
  final ImagePicker _imagePicker = ImagePicker();

  // حالة التحميل
  final RxBool _isLoading = false.obs;
  final RxBool _isUpdating = false.obs;
  final RxBool _isFollowing = false.obs;

  // بيانات المستخدم
  final Rx<UserModel?> _user = Rx<UserModel?>(null);
  final RxList<VideoModel> _userVideos = <VideoModel>[].obs;
  final RxList<VideoModel> _likedVideos = <VideoModel>[].obs;
  final RxList<UserModel> _followers = <UserModel>[].obs;
  final RxList<UserModel> _following = <UserModel>[].obs;

  // إعدادات العرض
  final RxInt _currentTabIndex = 0.obs;
  final RxString _selectedFilter = 'all'.obs;
  final RxBool _isPrivateProfile = false.obs;
  final RxBool _isOwnProfile = false.obs;

  // معلومات الإحصائيات
  final RxInt _totalVideos = 0.obs;
  final RxInt _totalLikes = 0.obs;
  final RxInt _totalViews = 0.obs;
  final RxInt _followersCount = 0.obs;
  final RxInt _followingCount = 0.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isUpdating => _isUpdating.value;
  bool get isFollowing => _isFollowing.value;
  UserModel? get user => _user.value;
  List<VideoModel> get userVideos => _userVideos;
  List<VideoModel> get likedVideos => _likedVideos;
  List<UserModel> get followers => _followers;
  List<UserModel> get following => _following;
  int get currentTabIndex => _currentTabIndex.value;
  String get selectedFilter => _selectedFilter.value;
  bool get isPrivateProfile => _isPrivateProfile.value;
  bool get isOwnProfile => _isOwnProfile.value;
  int get totalVideos => _totalVideos.value;
  int get totalLikes => _totalLikes.value;
  int get totalViews => _totalViews.value;
  int get followersCount => _followersCount.value;
  int get followingCount => _followingCount.value;

  @override
  void onInit() {
    super.onInit();
    _initializeProfile();
  }

  void _initializeProfile() {
    // تحميل بيانات المستخدم الحالي
    final currentUser = _storageService.getUserData();
    if (currentUser != null) {
      _user.value = currentUser;
      _isOwnProfile.value = true;
      loadUserProfile(currentUser.id);
    }
  }

  // ==================== Profile Loading ====================

  // تحميل ملف المستخدم
  Future<void> loadUserProfile(String userId) async {
    try {
      _isLoading.value = true;
      
      // التحقق من كون الملف للمستخدم الحالي
      final currentUser = _storageService.getUserData();
      _isOwnProfile.value = currentUser?.id == userId;

      // تحميل بيانات المستخدم
      final response = await _apiService.getUserProfile(userId);
      
      if (response.success && response.data != null) {
        _user.value = response.data!;
        _isPrivateProfile.value = response.data!.isPrivate;
        
        // تحديث الإحصائيات
        _updateStatistics();
        
        // تحميل فيديوهات المستخدم
        await loadUserVideos();
        
        // تحميل المتابعين والمتابَعين إذا كان الملف عام أو للمستخدم الحالي
        if (!_isPrivateProfile.value || _isOwnProfile.value) {
          await loadFollowers();
          await loadFollowing();
        }
      } else {
        Get.snackbar('خطأ', response.error ?? 'فشل في تحميل الملف الشخصي');
      }
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء تحميل الملف الشخصي');
      print('Error loading profile: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  // تحميل فيديوهات المستخدم
  Future<void> loadUserVideos() async {
    if (_user.value == null) return;

    try {
      final response = await _apiService.getVideos(userId: _user.value!.id);
      
      if (response.success && response.data != null) {
        _userVideos.value = response.data!;
        _totalVideos.value = response.data!.length;
        
        // حساب إجمالي المشاهدات والإعجابات
        _calculateTotalStats();
      }
    } catch (e) {
      print('Error loading user videos: $e');
    }
  }

  // تحميل الفيديوهات المعجب بها
  Future<void> loadLikedVideos() async {
    if (_user.value == null || !_isOwnProfile.value) return;

    try {
      // تحميل الفيديوهات المعجب بها من الخادم
      // final response = await _apiService.getLikedVideos();
      
      // مؤقتاً: تحميل من التخزين المحلي
      final favorites = _storageService.getFavorites();
      final likedVideosList = <VideoModel>[];
      
      for (String videoId in favorites) {
        // يمكن تحميل تفاصيل كل فيديو من الخادم
        // final videoResponse = await _apiService.getVideo(videoId);
        // if (videoResponse.success) {
        //   likedVideosList.add(videoResponse.data!);
        // }
      }
      
      _likedVideos.value = likedVideosList;
    } catch (e) {
      print('Error loading liked videos: $e');
    }
  }

  // تحميل المتابعين
  Future<void> loadFollowers() async {
    if (_user.value == null) return;

    try {
      final response = await _apiService.getFollowers(_user.value!.id);
      
      if (response.success && response.data != null) {
        _followers.value = response.data!;
        _followersCount.value = response.data!.length;
      }
    } catch (e) {
      print('Error loading followers: $e');
    }
  }

  // تحميل المتابَعين
  Future<void> loadFollowing() async {
    if (_user.value == null) return;

    try {
      final response = await _apiService.getFollowing(_user.value!.id);
      
      if (response.success && response.data != null) {
        _following.value = response.data!;
        _followingCount.value = response.data!.length;
      }
    } catch (e) {
      print('Error loading following: $e');
    }
  }

  // ==================== Profile Actions ====================

  // متابعة/إلغاء متابعة المستخدم
  Future<void> toggleFollow() async {
    if (_user.value == null || _isOwnProfile.value) return;

    try {
      _isFollowing.value = true;
      
      final response = _user.value!.isFollowing 
          ? await _apiService.unfollowUser(_user.value!.id)
          : await _apiService.followUser(_user.value!.id);
      
      if (response.success) {
        _user.value = _user.value!.copyWith(
          isFollowing: !_user.value!.isFollowing,
          followersCount: _user.value!.isFollowing 
              ? _user.value!.followersCount - 1
              : _user.value!.followersCount + 1,
        );
        
        Get.snackbar(
          'نجح',
          _user.value!.isFollowing ? 'تم إلغاء المتابعة' : 'تمت المتابعة',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar('خطأ', response.error ?? 'فشل في تحديث المتابعة');
      }
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء تحديث المتابعة');
      print('Error toggling follow: $e');
    } finally {
      _isFollowing.value = false;
    }
  }

  // تحديث الصورة الشخصية
  Future<void> updateAvatar() async {
    if (!_isOwnProfile.value) return;

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        _isUpdating.value = true;
        
        // رفع الصورة للخادم
        final file = File(image.path);
        // final response = await _apiService.updateAvatar(file);
        
        // مؤقتاً: تحديث محلي
        _user.value = _user.value!.copyWith(
          avatarUrl: image.path, // في الواقع سيكون URL من الخادم
        );
        
        // حفظ البيانات المحدثة
        await _storageService.saveUserData(_user.value!);
        
        Get.snackbar('نجح', 'تم تحديث الصورة الشخصية');
      }
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث الصورة الشخصية');
      print('Error updating avatar: $e');
    } finally {
      _isUpdating.value = false;
    }
  }

  // تحديث صورة الغلاف
  Future<void> updateCover() async {
    if (!_isOwnProfile.value) return;

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        _isUpdating.value = true;
        
        // رفع الصورة للخادم
        final file = File(image.path);
        // final response = await _apiService.updateCover(file);
        
        // مؤقتاً: تحديث محلي
        _user.value = _user.value!.copyWith(
          coverUrl: image.path, // في الواقع سيكون URL من الخادم
        );
        
        // حفظ البيانات المحدثة
        await _storageService.saveUserData(_user.value!);
        
        Get.snackbar('نجح', 'تم تحديث صورة الغلاف');
      }
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث صورة الغلاف');
      print('Error updating cover: $e');
    } finally {
      _isUpdating.value = false;
    }
  }

  // ==================== UI Controls ====================

  // تغيير التبويب الحالي
  void changeTab(int index) {
    _currentTabIndex.value = index;
    
    // تحميل البيانات حسب التبويب
    switch (index) {
      case 0: // الفيديوهات
        if (_userVideos.isEmpty) {
          loadUserVideos();
        }
        break;
      case 1: // المعجب بها
        if (_likedVideos.isEmpty && _isOwnProfile.value) {
          loadLikedVideos();
        }
        break;
      case 2: // المتابعين
        if (_followers.isEmpty) {
          loadFollowers();
        }
        break;
      case 3: // المتابَعين
        if (_following.isEmpty) {
          loadFollowing();
        }
        break;
    }
  }

  // تغيير الفلتر
  void changeFilter(String filter) {
    _selectedFilter.value = filter;
    // تطبيق الفلتر على الفيديوهات
    _applyFilter();
  }

  // تطبيق الفلتر
  void _applyFilter() {
    // تنفيذ منطق الفلترة حسب النوع المحدد
    switch (_selectedFilter.value) {
      case 'all':
        // عرض جميع الفيديوهات
        break;
      case 'popular':
        // ترتيب حسب الشعبية
        _userVideos.sort((a, b) => b.likesCount.compareTo(a.likesCount));
        break;
      case 'recent':
        // ترتيب حسب التاريخ
        _userVideos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }
  }

  // ==================== Statistics ====================

  // تحديث الإحصائيات
  void _updateStatistics() {
    if (_user.value != null) {
      _followersCount.value = _user.value!.followersCount;
      _followingCount.value = _user.value!.followingCount;
    }
  }

  // حساب إجمالي الإحصائيات
  void _calculateTotalStats() {
    int totalLikes = 0;
    int totalViews = 0;
    
    for (VideoModel video in _userVideos) {
      totalLikes += video.likesCount;
      totalViews += video.viewsCount;
    }
    
    _totalLikes.value = totalLikes;
    _totalViews.value = totalViews;
  }

  // ==================== Navigation ====================

  // الانتقال لتحرير الملف الشخصي
  void goToEditProfile() {
    if (_isOwnProfile.value) {
      Get.toNamed('/edit-profile', arguments: _user.value);
    }
  }

  // الانتقال لصفحة الإعدادات
  void goToSettings() {
    if (_isOwnProfile.value) {
      Get.toNamed('/settings');
    }
  }

  // الانتقال لصفحة المتابعين
  void goToFollowers() {
    Get.toNamed('/followers', arguments: {
      'userId': _user.value?.id,
      'username': _user.value?.username,
    });
  }

  // الانتقال لصفحة المتابَعين
  void goToFollowing() {
    Get.toNamed('/following', arguments: {
      'userId': _user.value?.id,
      'username': _user.value?.username,
    });
  }

  // ==================== Refresh ====================

  // تحديث البيانات
  Future<void> refreshProfile() async {
    if (_user.value != null) {
      await loadUserProfile(_user.value!.id);
    }
  }

  // تحديث التبويب الحالي
  Future<void> refreshCurrentTab() async {
    switch (_currentTabIndex.value) {
      case 0:
        await loadUserVideos();
        break;
      case 1:
        await loadLikedVideos();
        break;
      case 2:
        await loadFollowers();
        break;
      case 3:
        await loadFollowing();
        break;
    }
  }
}
