// =====================================================
// Auth Routes - مسارات المصادقة
// =====================================================

const express = require('express');
const { body } = require('express-validator');
const authController = require('../controllers/authController');
const auth = require('../middleware/auth');
const rateLimit = require('../middleware/rateLimit');

const router = express.Router();

// معدل محدود للمصادقة
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'تم تجاوز عدد محاولات تسجيل الدخول المسموحة'
});

// تسجيل مستخدم جديد
router.post('/register', [
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('اسم المستخدم يجب أن يكون بين 3 و 30 حرف')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('اسم المستخدم يجب أن يحتوي على أحرف وأرقام و _ فقط'),
  
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم'),
  
  body('fullName')
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون بين 2 و 100 حرف')
    .trim(),
  
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('رقم الهاتف غير صحيح')
], authController.register);

// تسجيل الدخول
router.post('/login', authLimiter, [
  body('email')
    .notEmpty()
    .withMessage('البريد الإلكتروني أو اسم المستخدم مطلوب'),
  
  body('password')
    .notEmpty()
    .withMessage('كلمة المرور مطلوبة')
], authController.login);

// تحديث الرمز المميز
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('رمز التحديث مطلوب')
], authController.refreshToken);

// تسجيل الخروج
router.post('/logout', auth, authController.logout);

// تسجيل الخروج من جميع الأجهزة
router.post('/logout-all', auth, authController.logoutAll);

// نسيان كلمة المرور
router.post('/forgot-password', authLimiter, [
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail()
], authController.forgotPassword);

// إعادة تعيين كلمة المرور
router.post('/reset-password', [
  body('token')
    .notEmpty()
    .withMessage('رمز إعادة التعيين مطلوب'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم')
], authController.resetPassword);

// تغيير كلمة المرور
router.post('/change-password', auth, [
  body('currentPassword')
    .notEmpty()
    .withMessage('كلمة المرور الحالية مطلوبة'),
  
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم')
], authController.changePassword);

// التحقق من البريد الإلكتروني
router.get('/verify-email/:token', authController.verifyEmail);

// إعادة إرسال بريد التحقق
router.post('/resend-verification', auth, authController.sendVerificationEmail);

// الحصول على المستخدم الحالي
router.get('/me', auth, authController.getMe);

// تسجيل الدخول بـ Google
router.post('/google', [
  body('token')
    .notEmpty()
    .withMessage('رمز Google مطلوب')
], async (req, res) => {
  try {
    // هنا يتم التحقق من رمز Google وإنشاء/تسجيل دخول المستخدم
    res.json({ message: 'تسجيل الدخول بـ Google قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تسجيل الدخول بـ Google' });
  }
});

// تسجيل الدخول بـ Facebook
router.post('/facebook', [
  body('token')
    .notEmpty()
    .withMessage('رمز Facebook مطلوب')
], async (req, res) => {
  try {
    // هنا يتم التحقق من رمز Facebook وإنشاء/تسجيل دخول المستخدم
    res.json({ message: 'تسجيل الدخول بـ Facebook قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تسجيل الدخول بـ Facebook' });
  }
});

// تسجيل الدخول بـ Apple
router.post('/apple', [
  body('token')
    .notEmpty()
    .withMessage('رمز Apple مطلوب')
], async (req, res) => {
  try {
    // هنا يتم التحقق من رمز Apple وإنشاء/تسجيل دخول المستخدم
    res.json({ message: 'تسجيل الدخول بـ Apple قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تسجيل الدخول بـ Apple' });
  }
});

// تفعيل المصادقة الثنائية
router.post('/2fa/enable', auth, async (req, res) => {
  try {
    // هنا يتم تفعيل المصادقة الثنائية
    res.json({ message: 'المصادقة الثنائية قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تفعيل المصادقة الثنائية' });
  }
});

// إلغاء تفعيل المصادقة الثنائية
router.post('/2fa/disable', auth, async (req, res) => {
  try {
    // هنا يتم إلغاء تفعيل المصادقة الثنائية
    res.json({ message: 'إلغاء المصادقة الثنائية قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في إلغاء المصادقة الثنائية' });
  }
});

// التحقق من المصادقة الثنائية
router.post('/2fa/verify', [
  body('code')
    .isLength({ min: 6, max: 6 })
    .withMessage('رمز التحقق يجب أن يكون 6 أرقام')
    .isNumeric()
    .withMessage('رمز التحقق يجب أن يحتوي على أرقام فقط')
], async (req, res) => {
  try {
    // هنا يتم التحقق من رمز المصادقة الثنائية
    res.json({ message: 'التحقق من المصادقة الثنائية قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في التحقق من المصادقة الثنائية' });
  }
});

// تسجيل رمز الجهاز للإشعارات
router.post('/device-token', auth, [
  body('token')
    .notEmpty()
    .withMessage('رمز الجهاز مطلوب'),
  
  body('platform')
    .isIn(['ios', 'android', 'web'])
    .withMessage('منصة الجهاز غير صحيحة')
], async (req, res) => {
  try {
    const { token, platform } = req.body;
    const user = req.user;
    
    await user.addDeviceToken(token, platform);
    
    res.json({ 
      success: true,
      message: 'تم تسجيل رمز الجهاز بنجاح' 
    });
  } catch (error) {
    console.error('Device token registration error:', error);
    res.status(500).json({ message: 'خطأ في تسجيل رمز الجهاز' });
  }
});

// إزالة رمز الجهاز
router.delete('/device-token', auth, [
  body('token')
    .notEmpty()
    .withMessage('رمز الجهاز مطلوب')
], async (req, res) => {
  try {
    const { token } = req.body;
    const user = req.user;
    
    user.deviceTokens = user.deviceTokens.filter(dt => dt.token !== token);
    await user.save();
    
    res.json({ 
      success: true,
      message: 'تم إزالة رمز الجهاز بنجاح' 
    });
  } catch (error) {
    console.error('Device token removal error:', error);
    res.status(500).json({ message: 'خطأ في إزالة رمز الجهاز' });
  }
});

module.exports = router;
