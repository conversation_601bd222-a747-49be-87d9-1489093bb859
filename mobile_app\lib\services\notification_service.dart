// =====================================================
// Notification Service - خدمة الإشعارات
// =====================================================

import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/constants.dart';
import 'storage_service.dart';

class NotificationService extends GetxService {
  late FirebaseMessaging _firebaseMessaging;
  late FlutterLocalNotificationsPlugin _localNotifications;
  final StorageService _storageService = Get.find<StorageService>();

  // حالة الإشعارات
  final RxBool _notificationsEnabled = false.obs;
  final RxString _fcmToken = ''.obs;
  final RxList<Map<String, dynamic>> _notifications = <Map<String, dynamic>>[].obs;

  // Getters
  bool get notificationsEnabled => _notificationsEnabled.value;
  String get fcmToken => _fcmToken.value;
  List<Map<String, dynamic>> get notifications => _notifications;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    try {
      // تهيئة Firebase Messaging
      _firebaseMessaging = FirebaseMessaging.instance;
      
      // تهيئة Local Notifications
      _localNotifications = FlutterLocalNotificationsPlugin();
      
      // طلب الأذونات
      await _requestPermissions();
      
      // إعداد Local Notifications
      await _setupLocalNotifications();
      
      // إعداد Firebase Messaging
      await _setupFirebaseMessaging();
      
      // تحميل الإشعارات المحفوظة
      _loadSavedNotifications();
      
      print('✅ Notification Service initialized successfully');
    } catch (e) {
      print('❌ Error initializing notifications: $e');
    }
  }

  // ==================== Permissions ====================

  Future<void> _requestPermissions() async {
    try {
      // طلب إذن الإشعارات من Firebase
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        _notificationsEnabled.value = true;
        print('✅ Firebase notifications permission granted');
      } else {
        print('❌ Firebase notifications permission denied');
      }

      // طلب إذن الإشعارات المحلية (Android)
      if (Platform.isAndroid) {
        final status = await Permission.notification.request();
        if (status.isGranted) {
          print('✅ Local notifications permission granted');
        }
      }
    } catch (e) {
      print('❌ Error requesting permissions: $e');
    }
  }

  // ==================== Local Notifications Setup ====================

  Future<void> _setupLocalNotifications() async {
    try {
      // إعدادات Android
      const AndroidInitializationSettings androidSettings = 
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const DarwinInitializationSettings iosSettings = 
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      // إعدادات التهيئة
      const InitializationSettings initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // تهيئة المكون الإضافي
      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      print('✅ Local notifications setup completed');
    } catch (e) {
      print('❌ Error setting up local notifications: $e');
    }
  }

  // ==================== Firebase Messaging Setup ====================

  Future<void> _setupFirebaseMessaging() async {
    try {
      // الحصول على FCM Token
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        _fcmToken.value = token;
        print('📱 FCM Token: $token');
        
        // حفظ الرمز محلياً
        await _storageService.saveToCache('fcm_token', token);
      }

      // الاستماع لتحديثات الرمز
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken.value = newToken;
        _storageService.saveToCache('fcm_token', newToken);
        print('🔄 FCM Token refreshed: $newToken');
      });

      // معالجة الرسائل في المقدمة
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // معالجة الرسائل عند النقر (التطبيق في الخلفية)
      FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

      // معالجة الرسائل عند فتح التطبيق من الإشعار
      RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleBackgroundMessage(initialMessage);
      }

      print('✅ Firebase messaging setup completed');
    } catch (e) {
      print('❌ Error setting up Firebase messaging: $e');
    }
  }

  // ==================== Message Handlers ====================

  // معالجة الرسائل في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    print('📨 Foreground message received: ${message.messageId}');
    
    // عرض إشعار محلي
    _showLocalNotification(
      title: message.notification?.title ?? 'إشعار جديد',
      body: message.notification?.body ?? '',
      data: message.data,
    );

    // إضافة الإشعار للقائمة
    _addNotificationToList(message);
  }

  // معالجة الرسائل في الخلفية
  void _handleBackgroundMessage(RemoteMessage message) {
    print('📨 Background message opened: ${message.messageId}');
    
    // معالجة البيانات والتنقل
    _handleNotificationData(message.data);
    
    // إضافة الإشعار للقائمة
    _addNotificationToList(message);
  }

  // معالجة النقر على الإشعار المحلي
  void _onNotificationTapped(NotificationResponse response) {
    print('👆 Local notification tapped: ${response.id}');
    
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      _handleNotificationData(data);
    }
  }

  // ==================== Local Notification Display ====================

  Future<void> _showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      // إعدادات Android
      AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        AppConstants.notificationChannelId,
        AppConstants.notificationChannelName,
        channelDescription: AppConstants.notificationChannelDescription,
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
        largeIcon: imageUrl != null ? NetworkAndroidBitmap(imageUrl) : null,
        styleInformation: imageUrl != null 
            ? BigPictureStyleInformation(
                NetworkAndroidBitmap(imageUrl),
                contentTitle: title,
                summaryText: body,
              )
            : BigTextStyleInformation(body),
      );

      // إعدادات iOS
      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      // إعدادات الإشعار
      NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // عرض الإشعار
      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        title,
        body,
        notificationDetails,
        payload: data != null ? jsonEncode(data) : null,
      );
    } catch (e) {
      print('❌ Error showing local notification: $e');
    }
  }

  // ==================== Notification Management ====================

  // إضافة إشعار للقائمة
  void _addNotificationToList(RemoteMessage message) {
    final notification = {
      'id': message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      'title': message.notification?.title ?? 'إشعار جديد',
      'body': message.notification?.body ?? '',
      'data': message.data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'read': false,
      'imageUrl': message.notification?.android?.imageUrl ?? 
                  message.notification?.apple?.imageUrl,
    };

    _notifications.insert(0, notification);
    
    // الاحتفاظ بآخر 100 إشعار فقط
    if (_notifications.length > 100) {
      _notifications.removeRange(100, _notifications.length);
    }

    // حفظ الإشعارات محلياً
    _saveNotifications();
  }

  // تحميل الإشعارات المحفوظة
  void _loadSavedNotifications() {
    final savedNotifications = _storageService.getFromCache<List<dynamic>>('notifications');
    if (savedNotifications != null) {
      _notifications.value = savedNotifications.cast<Map<String, dynamic>>();
    }
  }

  // حفظ الإشعارات محلياً
  void _saveNotifications() {
    _storageService.saveToCache('notifications', _notifications.toList());
  }

  // معالجة بيانات الإشعار والتنقل
  void _handleNotificationData(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    final targetId = data['targetId'] as String?;

    switch (type) {
      case 'like':
        if (targetId != null) {
          Get.toNamed('/video/$targetId');
        }
        break;
      case 'comment':
        if (targetId != null) {
          Get.toNamed('/video/$targetId', arguments: {'showComments': true});
        }
        break;
      case 'follow':
        if (targetId != null) {
          Get.toNamed('/profile/$targetId');
        }
        break;
      case 'live':
        if (targetId != null) {
          Get.toNamed('/live/$targetId');
        }
        break;
      default:
        Get.toNamed('/notifications');
    }
  }

  // ==================== Public Methods ====================

  // إرسال إشعار محلي مخصص
  Future<void> showCustomNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    await _showLocalNotification(
      title: title,
      body: body,
      data: data,
      imageUrl: imageUrl,
    );
  }

  // تحديد إشعار كمقروء
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n['id'] == notificationId);
    if (index != -1) {
      _notifications[index]['read'] = true;
      _saveNotifications();
    }
  }

  // تحديد جميع الإشعارات كمقروءة
  void markAllAsRead() {
    for (var notification in _notifications) {
      notification['read'] = true;
    }
    _saveNotifications();
  }

  // حذف إشعار
  void deleteNotification(String notificationId) {
    _notifications.removeWhere((n) => n['id'] == notificationId);
    _saveNotifications();
  }

  // مسح جميع الإشعارات
  void clearAllNotifications() {
    _notifications.clear();
    _saveNotifications();
  }

  // الحصول على عدد الإشعارات غير المقروءة
  int get unreadCount {
    return _notifications.where((n) => n['read'] == false).length;
  }

  // تحديث إعدادات الإشعارات
  Future<void> updateNotificationSettings(Map<String, bool> settings) async {
    await _storageService.saveNotificationSettings(settings);
    
    // تحديث اشتراكات المواضيع
    for (final entry in settings.entries) {
      if (entry.value) {
        await _firebaseMessaging.subscribeToTopic(entry.key);
      } else {
        await _firebaseMessaging.unsubscribeFromTopic(entry.key);
      }
    }
  }

  // الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      print('✅ Subscribed to topic: $topic');
    } catch (e) {
      print('❌ Error subscribing to topic: $e');
    }
  }

  // إلغاء الاشتراك في موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('✅ Unsubscribed from topic: $topic');
    } catch (e) {
      print('❌ Error unsubscribing from topic: $e');
    }
  }

  // تحديث رمز FCM على الخادم
  Future<void> updateFCMTokenOnServer() async {
    if (_fcmToken.value.isNotEmpty) {
      // إرسال الرمز للخادم
      // يمكن تنفيذ هذا عبر API Service
      print('📤 Updating FCM token on server: ${_fcmToken.value}');
    }
  }
}
