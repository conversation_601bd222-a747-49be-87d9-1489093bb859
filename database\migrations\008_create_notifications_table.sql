-- =====================================================
-- Migration: Create Notifications Table
-- =====================================================

CREATE TABLE IF NOT EXISTS notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL, -- recipient
    sender_id BIGINT, -- who triggered the notification
    
    -- Notification Details
    type ENUM(
        'like', 'comment', 'follow', 'mention', 'share',
        'video_approved', 'video_featured', 'live_start',
        'system', 'promotion', 'achievement', 'reminder'
    ) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    
    -- Related Entities
    video_id BIGINT,
    comment_id BIGINT,
    
    -- Metadata
    data JSON, -- additional data
    action_url VARCHAR(500), -- deep link or URL
    
    -- Media
    image_url VARCHAR(500),
    icon VARCHAR(100),
    
    -- Status
    is_read BOOLEAN DEFAULT FALSE,
    is_sent BOOLEAN DEFAULT FALSE,
    
    -- Delivery Channels
    push_sent BOOLEAN DEFAULT FALSE,
    email_sent BOOLEAN DEFAULT FALSE,
    sms_sent BOOLEAN DEFAULT FALSE,
    
    -- Scheduling
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_video_id (video_id)
);
