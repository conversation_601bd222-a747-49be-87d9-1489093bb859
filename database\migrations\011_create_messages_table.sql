-- =====================================================
-- Migration: Create Messages Table
-- =====================================================

CREATE TABLE IF NOT EXISTS messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    conversation_id BIGINT NOT NULL,
    sender_id BIGINT NOT NULL,
    
    -- Message Content
    content TEXT,
    message_type ENUM(
        'text', 'image', 'video', 'audio', 'gif', 
        'sticker', 'location', 'contact', 'file'
    ) DEFAULT 'text',
    
    -- Media Attachments
    media_url VARCHAR(500),
    media_thumbnail VARCHAR(500),
    media_duration INT, -- for video/audio
    media_size BIGINT, -- file size in bytes
    
    -- Message Metadata
    reply_to_id BIGINT, -- replying to another message
    forwarded_from_id BIGINT, -- forwarded message
    
    -- Status
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    
    -- Delivery Status
    delivery_status ENUM('sent', 'delivered', 'read') DEFAULT 'sent',
    
    -- Timestamps
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    edited_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reply_to_id) REFERENCES messages(id) ON DELETE SET NULL,
    FOREIGN KEY (forwarded_from_id) REFERENCES messages(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_delivery_status (delivery_status),
    INDEX idx_message_type (message_type),
    INDEX idx_reply_to_id (reply_to_id),
    FULLTEXT INDEX idx_content (content)
);
