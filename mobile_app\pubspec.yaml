name: tiktok_clone
description: "تطبيق TikTok Clone ثوري مع 48+ ميزة مبتكرة"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_grid_view: ^0.7.0
  pull_to_refresh: ^2.0.0
  flutter_spinkit: ^5.2.0

  # Navigation & State Management
  get: ^4.6.6
  provider: ^6.1.1
  flutter_bloc: ^8.1.3

  # Network & API
  dio: ^5.3.3
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  connectivity_plus: ^5.0.2

  # Storage & Cache
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1

  # Media & Camera
  camera: ^0.10.5+5
  video_player: ^2.8.1
  image_picker: ^1.0.4
  photo_manager: ^3.0.0
  video_compress: ^3.1.2
  ffmpeg_kit_flutter: ^6.0.3

  # Audio
  audioplayers: ^5.2.1
  just_audio: ^0.9.36
  audio_waveforms: ^1.0.5

  # Permissions
  permission_handler: ^11.1.0

  # Location & Maps
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0

  # Social Features
  share_plus: ^7.2.1
  url_launcher: ^6.2.1
  flutter_contacts: ^1.1.7+1

  # Authentication
  firebase_auth: ^4.15.0
  google_sign_in: ^6.1.6
  sign_in_with_apple: ^5.0.0

  # Firebase Services
  firebase_core: ^2.24.0
  firebase_storage: ^11.5.0
  firebase_messaging: ^14.7.6
  cloud_firestore: ^4.13.3
  firebase_analytics: ^10.7.4

  # Real-time Communication
  socket_io_client: ^2.0.3+1
  agora_rtc_engine: ^6.3.0

  # AI & ML
  tflite_flutter: ^0.10.4
  camera_deep_ar: ^0.0.1

  # Utils
  intl: ^0.18.1
  uuid: ^4.2.1
  crypto: ^3.0.3
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  flutter_local_notifications: ^16.3.0

  # Development
  logger: ^2.0.2+1
  pretty_dio_logger: ^1.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

  # Linting
  flutter_lints: ^3.0.1

  # Testing
  mockito: ^5.4.2
  bloc_test: ^9.1.5

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/sounds/
    - assets/fonts/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
