// =====================================================
// Admin Not Found Page
// =====================================================

import React from 'react';
import { Box, Container, Typography, Button } from '@mui/material';
import { Home as HomeIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const NotFoundPage = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="sm" sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center' }}>
      <Box textAlign="center" width="100%">
        <Typography 
          variant="h1" 
          sx={{ 
            fontSize: '8rem',
            fontWeight: 'bold',
            color: 'primary.main',
            opacity: 0.7,
            mb: 2
          }}
        >
          404
        </Typography>
        
        <Typography variant="h4" gutterBottom fontWeight="bold">
          الصفحة غير موجودة
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          عذراً، لا يمكن العثور على الصفحة المطلوبة في لوحة التحكم.
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<HomeIcon />}
          onClick={() => navigate('/dashboard')}
          size="large"
        >
          العودة للوحة المعلومات
        </Button>
      </Box>
    </Container>
  );
};

export default NotFoundPage;
