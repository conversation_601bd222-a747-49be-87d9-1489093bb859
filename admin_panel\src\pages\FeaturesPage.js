// =====================================================
// Features Page - صفحة إدارة الميزات
// =====================================================

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Switch,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

const FeaturesPage = () => {
  const [features, setFeatures] = useState([
    {
      id: 1,
      key: 'creative_twin',
      name_ar: 'التوأم الإبداعي',
      name_en: 'Creative Twin',
      description_ar: 'نسخة ذكية تتعلم من أسلوب المستخدم',
      status: true,
      category: 'ai'
    },
    {
      id: 2,
      key: 'social_radar',
      name_ar: 'الرادار الاجتماعي',
      name_en: 'Social Radar',
      description_ar: 'اكتشاف الأشخاص والمحتوى القريب جغرافياً',
      status: true,
      category: 'social'
    },
    {
      id: 3,
      key: 'voice_video_generation',
      name_ar: 'توليد فيديو بالصوت',
      name_en: 'Voice Video Generation',
      description_ar: 'إنشاء فيديوهات من خلال الأوامر الصوتية',
      status: false,
      category: 'ai'
    },
    {
      id: 4,
      key: 'instant_art_mode',
      name_ar: 'وضع الفن الفوري',
      name_en: 'Instant Art Mode',
      description_ar: 'تحويل الفيديوهات إلى أعمال فنية',
      status: true,
      category: 'creative'
    }
  ]);

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState(null);

  const handleToggleFeature = (featureId) => {
    setFeatures(prev => prev.map(feature => 
      feature.id === featureId 
        ? { ...feature, status: !feature.status }
        : feature
    ));
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'ai': return 'primary';
      case 'social': return 'secondary';
      case 'creative': return 'success';
      default: return 'default';
    }
  };

  const getCategoryLabel = (category) => {
    switch (category) {
      case 'ai': return 'ذكاء اصطناعي';
      case 'social': return 'اجتماعي';
      case 'creative': return 'إبداعي';
      default: return 'عام';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            إدارة الميزات الديناميكية
          </Typography>
          <Typography variant="body2" color="text.secondary">
            تحكم في الميزات الثورية وإعداداتها
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenDialog(true)}
        >
          إضافة ميزة جديدة
        </Button>
      </Box>

      {/* Features Grid */}
      <Grid container spacing={3}>
        {features.map((feature) => (
          <Grid item xs={12} md={6} lg={4} key={feature.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {feature.name_ar}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {feature.name_en}
                    </Typography>
                  </Box>
                  <Switch
                    checked={feature.status}
                    onChange={() => handleToggleFeature(feature.id)}
                    color="primary"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" paragraph>
                  {feature.description_ar}
                </Typography>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Chip
                    label={getCategoryLabel(feature.category)}
                    size="small"
                    color={getCategoryColor(feature.category)}
                    variant="outlined"
                  />
                  
                  <Box>
                    <IconButton size="small" onClick={() => setSelectedFeature(feature)}>
                      <SettingsIcon />
                    </IconButton>
                    <IconButton size="small">
                      <EditIcon />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Add Feature Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>إضافة ميزة جديدة</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              label="اسم الميزة (عربي)"
              fullWidth
              variant="outlined"
            />
            <TextField
              label="اسم الميزة (إنجليزي)"
              fullWidth
              variant="outlined"
            />
            <TextField
              label="الوصف"
              fullWidth
              multiline
              rows={3}
              variant="outlined"
            />
            <TextField
              label="مفتاح الميزة"
              fullWidth
              variant="outlined"
              helperText="مفتاح فريد للميزة (بالإنجليزية)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={() => setOpenDialog(false)}>
            إضافة
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FeaturesPage;
