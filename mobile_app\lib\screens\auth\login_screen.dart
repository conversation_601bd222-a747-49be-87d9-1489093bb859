// =====================================================
// شاشة تسجيل الدخول
// =====================================================

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تسجيل الدخول'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.login, size: 64),
            SizedBox(height: 16),
            Text(
              'شاشة تسجيل الدخول',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 8),
            Text('قيد التطوير'),
            <PERSON><PERSON><PERSON><PERSON>(height: 24),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement login
                Get.snackbar('تنبيه', 'تسجيل الدخول قيد التطوير');
              },
              child: Text('تسجيل الدخول'),
            ),
          ],
        ),
      ),
    );
  }
}
