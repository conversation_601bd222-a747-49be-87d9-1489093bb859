// =====================================================
// تصميم التطبيق
// =====================================================

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTheme {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFFFF0050);
  static const Color secondaryColor = Color(0xFF25F4EE);
  static const Color accentColor = Color(0xFFFE2C55);
  
  // ألوان النص
  static const Color textPrimary = Color(0xFF161823);
  static const Color textSecondary = Color(0xFF69707D);
  static const Color textLight = Color(0xFFA1A2A7);
  static const Color textWhite = Colors.white;
  
  // ألوان الخلفية
  static const Color backgroundLight = Colors.white;
  static const Color backgroundDark = Color(0xFF000000);
  static const Color surfaceLight = Color(0xFFF8F8F8);
  static const Color surfaceDark = Color(0xFF161823);
  
  // ألوان الحالة
  static const Color successColor = Color(0xFF00D9FF);
  static const Color warningColor = Color(0xFFFFB800);
  static const Color errorColor = Color(0xFFFF3040);
  static const Color infoColor = Color(0xFF007AFF);
  
  // ألوان إضافية
  static const Color borderColor = Color(0xFFE1E2E3);
  static const Color dividerColor = Color(0xFFEFEFF0);
  static const Color shadowColor = Color(0x1A000000);
  
  // التدرجات
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryColor, accentColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondaryColor, Color(0xFF00F5FF)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // أحجام الخط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeading = 28.0;
  
  // المسافات
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // نصف القطر
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  static const double radiusCircular = 50.0;
  
  // الارتفاعات
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  
  // التصميم الفاتح
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // الألوان الأساسية
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceLight,
        background: backgroundLight,
        error: errorColor,
        onPrimary: textWhite,
        onSecondary: textPrimary,
        onSurface: textPrimary,
        onBackground: textPrimary,
        onError: textWhite,
      ),
      
      // خلفية التطبيق
      scaffoldBackgroundColor: backgroundLight,
      
      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundLight,
        foregroundColor: textPrimary,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          color: textPrimary,
          fontSize: fontSizeXLarge,
          fontWeight: FontWeight.bold,
          fontFamily: 'Cairo',
        ),
      ),
      
      // النصوص
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: fontSizeHeading,
          fontWeight: FontWeight.bold,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        displayMedium: TextStyle(
          fontSize: fontSizeTitle,
          fontWeight: FontWeight.bold,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        headlineLarge: TextStyle(
          fontSize: fontSizeXXLarge,
          fontWeight: FontWeight.w600,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        headlineMedium: TextStyle(
          fontSize: fontSizeXLarge,
          fontWeight: FontWeight.w600,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        titleLarge: TextStyle(
          fontSize: fontSizeLarge,
          fontWeight: FontWeight.w600,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        titleMedium: TextStyle(
          fontSize: fontSizeMedium,
          fontWeight: FontWeight.w500,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        bodyLarge: TextStyle(
          fontSize: fontSizeLarge,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        bodyMedium: TextStyle(
          fontSize: fontSizeMedium,
          color: textPrimary,
          fontFamily: 'Cairo',
        ),
        bodySmall: TextStyle(
          fontSize: fontSizeSmall,
          color: textSecondary,
          fontFamily: 'Cairo',
        ),
      ),
      
      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textWhite,
          elevation: elevationMedium,
          padding: const EdgeInsets.symmetric(
            horizontal: paddingLarge,
            vertical: paddingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusLarge),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),
      
      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceLight,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingMedium,
        ),
        hintStyle: const TextStyle(
          color: textLight,
          fontFamily: 'Cairo',
        ),
      ),
      
      // البطاقات
      cardTheme: CardTheme(
        color: backgroundLight,
        elevation: elevationLow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
        ),
        margin: const EdgeInsets.all(paddingSmall),
      ),
      
      // الأيقونات
      iconTheme: const IconThemeData(
        color: textPrimary,
        size: 24,
      ),
      
      // الفواصل
      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: 1,
      ),
    );
  }
  
  // التصميم المظلم
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // الألوان الأساسية
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      colorScheme: const ColorScheme.dark(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceDark,
        background: backgroundDark,
        error: errorColor,
        onPrimary: textWhite,
        onSecondary: textWhite,
        onSurface: textWhite,
        onBackground: textWhite,
        onError: textWhite,
      ),
      
      // خلفية التطبيق
      scaffoldBackgroundColor: backgroundDark,
      
      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundDark,
        foregroundColor: textWhite,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          color: textWhite,
          fontSize: fontSizeXLarge,
          fontWeight: FontWeight.bold,
          fontFamily: 'Cairo',
        ),
      ),
      
      // النصوص
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: fontSizeHeading,
          fontWeight: FontWeight.bold,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        displayMedium: TextStyle(
          fontSize: fontSizeTitle,
          fontWeight: FontWeight.bold,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        headlineLarge: TextStyle(
          fontSize: fontSizeXXLarge,
          fontWeight: FontWeight.w600,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        headlineMedium: TextStyle(
          fontSize: fontSizeXLarge,
          fontWeight: FontWeight.w600,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        titleLarge: TextStyle(
          fontSize: fontSizeLarge,
          fontWeight: FontWeight.w600,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        titleMedium: TextStyle(
          fontSize: fontSizeMedium,
          fontWeight: FontWeight.w500,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        bodyLarge: TextStyle(
          fontSize: fontSizeLarge,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        bodyMedium: TextStyle(
          fontSize: fontSizeMedium,
          color: textWhite,
          fontFamily: 'Cairo',
        ),
        bodySmall: TextStyle(
          fontSize: fontSizeSmall,
          color: textLight,
          fontFamily: 'Cairo',
        ),
      ),
      
      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textWhite,
          elevation: elevationMedium,
          padding: const EdgeInsets.symmetric(
            horizontal: paddingLarge,
            vertical: paddingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusLarge),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),
      
      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceDark,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: Color(0xFF2F2F2F)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: Color(0xFF2F2F2F)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingMedium,
        ),
        hintStyle: const TextStyle(
          color: textLight,
          fontFamily: 'Cairo',
        ),
      ),
      
      // البطاقات
      cardTheme: CardTheme(
        color: surfaceDark,
        elevation: elevationLow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
        ),
        margin: const EdgeInsets.all(paddingSmall),
      ),
      
      // الأيقونات
      iconTheme: const IconThemeData(
        color: textWhite,
        size: 24,
      ),
      
      // الفواصل
      dividerTheme: const DividerThemeData(
        color: Color(0xFF2F2F2F),
        thickness: 1,
        space: 1,
      ),
    );
  }
  
  // إنشاء MaterialColor من Color
  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    
    return MaterialColor(color.value, swatch);
  }
}
