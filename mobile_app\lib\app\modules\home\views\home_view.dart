// =====================================================
// Home View - شاشة الرئيسية
// =====================================================

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tiktok_clone/app/modules/home/<USER>/home_controller.dart';
import 'package:tiktok_clone/app/widgets/video_player_widget.dart';
import 'package:tiktok_clone/app/widgets/video_actions_widget.dart';
import 'package:tiktok_clone/app/widgets/video_info_widget.dart';
import 'package:tiktok_clone/app/widgets/custom_app_bar.dart';
import 'package:tiktok_clone/app/widgets/loading_widget.dart';
import 'package:tiktok_clone/app/widgets/error_widget.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.live_tv, color: Colors.white),
        onPressed: () => Get.toNamed('/live'),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            onPressed: () => controller.switchToFollowing(),
            child: Obx(() => Text(
              'متابَعون',
              style: TextStyle(
                color: controller.isFollowingTab.value 
                    ? Colors.white 
                    : Colors.grey,
                fontSize: 16,
                fontWeight: controller.isFollowingTab.value 
                    ? FontWeight.bold 
                    : FontWeight.normal,
              ),
            )),
          ),
          const SizedBox(width: 20),
          TextButton(
            onPressed: () => controller.switchToForYou(),
            child: Obx(() => Text(
              'لك',
              style: TextStyle(
                color: !controller.isFollowingTab.value 
                    ? Colors.white 
                    : Colors.grey,
                fontSize: 16,
                fontWeight: !controller.isFollowingTab.value 
                    ? FontWeight.bold 
                    : FontWeight.normal,
              ),
            )),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: Colors.white),
          onPressed: () => Get.toNamed('/search'),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Obx(() {
      if (controller.isLoading.value && controller.videos.isEmpty) {
        return const LoadingWidget();
      }

      if (controller.hasError.value && controller.videos.isEmpty) {
        return CustomErrorWidget(
          message: controller.errorMessage.value,
          onRetry: () => controller.loadVideos(),
        );
      }

      if (controller.videos.isEmpty) {
        return _buildEmptyState();
      }

      return _buildVideoFeed();
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 80,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فيديوهات',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بمتابعة المبدعين لرؤية محتواهم',
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Get.toNamed('/discover'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text('اكتشف المبدعين'),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoFeed() {
    return PageView.builder(
      controller: controller.pageController,
      scrollDirection: Axis.vertical,
      onPageChanged: (index) => controller.onPageChanged(index),
      itemCount: controller.videos.length,
      itemBuilder: (context, index) {
        final video = controller.videos[index];
        
        return Stack(
          children: [
            // Video Player
            VideoPlayerWidget(
              videoUrl: video.videoUrl,
              isPlaying: controller.currentIndex.value == index,
              onTap: () => controller.togglePlayPause(),
            ),
            
            // Video Info (Bottom Left)
            Positioned(
              left: 16,
              bottom: 100,
              right: 80,
              child: VideoInfoWidget(
                video: video,
                onUserTap: () => Get.toNamed('/profile/${video.user.username}'),
                onMusicTap: () => Get.toNamed('/music/${video.music?.id}'),
                onHashtagTap: (hashtag) => Get.toNamed('/hashtag/$hashtag'),
              ),
            ),
            
            // Video Actions (Bottom Right)
            Positioned(
              right: 16,
              bottom: 100,
              child: VideoActionsWidget(
                video: video,
                onLike: () => controller.toggleLike(video),
                onComment: () => _showCommentsBottomSheet(video),
                onShare: () => controller.shareVideo(video),
                onSave: () => controller.saveVideo(video),
                onMore: () => _showMoreOptions(video),
              ),
            ),
            
            // Loading indicator for next videos
            if (index == controller.videos.length - 1 && controller.isLoadingMore.value)
              const Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      height: 80,
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Obx(() => BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.black,
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.grey,
        currentIndex: controller.currentBottomIndex.value,
        onTap: (index) => controller.onBottomNavTap(index),
        items: [
          const BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.explore),
            label: 'اكتشف',
          ),
          BottomNavigationBarItem(
            icon: Container(
              width: 45,
              height: 30,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.red, Colors.pink],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.add, color: Colors.white),
            ),
            label: '',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.message),
            label: 'الرسائل',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'الملف الشخصي',
          ),
        ],
      )),
    );
  }

  void _showCommentsBottomSheet(dynamic video) {
    Get.bottomSheet(
      Container(
        height: Get.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey, width: 0.2),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${video.commentsCount} تعليق',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
            ),
            
            // Comments List
            Expanded(
              child: ListView.builder(
                itemCount: 10, // Placeholder
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: const CircleAvatar(
                      backgroundImage: NetworkImage('https://via.placeholder.com/40'),
                    ),
                    title: const Text('اسم المستخدم'),
                    subtitle: const Text('هذا تعليق تجريبي على الفيديو'),
                    trailing: IconButton(
                      icon: const Icon(Icons.favorite_border),
                      onPressed: () {},
                    ),
                  );
                },
              ),
            ),
            
            // Comment Input
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Colors.grey, width: 0.2),
                ),
              ),
              child: Row(
                children: [
                  const CircleAvatar(
                    radius: 16,
                    backgroundImage: NetworkImage('https://via.placeholder.com/32'),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'أضف تعليق...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.send, color: Colors.red),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  void _showMoreOptions(dynamic video) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.bookmark_border),
              title: const Text('حفظ الفيديو'),
              onTap: () {
                Get.back();
                controller.saveVideo(video);
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('تحميل الفيديو'),
              onTap: () {
                Get.back();
                controller.downloadVideo(video);
              },
            ),
            ListTile(
              leading: const Icon(Icons.report),
              title: const Text('الإبلاغ عن الفيديو'),
              onTap: () {
                Get.back();
                controller.reportVideo(video);
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('حظر المستخدم'),
              onTap: () {
                Get.back();
                controller.blockUser(video.user);
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
