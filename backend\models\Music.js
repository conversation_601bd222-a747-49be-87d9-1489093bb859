// =====================================================
// Music Model - نموذج الموسيقى
// =====================================================

const mongoose = require('mongoose');

const musicSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255,
    index: true
  },
  artist: {
    type: String,
    trim: true,
    maxlength: 255,
    index: true
  },
  album: {
    type: String,
    trim: true,
    maxlength: 255
  },
  genre: {
    type: String,
    trim: true,
    maxlength: 100,
    index: true
  },
  
  // File Details
  audioUrl: {
    type: String,
    required: true
  },
  duration: {
    type: Number,
    required: true,
    min: 1,
    max: 600 // 10 minutes max
  },
  fileSize: {
    type: Number,
    min: 0
  },
  format: {
    type: String,
    default: 'mp3',
    enum: ['mp3', 'wav', 'aac', 'ogg', 'm4a']
  },
  
  // Metadata
  coverUrl: {
    type: String
  },
  lyrics: {
    type: String,
    maxlength: 5000
  },
  mood: {
    type: String,
    maxlength: 50,
    enum: [
      'happy', 'sad', 'energetic', 'calm', 'romantic', 
      'aggressive', 'melancholic', 'uplifting', 'mysterious', 'playful'
    ]
  },
  tempo: {
    type: String,
    enum: ['slow', 'medium', 'fast'],
    index: true
  },
  bpm: {
    type: Number,
    min: 60,
    max: 200
  },
  
  // Usage Stats
  usageCount: {
    type: Number,
    default: 0,
    min: 0,
    index: true
  },
  trendingScore: {
    type: Number,
    default: 0,
    min: 0,
    index: true
  },
  
  // Content Rights
  isOriginal: {
    type: Boolean,
    default: false
  },
  isLicensed: {
    type: Boolean,
    default: true
  },
  copyrightOwner: {
    type: String,
    maxlength: 255
  },
  licenseType: {
    type: String,
    enum: ['free', 'premium', 'exclusive'],
    default: 'free',
    index: true
  },
  
  // Status
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  isFeatured: {
    type: Boolean,
    default: false,
    index: true
  },
  isTrending: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // AI Analysis
  aiTags: [{
    tag: String,
    confidence: {
      type: Number,
      min: 0,
      max: 1
    }
  }],
  aiGenrePrediction: {
    type: String,
    maxlength: 100
  },
  aiMoodAnalysis: {
    primary: String,
    secondary: String,
    confidence: {
      type: Number,
      min: 0,
      max: 1
    }
  },
  
  // Audio Features (for recommendation algorithm)
  audioFeatures: {
    danceability: {
      type: Number,
      min: 0,
      max: 1
    },
    energy: {
      type: Number,
      min: 0,
      max: 1
    },
    valence: {
      type: Number,
      min: 0,
      max: 1
    },
    acousticness: {
      type: Number,
      min: 0,
      max: 1
    },
    instrumentalness: {
      type: Number,
      min: 0,
      max: 1
    },
    speechiness: {
      type: Number,
      min: 0,
      max: 1
    }
  },
  
  // Upload Information
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  source: {
    type: String,
    enum: ['upload', 'library', 'generated', 'imported'],
    default: 'library'
  }
}, {
  timestamps: true
});

// Compound Indexes
musicSchema.index({ title: 'text', artist: 'text', album: 'text' });
musicSchema.index({ usageCount: -1, isTrending: -1 });
musicSchema.index({ genre: 1, mood: 1 });
musicSchema.index({ isFeatured: 1, createdAt: -1 });
musicSchema.index({ licenseType: 1, isActive: 1 });

// Method to increment usage count
musicSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.trendingScore = this.calculateTrendingScore();
  return this.save();
};

// Method to calculate trending score
musicSchema.methods.calculateTrendingScore = function() {
  const now = new Date();
  const daysSinceCreated = (now - this.createdAt) / (1000 * 60 * 60 * 24);
  const recentUsageWeight = Math.max(0, 1 - (daysSinceCreated / 30)); // Decay over 30 days
  
  return this.usageCount * recentUsageWeight;
};

// Static method to get trending music
musicSchema.statics.getTrending = function(limit = 20) {
  return this.find({
    isActive: true,
    isLicensed: true
  })
  .sort({ 
    isTrending: -1,
    trendingScore: -1,
    usageCount: -1 
  })
  .limit(limit);
};

// Static method to get featured music
musicSchema.statics.getFeatured = function(limit = 10) {
  return this.find({
    isActive: true,
    isFeatured: true,
    isLicensed: true
  })
  .sort({ createdAt: -1 })
  .limit(limit);
};

// Static method to search music
musicSchema.statics.searchMusic = function(query, options = {}) {
  const {
    page = 1,
    limit = 20,
    genre = null,
    mood = null,
    tempo = null,
    licenseType = null
  } = options;
  
  const skip = (page - 1) * limit;
  
  const searchQuery = {
    $text: { $search: query },
    isActive: true,
    isLicensed: true
  };
  
  if (genre) searchQuery.genre = genre;
  if (mood) searchQuery.mood = mood;
  if (tempo) searchQuery.tempo = tempo;
  if (licenseType) searchQuery.licenseType = licenseType;
  
  return this.find(
    searchQuery,
    { score: { $meta: 'textScore' } }
  )
  .sort({ score: { $meta: 'textScore' } })
  .skip(skip)
  .limit(limit);
};

// Static method to get music by genre
musicSchema.statics.getByGenre = function(genre, options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'popular'
  } = options;
  
  const skip = (page - 1) * limit;
  
  let sortOptions = {};
  switch (sortBy) {
    case 'recent':
      sortOptions = { createdAt: -1 };
      break;
    case 'alphabetical':
      sortOptions = { title: 1 };
      break;
    default:
      sortOptions = { usageCount: -1 };
  }
  
  return this.find({
    genre: genre,
    isActive: true,
    isLicensed: true
  })
  .sort(sortOptions)
  .skip(skip)
  .limit(limit);
};

// Static method to get music recommendations
musicSchema.statics.getRecommendations = async function(userId, limit = 20) {
  // Get user's recently used music
  const recentVideos = await mongoose.model('Video').find({
    user: userId,
    music: { $exists: true }
  })
  .sort({ createdAt: -1 })
  .limit(10)
  .populate('music');
  
  const recentMusicIds = recentVideos
    .map(video => video.music)
    .filter(music => music)
    .map(music => music._id);
  
  if (recentMusicIds.length === 0) {
    // Return trending music if no history
    return this.getTrending(limit);
  }
  
  // Get genres and moods from recent music
  const recentMusic = await this.find({
    _id: { $in: recentMusicIds }
  });
  
  const genres = [...new Set(recentMusic.map(m => m.genre).filter(Boolean))];
  const moods = [...new Set(recentMusic.map(m => m.mood).filter(Boolean))];
  
  // Find similar music
  return this.find({
    _id: { $nin: recentMusicIds },
    $or: [
      { genre: { $in: genres } },
      { mood: { $in: moods } }
    ],
    isActive: true,
    isLicensed: true
  })
  .sort({ usageCount: -1, trendingScore: -1 })
  .limit(limit);
};

// Static method to get music statistics
musicSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $match: { isActive: true }
    },
    {
      $group: {
        _id: null,
        totalTracks: { $sum: 1 },
        totalUsage: { $sum: '$usageCount' },
        avgDuration: { $avg: '$duration' },
        genreBreakdown: {
          $push: '$genre'
        }
      }
    },
    {
      $project: {
        totalTracks: 1,
        totalUsage: 1,
        avgDuration: 1,
        genreBreakdown: {
          $reduce: {
            input: '$genreBreakdown',
            initialValue: {},
            in: {
              $mergeObjects: [
                '$$value',
                {
                  $arrayToObject: [
                    [
                      {
                        k: '$$this',
                        v: {
                          $add: [
                            { $ifNull: [{ $getField: { field: '$$this', input: '$$value' } }, 0] },
                            1
                          ]
                        }
                      }
                    ]
                  ]
                }
              ]
            }
          }
        }
      }
    }
  ]);
};

// Virtual for formatted duration
musicSchema.virtual('formattedDuration').get(function() {
  const minutes = Math.floor(this.duration / 60);
  const seconds = this.duration % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

module.exports = mongoose.model('Music', musicSchema);
