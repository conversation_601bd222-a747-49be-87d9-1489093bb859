# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Production builds
/build
/dist
/out

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# Flutter/Dart specific
mobile_app/.dart_tool/
mobile_app/.flutter-plugins
mobile_app/.flutter-plugins-dependencies
mobile_app/.packages
mobile_app/.pub-cache/
mobile_app/.pub/
mobile_app/build/
mobile_app/ios/Flutter/Generated.xcconfig
mobile_app/ios/Runner/GeneratedPluginRegistrant.*
mobile_app/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
mobile_app/android/.gradle
mobile_app/android/captures/
mobile_app/android/gradlew
mobile_app/android/gradlew.bat
mobile_app/android/local.properties
mobile_app/android/**/GeneratedPluginRegistrant.java
mobile_app/android/key.properties
mobile_app/*.iml
mobile_app/android/.idea
mobile_app/android/.gradle
mobile_app/android/build/

# iOS specific
mobile_app/ios/Pods/
mobile_app/ios/.symlinks/
mobile_app/ios/Flutter/flutter_assets/
mobile_app/ios/ServiceDefinitions.json
mobile_app/ios/Runner/GoogleService-Info.plist

# Android specific
mobile_app/android/app/google-services.json

# Web specific
web_app/build/
web_app/.firebase/

# Admin Panel specific
admin_panel/build/
admin_panel/.firebase/

# Backend specific
backend/uploads/
backend/temp/

# IDE specific
.vscode/
.idea/
*.swp
*.swo
*~

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# API Keys and sensitive data
config/keys.js
config/prod.js
firebase-adminsdk-*.json

# Upload directories
uploads/
temp/
storage/

# Cache
.cache/
.parcel-cache/

# Testing
coverage/
.nyc_output/

# Misc
*.tgz
*.tar.gz
