// =====================================================
// JWT Utilities - أدوات JWT
// =====================================================

const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// إنشاء رمز الوصول
const generateAccessToken = (userId, expiresIn = '15m') => {
  return jwt.sign(
    { 
      userId,
      type: 'access',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn }
  );
};

// إنشاء رمز التحديث
const generateRefreshToken = (userId, expiresIn = '7d') => {
  return jwt.sign(
    { 
      userId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      jti: crypto.randomBytes(16).toString('hex') // معرف فريد للرمز
    },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn }
  );
};

// إنشاء كلا الرمزين
const generateTokens = (userId, accessExpiresIn = '15m', refreshExpiresIn = '7d') => {
  const accessToken = generateAccessToken(userId, accessExpiresIn);
  const refreshToken = generateRefreshToken(userId, refreshExpiresIn);
  
  return {
    accessToken,
    refreshToken
  };
};

// التحقق من رمز الوصول
const verifyAccessToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'access') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

// التحقق من رمز التحديث
const verifyRefreshToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET);
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

// فك تشفير الرمز بدون التحقق من الصحة
const decodeToken = (token) => {
  try {
    return jwt.decode(token, { complete: true });
  } catch (error) {
    return null;
  }
};

// التحقق من انتهاء صلاحية الرمز
const isTokenExpired = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) {
      return true;
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
};

// الحصول على وقت انتهاء الصلاحية
const getTokenExpiration = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) {
      return null;
    }
    
    return new Date(decoded.exp * 1000);
  } catch (error) {
    return null;
  }
};

// الحصول على الوقت المتبقي للانتهاء
const getTimeToExpiration = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) {
      return 0;
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    const timeLeft = decoded.exp - currentTime;
    
    return Math.max(0, timeLeft);
  } catch (error) {
    return 0;
  }
};

// إنشاء رمز لإعادة تعيين كلمة المرور
const generatePasswordResetToken = (userId, email) => {
  return jwt.sign(
    { 
      userId,
      email,
      type: 'password_reset',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn: '1h' }
  );
};

// التحقق من رمز إعادة تعيين كلمة المرور
const verifyPasswordResetToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'password_reset') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

// إنشاء رمز تأكيد البريد الإلكتروني
const generateEmailVerificationToken = (userId, email) => {
  return jwt.sign(
    { 
      userId,
      email,
      type: 'email_verification',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn: '24h' }
  );
};

// التحقق من رمز تأكيد البريد الإلكتروني
const verifyEmailVerificationToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'email_verification') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

// إنشاء رمز للمصادقة الثنائية
const generate2FAToken = (userId) => {
  return jwt.sign(
    { 
      userId,
      type: '2fa_pending',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn: '10m' }
  );
};

// التحقق من رمز المصادقة الثنائية
const verify2FAToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== '2fa_pending') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

// إنشاء رمز مؤقت للعمليات الحساسة
const generateTemporaryToken = (userId, action, data = {}, expiresIn = '15m') => {
  return jwt.sign(
    { 
      userId,
      action,
      data,
      type: 'temporary',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn }
  );
};

// التحقق من الرمز المؤقت
const verifyTemporaryToken = (token, expectedAction = null) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'temporary') {
      throw new Error('Invalid token type');
    }
    
    if (expectedAction && decoded.action !== expectedAction) {
      throw new Error('Invalid action');
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

// إنشاء رمز API للتطبيقات الخارجية
const generateAPIToken = (userId, appId, permissions = [], expiresIn = '30d') => {
  return jwt.sign(
    { 
      userId,
      appId,
      permissions,
      type: 'api',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn }
  );
};

// التحقق من رمز API
const verifyAPIToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'api') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

// تحديث رمز الوصول باستخدام رمز التحديث
const refreshAccessToken = (refreshToken) => {
  const decoded = verifyRefreshToken(refreshToken);
  
  if (!decoded) {
    return null;
  }
  
  const newAccessToken = generateAccessToken(decoded.userId);
  const newRefreshToken = generateRefreshToken(decoded.userId);
  
  return {
    accessToken: newAccessToken,
    refreshToken: newRefreshToken
  };
};

// إنشاء معرف جلسة فريد
const generateSessionId = () => {
  return crypto.randomBytes(32).toString('hex');
};

// تشفير البيانات الحساسة في الرمز
const encryptTokenData = (data, secret = process.env.JWT_SECRET) => {
  const cipher = crypto.createCipher('aes-256-cbc', secret);
  let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

// فك تشفير البيانات من الرمز
const decryptTokenData = (encryptedData, secret = process.env.JWT_SECRET) => {
  try {
    const decipher = crypto.createDecipher('aes-256-cbc', secret);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
  } catch (error) {
    return null;
  }
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  generateTokens,
  verifyAccessToken,
  verifyRefreshToken,
  decodeToken,
  isTokenExpired,
  getTokenExpiration,
  getTimeToExpiration,
  generatePasswordResetToken,
  verifyPasswordResetToken,
  generateEmailVerificationToken,
  verifyEmailVerificationToken,
  generate2FAToken,
  verify2FAToken,
  generateTemporaryToken,
  verifyTemporaryToken,
  generateAPIToken,
  verifyAPIToken,
  refreshAccessToken,
  generateSessionId,
  encryptTokenData,
  decryptTokenData
};
