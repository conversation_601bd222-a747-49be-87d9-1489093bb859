-- =====================================================
-- Migration: Create Music Table
-- =====================================================

CREATE TABLE IF NOT EXISTS music (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    artist VARCHAR(255),
    album VARCHAR(255),
    genre VARCHAR(100),
    
    -- File Details
    audio_url VARCHAR(500) NOT NULL,
    duration INT NOT NULL, -- in seconds
    file_size BIGINT, -- in bytes
    format VARCHAR(10) DEFAULT 'mp3',
    
    -- Metadata
    cover_url VARCHAR(500),
    lyrics TEXT,
    mood VARCHAR(50),
    tempo VARCHAR(20), -- slow, medium, fast
    
    -- Usage Stats
    usage_count INT DEFAULT 0,
    trending_score DECIMAL(5,2) DEFAULT 0,
    
    -- Content Rights
    is_original BOOLEAN DEFAULT FALSE,
    is_licensed BOOLEAN DEFAULT TRUE,
    copyright_owner VARCHAR(255),
    license_type ENUM('free', 'premium', 'exclusive') DEFAULT 'free',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    
    -- AI Analysis
    ai_tags JSON,
    ai_genre_prediction VARCHAR(100),
    ai_mood_analysis JSON,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    -- Indexes
    INDEX idx_title (title),
    INDEX idx_artist (artist),
    INDEX idx_genre (genre),
    INDEX idx_usage_count (usage_count),
    INDEX idx_is_trending (is_trending),
    INDEX idx_is_featured (is_featured),
    INDEX idx_license_type (license_type),
    FULLTEXT INDEX idx_search (title, artist, album)
);
