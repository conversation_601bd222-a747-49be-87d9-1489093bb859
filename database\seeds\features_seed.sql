-- =====================================================
-- بيانات أولية لجدول الميزات الثورية
-- =====================================================

INSERT INTO features (feature_key, name_ar, name_en, description_ar, description_en, status, settings) VALUES

-- الميزات الأساسية
('user_registration', 'تسجيل المستخدمين', 'User Registration', 'نظام تسجيل وإدارة المستخدمين', 'User registration and management system', 1, '{"max_users": null, "verification_required": true}'),
('video_upload', 'رفع الفيديوهات', 'Video Upload', 'رفع ومشاركة الفيديوهات', 'Upload and share videos', 1, '{"max_duration": 180, "max_size": "100MB", "formats": ["mp4", "mov", "avi"]}'),
('social_interactions', 'التفاعل الاجتماعي', 'Social Interactions', 'الإعجاب والتعليق والمشاركة', 'Like, comment and share functionality', 1, '{"enable_likes": true, "enable_comments": true, "enable_shares": true}'),
('live_streaming', 'البث المباشر', 'Live Streaming', 'بث مباشر للفيديو', 'Live video streaming', 1, '{"max_duration": 7200, "quality": "1080p"}'),
('messaging', 'الرسائل الخاصة', 'Private Messaging', 'نظام الرسائل بين المستخدمين', 'Private messaging system', 1, '{"file_sharing": true, "voice_messages": true}'),

-- الميزات المتقدمة
('video_editing', 'تحرير الفيديو', 'Video Editing', 'أدوات تحرير الفيديو المتقدمة', 'Advanced video editing tools', 1, '{"filters": true, "effects": true, "music": true}'),
('ai_recommendations', 'التوصيات الذكية', 'AI Recommendations', 'خوارزمية توصيات بالذكاء الاصطناعي', 'AI-powered recommendation algorithm', 1, '{"learning_rate": 0.1, "update_frequency": "hourly"}'),
('hashtag_system', 'نظام الهاشتاج', 'Hashtag System', 'تنظيم المحتوى بالهاشتاجز', 'Content organization with hashtags', 1, '{"trending_algorithm": true, "max_hashtags": 10}'),
('challenges', 'التحديات', 'Challenges', 'تحديات وأنشطة جماعية', 'Community challenges and activities', 1, '{"duration_days": 7, "prizes": true}'),
('virtual_gifts', 'الهدايا الافتراضية', 'Virtual Gifts', 'نظام الهدايا والعملات الافتراضية', 'Virtual gifts and currency system', 1, '{"coin_rate": 100, "gift_categories": ["basic", "premium", "exclusive"]}'),

-- الميزات الثورية (33-48)
('creative_twin', 'التوأم الإبداعي', 'Creative Twin', 'إنشاء نسخة ذكية من المستخدم تتعلم من أسلوبه', 'Create an AI twin that learns user style', 1, '{"learning_period": 30, "similarity_threshold": 0.8, "auto_generate": false}'),

('social_radar', 'الرادار الاجتماعي', 'Social Radar', 'اكتشاف الأشخاص والمحتوى القريب جغرافياً', 'Discover nearby people and content', 1, '{"radius_km": 10, "privacy_levels": ["public", "friends", "private"], "real_time": true}'),

('infinite_fusion', 'الدمج العشوائي للفيديوهات', 'Infinite Fusion', 'دمج فيديوهات متعددة بطريقة إبداعية وعشوائية', 'Creative random fusion of multiple videos', 1, '{"max_videos": 5, "transition_effects": true, "auto_sync": true}'),

('ai_reaction_overlay', 'ردود فعل بالفيديو', 'AI Reaction Overlay', 'إضافة ردود فعل فيديو ذكية على الفيديوهات', 'Add intelligent video reactions overlay', 1, '{"emotion_detection": true, "auto_reactions": true, "custom_reactions": true}'),

('life_timeline', 'تايم لاين الحياة', 'Life Timeline', 'إنشاء خط زمني تفاعلي لحياة المستخدم', 'Create interactive life timeline', 1, '{"auto_organize": true, "milestone_detection": true, "privacy_control": true}'),

('emotion_ui', 'واجهة تتغير بالمشاعر', 'Emotion UI', 'تغيير واجهة التطبيق حسب مشاعر المستخدم', 'UI that adapts to user emotions', 1, '{"emotion_detection": "face", "theme_adaptation": true, "music_sync": true}'),

('tutor_ai', 'مساعد الفيديو الذكي', 'Tutor AI', 'مساعد ذكي لتحسين جودة الفيديوهات وتقديم النصائح', 'AI assistant for video improvement', 1, '{"real_time_tips": true, "quality_analysis": true, "trend_suggestions": true}'),

('live_remix', 'ريمكس لايف', 'Live Remix', 'إعادة مزج البث المباشر في الوقت الفعلي', 'Real-time live stream remixing', 1, '{"effects_library": true, "audience_participation": true, "auto_highlights": true}'),

('simucast_ai', 'البث الوهمي', 'Simucast AI', 'إنشاء بث مباشر وهمي بالذكاء الاصطناعي', 'AI-generated simulated live streaming', 1, '{"personality_types": 5, "interaction_level": "high", "content_themes": ["entertainment", "education", "music"]}'),

('voice_video_generator', 'توليد فيديو بالصوت', 'Voice Video Generator', 'إنشاء فيديوهات من الأوامر الصوتية', 'Generate videos from voice commands', 1, '{"languages": ["ar", "en"], "video_styles": ["realistic", "animated", "artistic"], "max_duration": 60}'),

('video_dialogue_comments', 'التعليق بالفيديو', 'Video Dialogue Comments', 'التعليق على الفيديوهات بفيديوهات أخرى', 'Comment on videos with video responses', 1, '{"max_duration": 30, "threading": true, "moderation": true}'),

('reverse_storytelling', 'الحكاية العكسية', 'Reverse Storytelling', 'سرد القصص من النهاية إلى البداية بطريقة تفاعلية', 'Interactive reverse storytelling', 1, '{"auto_reverse": true, "chapter_detection": true, "user_control": true}'),

('viral_boost', 'سوق تريند افتراضي', 'Viral Boost', 'سوق لشراء وبيع الترندات والشهرة', 'Virtual marketplace for trends and fame', 1, '{"boost_packages": ["basic", "premium", "viral"], "auction_system": true, "roi_tracking": true}'),

('multi_angle_record', 'التصوير بزوايا متعددة', 'Multi Angle Record', 'تسجيل من عدة زوايا في نفس الوقت', 'Record from multiple angles simultaneously', 1, '{"max_cameras": 4, "auto_switching": true, "sync_audio": true}'),

('instant_art', 'وضع الفن الفوري', 'Instant Art', 'تحويل الفيديوهات إلى أعمال فنية فورية', 'Transform videos into instant artworks', 1, '{"art_styles": ["impressionist", "abstract", "pop_art", "digital"], "real_time": true, "save_originals": true}'),

('video_encryption', 'تشفير الفيديو المؤقت', 'Video Encryption', 'تشفير الفيديوهات الحساسة مؤقتاً', 'Temporary encryption for sensitive videos', 1, '{"encryption_levels": ["basic", "advanced", "military"], "auto_expire": true, "access_control": true}'),

-- ميزات إضافية متقدمة
('face_verification', 'التحقق بالوجه', 'Face Verification', 'نظام التحقق من الهوية بالوجه', 'Face-based identity verification', 1, '{"accuracy_threshold": 0.95, "liveness_detection": true}'),
('content_moderation', 'إشراف المحتوى', 'Content Moderation', 'إشراف تلقائي على المحتوى بالذكاء الاصطناعي', 'AI-powered content moderation', 1, '{"auto_flag": true, "sensitivity": "medium", "human_review": true}'),
('analytics_dashboard', 'لوحة التحليلات', 'Analytics Dashboard', 'تحليلات متقدمة للمستخدمين والمحتوى', 'Advanced analytics for users and content', 1, '{"real_time": true, "export_formats": ["pdf", "excel", "json"]}'),
('multi_language', 'دعم متعدد اللغات', 'Multi Language', 'دعم لغات متعددة مع ترجمة تلقائية', 'Multi-language support with auto translation', 1, '{"supported_languages": ["ar", "en", "fr", "es", "de"], "auto_detect": true}'),
('dark_mode', 'الوضع المظلم', 'Dark Mode', 'واجهة مظلمة لراحة العين', 'Dark theme for eye comfort', 1, '{"auto_switch": true, "schedule": "sunset_sunrise"}'),
('offline_mode', 'الوضع غير المتصل', 'Offline Mode', 'تشغيل المحتوى بدون اتصال إنترنت', 'Offline content playback', 1, '{"download_quality": ["720p", "1080p"], "storage_limit": "5GB"}'),
('parental_controls', 'الرقابة الأبوية', 'Parental Controls', 'أدوات الرقابة الأبوية والحماية', 'Parental control and safety tools', 1, '{"age_restrictions": true, "content_filtering": true, "time_limits": true}'),
('accessibility', 'إمكانية الوصول', 'Accessibility', 'ميزات لذوي الاحتياجات الخاصة', 'Accessibility features for disabled users', 1, '{"screen_reader": true, "voice_navigation": true, "high_contrast": true}');
