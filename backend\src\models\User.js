// =====================================================
// نموذج المستخدم
// =====================================================

const bcrypt = require('bcryptjs');
const { query, transaction } = require('../../database/config/database');
const { ValidationError, NotFoundError, ConflictError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

class User {
  constructor(data = {}) {
    this.id = data.id;
    this.username = data.username;
    this.email = data.email;
    this.phone = data.phone;
    this.first_name = data.first_name;
    this.last_name = data.last_name;
    this.bio = data.bio;
    this.avatar_url = data.avatar_url;
    this.cover_url = data.cover_url;
    this.birth_date = data.birth_date;
    this.gender = data.gender;
    this.country = data.country;
    this.city = data.city;
    this.language = data.language || 'ar';
    this.timezone = data.timezone;
    this.is_verified = data.is_verified || false;
    this.is_private = data.is_private || false;
    this.is_active = data.is_active || true;
    this.is_banned = data.is_banned || false;
    this.followers_count = data.followers_count || 0;
    this.following_count = data.following_count || 0;
    this.videos_count = data.videos_count || 0;
    this.likes_received_count = data.likes_received_count || 0;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // إنشاء مستخدم جديد
  static async create(userData) {
    try {
      const { username, email, password, phone, first_name, last_name } = userData;
      
      // التحقق من البيانات المطلوبة
      if (!username || !email || !password) {
        throw new ValidationError('اسم المستخدم والبريد الإلكتروني وكلمة المرور مطلوبة');
      }
      
      // التحقق من عدم وجود المستخدم مسبقاً
      const existingUser = await User.findByEmailOrUsername(email, username);
      if (existingUser) {
        throw new ConflictError('البريد الإلكتروني أو اسم المستخدم موجود مسبقاً');
      }
      
      // تشفير كلمة المرور
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
      const password_hash = await bcrypt.hash(password, saltRounds);
      
      // إدخال المستخدم في قاعدة البيانات
      const result = await query(
        `INSERT INTO users (username, email, phone, password_hash, first_name, last_name) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [username, email, phone, password_hash, first_name, last_name]
      );
      
      // إنشاء محفظة العملات للمستخدم
      await query(
        'INSERT INTO user_coins (user_id) VALUES (?)',
        [result.insertId]
      );
      
      // تسجيل النشاط
      logger.logUserActivity(result.insertId, 'user_created', { email, username });
      
      // إرجاع المستخدم الجديد
      return await User.findById(result.insertId);
      
    } catch (error) {
      logger.error('خطأ في إنشاء المستخدم:', error);
      throw error;
    }
  }

  // البحث عن مستخدم بالمعرف
  static async findById(id) {
    try {
      const users = await query(
        `SELECT * FROM users WHERE id = ? AND is_active = 1`,
        [id]
      );
      
      return users.length > 0 ? new User(users[0]) : null;
      
    } catch (error) {
      logger.logDatabaseError(error, 'findById', [id]);
      throw error;
    }
  }

  // البحث عن مستخدم بالبريد الإلكتروني
  static async findByEmail(email) {
    try {
      const users = await query(
        'SELECT * FROM users WHERE email = ? AND is_active = 1',
        [email]
      );
      
      return users.length > 0 ? new User(users[0]) : null;
      
    } catch (error) {
      logger.logDatabaseError(error, 'findByEmail', [email]);
      throw error;
    }
  }

  // البحث عن مستخدم باسم المستخدم
  static async findByUsername(username) {
    try {
      const users = await query(
        'SELECT * FROM users WHERE username = ? AND is_active = 1',
        [username]
      );
      
      return users.length > 0 ? new User(users[0]) : null;
      
    } catch (error) {
      logger.logDatabaseError(error, 'findByUsername', [username]);
      throw error;
    }
  }

  // البحث عن مستخدم بالبريد الإلكتروني أو اسم المستخدم
  static async findByEmailOrUsername(email, username) {
    try {
      const users = await query(
        'SELECT * FROM users WHERE (email = ? OR username = ?) AND is_active = 1',
        [email, username]
      );
      
      return users.length > 0 ? new User(users[0]) : null;
      
    } catch (error) {
      logger.logDatabaseError(error, 'findByEmailOrUsername', [email, username]);
      throw error;
    }
  }

  // التحقق من كلمة المرور
  async verifyPassword(password) {
    try {
      const user = await query(
        'SELECT password_hash FROM users WHERE id = ?',
        [this.id]
      );
      
      if (user.length === 0) {
        return false;
      }
      
      return await bcrypt.compare(password, user[0].password_hash);
      
    } catch (error) {
      logger.error('خطأ في التحقق من كلمة المرور:', error);
      return false;
    }
  }

  // تحديث بيانات المستخدم
  async update(updateData) {
    try {
      const allowedFields = [
        'first_name', 'last_name', 'bio', 'avatar_url', 'cover_url',
        'birth_date', 'gender', 'country', 'city', 'language', 'timezone',
        'is_private'
      ];
      
      const updateFields = [];
      const updateValues = [];
      
      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key) && value !== undefined) {
          updateFields.push(`${key} = ?`);
          updateValues.push(value);
        }
      }
      
      if (updateFields.length === 0) {
        throw new ValidationError('لا توجد حقول صالحة للتحديث');
      }
      
      updateValues.push(this.id);
      
      await query(
        `UPDATE users SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        updateValues
      );
      
      // تسجيل النشاط
      logger.logUserActivity(this.id, 'profile_updated', updateData);
      
      // إرجاع البيانات المحدثة
      const updatedUser = await User.findById(this.id);
      Object.assign(this, updatedUser);
      
      return this;
      
    } catch (error) {
      logger.error('خطأ في تحديث المستخدم:', error);
      throw error;
    }
  }

  // تغيير كلمة المرور
  async changePassword(currentPassword, newPassword) {
    try {
      // التحقق من كلمة المرور الحالية
      const isCurrentPasswordValid = await this.verifyPassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new ValidationError('كلمة المرور الحالية غير صحيحة');
      }
      
      // تشفير كلمة المرور الجديدة
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
      const password_hash = await bcrypt.hash(newPassword, saltRounds);
      
      await query(
        'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [password_hash, this.id]
      );
      
      // تسجيل النشاط
      logger.logUserActivity(this.id, 'password_changed');
      
      return true;
      
    } catch (error) {
      logger.error('خطأ في تغيير كلمة المرور:', error);
      throw error;
    }
  }

  // متابعة مستخدم
  async follow(targetUserId) {
    try {
      if (this.id === targetUserId) {
        throw new ValidationError('لا يمكنك متابعة نفسك');
      }
      
      // التحقق من وجود المستخدم المستهدف
      const targetUser = await User.findById(targetUserId);
      if (!targetUser) {
        throw new NotFoundError('المستخدم غير موجود');
      }
      
      // التحقق من عدم وجود متابعة مسبقة
      const existingFollow = await query(
        'SELECT id FROM follows WHERE follower_id = ? AND following_id = ?',
        [this.id, targetUserId]
      );
      
      if (existingFollow.length > 0) {
        throw new ConflictError('تتابع هذا المستخدم بالفعل');
      }
      
      // إضافة المتابعة وتحديث العدادات
      await transaction([
        {
          sql: 'INSERT INTO follows (follower_id, following_id) VALUES (?, ?)',
          params: [this.id, targetUserId]
        },
        {
          sql: 'UPDATE users SET following_count = following_count + 1 WHERE id = ?',
          params: [this.id]
        },
        {
          sql: 'UPDATE users SET followers_count = followers_count + 1 WHERE id = ?',
          params: [targetUserId]
        }
      ]);
      
      // تسجيل النشاط
      logger.logUserActivity(this.id, 'user_followed', { targetUserId });
      
      return true;
      
    } catch (error) {
      logger.error('خطأ في المتابعة:', error);
      throw error;
    }
  }

  // إلغاء متابعة مستخدم
  async unfollow(targetUserId) {
    try {
      // التحقق من وجود المتابعة
      const existingFollow = await query(
        'SELECT id FROM follows WHERE follower_id = ? AND following_id = ?',
        [this.id, targetUserId]
      );
      
      if (existingFollow.length === 0) {
        throw new NotFoundError('لا تتابع هذا المستخدم');
      }
      
      // حذف المتابعة وتحديث العدادات
      await transaction([
        {
          sql: 'DELETE FROM follows WHERE follower_id = ? AND following_id = ?',
          params: [this.id, targetUserId]
        },
        {
          sql: 'UPDATE users SET following_count = following_count - 1 WHERE id = ?',
          params: [this.id]
        },
        {
          sql: 'UPDATE users SET followers_count = followers_count - 1 WHERE id = ?',
          params: [targetUserId]
        }
      ]);
      
      // تسجيل النشاط
      logger.logUserActivity(this.id, 'user_unfollowed', { targetUserId });
      
      return true;
      
    } catch (error) {
      logger.error('خطأ في إلغاء المتابعة:', error);
      throw error;
    }
  }

  // الحصول على قائمة المتابعين
  async getFollowers(page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      
      const followers = await query(
        `SELECT u.id, u.username, u.first_name, u.last_name, u.avatar_url, u.is_verified
         FROM follows f
         JOIN users u ON f.follower_id = u.id
         WHERE f.following_id = ? AND u.is_active = 1
         ORDER BY f.created_at DESC
         LIMIT ? OFFSET ?`,
        [this.id, limit, offset]
      );
      
      return followers;
      
    } catch (error) {
      logger.logDatabaseError(error, 'getFollowers', [this.id, limit, offset]);
      throw error;
    }
  }

  // الحصول على قائمة المتابَعين
  async getFollowing(page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      
      const following = await query(
        `SELECT u.id, u.username, u.first_name, u.last_name, u.avatar_url, u.is_verified
         FROM follows f
         JOIN users u ON f.following_id = u.id
         WHERE f.follower_id = ? AND u.is_active = 1
         ORDER BY f.created_at DESC
         LIMIT ? OFFSET ?`,
        [this.id, limit, offset]
      );
      
      return following;
      
    } catch (error) {
      logger.logDatabaseError(error, 'getFollowing', [this.id, limit, offset]);
      throw error;
    }
  }

  // تحويل إلى JSON (إخفاء البيانات الحساسة)
  toJSON() {
    const user = { ...this };
    delete user.password_hash;
    return user;
  }

  // الحصول على الملف الشخصي العام
  toPublicProfile() {
    return {
      id: this.id,
      username: this.username,
      first_name: this.first_name,
      last_name: this.last_name,
      bio: this.bio,
      avatar_url: this.avatar_url,
      cover_url: this.cover_url,
      is_verified: this.is_verified,
      is_private: this.is_private,
      followers_count: this.followers_count,
      following_count: this.following_count,
      videos_count: this.videos_count,
      likes_received_count: this.likes_received_count,
      created_at: this.created_at
    };
  }
}

module.exports = User;
