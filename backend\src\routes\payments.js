// =====================================================
// مسارات المدفوعات
// =====================================================

const express = require('express');
const router = express.Router();

// TODO: تطوير مسارات المدفوعات
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'مسارات المدفوعات قيد التطوير',
    endpoints: [
      'GET /payments - الحصول على المعاملات',
      'POST /payments/purchase - شراء عملات',
      'POST /payments/gift - إرسال هدية',
      'GET /payments/history - تاريخ المعاملات',
      'POST /payments/withdraw - سحب أموال',
      'GET /payments/balance - رصيد المستخدم'
    ]
  });
});

module.exports = router;
