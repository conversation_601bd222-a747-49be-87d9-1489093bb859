// =====================================================
// Admin Dashboard Component - مكون لوحة التحكم الإدارية
// =====================================================

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  IconButton,
  Chip,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  useTheme
} from '@mui/material';
import {
  People,
  VideoLibrary,
  TrendingUp,
  Report,
  Analytics,
  Security,
  Speed,
  Storage,
  Refresh,
  Warning,
  CheckCircle,
  Error
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { motion } from 'framer-motion';
import { fetchDashboardStats, fetchSystemHealth } from '../store/slices/dashboardSlice';
import StatsCard from './StatsCard';
import RecentActivity from './RecentActivity';
import SystemHealth from './SystemHealth';
import UserGrowthChart from './UserGrowthChart';
import ContentAnalytics from './ContentAnalytics';

const Dashboard = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  
  const { 
    stats, 
    systemHealth, 
    userGrowth, 
    contentAnalytics,
    loading, 
    error 
  } = useSelector(state => state.dashboard);

  const [activeTab, setActiveTab] = useState(0);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, [dispatch]);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        dispatch(fetchDashboardStats()),
        dispatch(fetchSystemHealth())
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          حدث خطأ في تحميل بيانات لوحة التحكم: {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          لوحة التحكم الرئيسية
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton 
            onClick={handleRefresh} 
            disabled={refreshing}
            color="primary"
          >
            <Refresh sx={{ 
              animation: refreshing ? 'spin 1s linear infinite' : 'none',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }} />
          </IconButton>
        </Box>
      </Box>

      {/* Loading Progress */}
      {loading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress />
        </Box>
      )}

      {/* System Health Alert */}
      {systemHealth && systemHealth.status !== 'healthy' && (
        <Alert 
          severity={systemHealth.status === 'degraded' ? 'warning' : 'error'} 
          sx={{ mb: 3 }}
          icon={systemHealth.status === 'degraded' ? <Warning /> : <Error />}
        >
          حالة النظام: {systemHealth.status === 'degraded' ? 'متدهورة' : 'غير صحية'}
          - يرجى مراجعة حالة الخدمات
        </Alert>
      )}

      {/* Main Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <StatsCard
              title="إجمالي المستخدمين"
              value={stats?.totalUsers || 0}
              change={stats?.userGrowth || 0}
              icon={<People sx={{ fontSize: 40, color: 'primary.main' }} />}
              color="primary"
              loading={loading}
            />
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <StatsCard
              title="إجمالي الفيديوهات"
              value={stats?.totalVideos || 0}
              change={stats?.videoGrowth || 0}
              icon={<VideoLibrary sx={{ fontSize: 40, color: 'secondary.main' }} />}
              color="secondary"
              loading={loading}
            />
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <StatsCard
              title="المشاهدات اليومية"
              value={stats?.dailyViews || 0}
              change={stats?.viewsGrowth || 0}
              icon={<TrendingUp sx={{ fontSize: 40, color: 'success.main' }} />}
              color="success"
              loading={loading}
            />
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <StatsCard
              title="التقارير المعلقة"
              value={stats?.pendingReports || 0}
              change={stats?.reportsChange || 0}
              icon={<Report sx={{ fontSize: 40, color: 'warning.main' }} />}
              color="warning"
              loading={loading}
            />
          </motion.div>
        </Grid>
      </Grid>

      {/* System Health Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Speed sx={{ mr: 1, color: 'info.main' }} />
                <Typography variant="h6">الأداء</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>
                {systemHealth?.performance || 'N/A'}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                متوسط وقت الاستجابة
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Storage sx={{ mr: 1, color: 'info.main' }} />
                <Typography variant="h6">التخزين</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>
                {systemHealth?.storage || 'N/A'}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                استخدام مساحة التخزين
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Security sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6">الأمان</Typography>
              </Box>
              <Chip 
                label={systemHealth?.security || 'آمن'}
                color="success"
                icon={<CheckCircle />}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                حالة الأمان
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Analytics sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">وقت التشغيل</Typography>
              </Box>
              <Typography variant="h4" sx={{ mb: 1 }}>
                {systemHealth?.uptime || 'N/A'}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                خلال آخر 30 يوم
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for Different Views */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="نمو المستخدمين" />
          <Tab label="تحليلات المحتوى" />
          <Tab label="النشاط الأخير" />
          <Tab label="صحة النظام" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <Box sx={{ mt: 3 }}>
        {activeTab === 0 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <UserGrowthChart data={userGrowth} loading={loading} />
          </motion.div>
        )}

        {activeTab === 1 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ContentAnalytics data={contentAnalytics} loading={loading} />
          </motion.div>
        )}

        {activeTab === 2 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <RecentActivity loading={loading} />
          </motion.div>
        )}

        {activeTab === 3 && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SystemHealth data={systemHealth} loading={loading} />
          </motion.div>
        )}
      </Box>

      {/* Quick Actions */}
      <Paper sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          إجراءات سريعة
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'scale(1.02)' }
              }}
              onClick={() => window.open('/admin/users', '_blank')}
            >
              <CardContent sx={{ textAlign: 'center' }}>
                <People sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">إدارة المستخدمين</Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'scale(1.02)' }
              }}
              onClick={() => window.open('/admin/content', '_blank')}
            >
              <CardContent sx={{ textAlign: 'center' }}>
                <VideoLibrary sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                <Typography variant="h6">إدارة المحتوى</Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'scale(1.02)' }
              }}
              onClick={() => window.open('/admin/reports', '_blank')}
            >
              <CardContent sx={{ textAlign: 'center' }}>
                <Report sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                <Typography variant="h6">مراجعة التقارير</Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'scale(1.02)' }
              }}
              onClick={() => window.open('/admin/analytics', '_blank')}
            >
              <CardContent sx={{ textAlign: 'center' }}>
                <Analytics sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                <Typography variant="h6">التحليلات المتقدمة</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default Dashboard;
