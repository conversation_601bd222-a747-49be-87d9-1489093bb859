// =====================================================
// ثوابت التطبيق
// =====================================================

import 'package:flutter/material.dart';

class AppConstants {
  // معلومات التطبيق
  static const String appName = 'TikTok Clone';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق TikTok Clone ثوري مع 48+ ميزة مبتكرة';
  
  // إعدادات API
  static const String baseUrl = 'http://localhost:5000/api';
  static const String socketUrl = 'http://localhost:5000';
  
  // مسارات API
  static const String authPath = '/auth';
  static const String usersPath = '/users';
  static const String videosPath = '/videos';
  static const String featuresPath = '/features';
  static const String livePath = '/live';
  static const String messagesPath = '/messages';
  static const String paymentsPath = '/payments';
  static const String aiPath = '/ai';
  
  // إعدادات الفيديو
  static const int maxVideoDuration = 180; // 3 دقائق
  static const int maxVideoSize = 100 * 1024 * 1024; // 100 MB
  static const List<String> supportedVideoFormats = ['mp4', 'mov', 'avi'];
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'gif'];
  
  // إعدادات الصوت
  static const int maxAudioDuration = 300; // 5 دقائق
  static const List<String> supportedAudioFormats = ['mp3', 'wav', 'aac', 'm4a'];
  
  // إعدادات البث المباشر
  static const int maxLiveDuration = 7200; // ساعتين
  static const String liveQuality = '1080p';
  
  // إعدادات التخزين المؤقت
  static const int cacheMaxAge = 7 * 24 * 60 * 60; // أسبوع
  static const int maxCacheSize = 500 * 1024 * 1024; // 500 MB
  
  // مفاتيح التخزين المحلي
  static const String userTokenKey = 'user_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String featuresKey = 'app_features';
  static const String themeKey = 'app_theme';
  static const String languageKey = 'app_language';
  
  // أبعاد الشاشة
  static const double maxMobileWidth = 600;
  static const double maxTabletWidth = 1200;
  
  // أبعاد الفيديو
  static const double videoAspectRatio = 9 / 16; // عمودي
  static const Size videoResolution = Size(1080, 1920);
  static const Size thumbnailSize = Size(300, 400);
  
  // إعدادات الرسوم المتحركة
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // إعدادات التفاعل
  static const Duration doubleTapDelay = Duration(milliseconds: 300);
  static const Duration longPressDelay = Duration(milliseconds: 500);
  
  // حدود النصوص
  static const int maxUsernameLength = 30;
  static const int maxBioLength = 500;
  static const int maxCommentLength = 500;
  static const int maxCaptionLength = 2000;
  
  // إعدادات الصفحات
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // أنواع الإشعارات
  static const String notificationTypeLike = 'like';
  static const String notificationTypeComment = 'comment';
  static const String notificationTypeFollow = 'follow';
  static const String notificationTypeMention = 'mention';
  static const String notificationTypeLive = 'live';
  static const String notificationTypeGift = 'gift';
  static const String notificationTypeSystem = 'system';
  
  // أنواع التقارير
  static const String reportTypeSpam = 'spam';
  static const String reportTypeHarassment = 'harassment';
  static const String reportTypeInappropriate = 'inappropriate';
  static const String reportTypeCopyright = 'copyright';
  static const String reportTypeFake = 'fake';
  static const String reportTypeOther = 'other';
  
  // أنواع الهدايا
  static const String giftCategoryBasic = 'basic';
  static const String giftCategoryPremium = 'premium';
  static const String giftCategoryExclusive = 'exclusive';
  
  // مستويات الندرة
  static const String rarityCommon = 'common';
  static const String rarityRare = 'rare';
  static const String rarityEpic = 'epic';
  static const String rarityLegendary = 'legendary';
  
  // أنواع العملات
  static const String currencyCoins = 'coins';
  static const String currencyDiamonds = 'diamonds';
  static const String currencyUSD = 'USD';
  
  // حالات المحتوى
  static const String moderationPending = 'pending';
  static const String moderationApproved = 'approved';
  static const String moderationRejected = 'rejected';
  
  // أنواع المعاملات
  static const String transactionEarn = 'earn';
  static const String transactionSpend = 'spend';
  static const String transactionGift = 'gift';
  static const String transactionPurchase = 'purchase';
  static const String transactionRefund = 'refund';
  
  // حالات البث المباشر
  static const String liveStatusScheduled = 'scheduled';
  static const String liveStatusLive = 'live';
  static const String liveStatusEnded = 'ended';
  static const String liveStatusCancelled = 'cancelled';
  
  // أنواع الرسائل
  static const String messageTypeText = 'text';
  static const String messageTypeImage = 'image';
  static const String messageTypeVideo = 'video';
  static const String messageTypeAudio = 'audio';
  static const String messageTypeFile = 'file';
  
  // مستويات السجل
  static const String logLevelDebug = 'debug';
  static const String logLevelInfo = 'info';
  static const String logLevelWarning = 'warning';
  static const String logLevelError = 'error';
  
  // روابط مهمة
  static const String privacyPolicyUrl = 'https://tiktokclone.com/privacy';
  static const String termsOfServiceUrl = 'https://tiktokclone.com/terms';
  static const String supportUrl = 'https://tiktokclone.com/support';
  static const String feedbackUrl = 'https://tiktokclone.com/feedback';
  
  // معلومات الاتصال
  static const String supportEmail = '<EMAIL>';
  static const String feedbackEmail = '<EMAIL>';
  static const String businessEmail = '<EMAIL>';
  
  // شبكات التواصل الاجتماعي
  static const String facebookUrl = 'https://facebook.com/tiktokclone';
  static const String twitterUrl = 'https://twitter.com/tiktokclone';
  static const String instagramUrl = 'https://instagram.com/tiktokclone';
  static const String youtubeUrl = 'https://youtube.com/tiktokclone';
  
  // رسائل الخطأ الافتراضية
  static const String errorNetworkConnection = 'تحقق من اتصال الإنترنت';
  static const String errorServerError = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
  static const String errorUnknown = 'حدث خطأ غير متوقع';
  static const String errorTimeout = 'انتهت مهلة الاتصال';
  static const String errorUnauthorized = 'غير مصرح لك بالوصول';
  static const String errorForbidden = 'ليس لديك صلاحية للقيام بهذا الإجراء';
  static const String errorNotFound = 'المورد غير موجود';
  static const String errorValidation = 'بيانات غير صحيحة';
  
  // رسائل النجاح الافتراضية
  static const String successLogin = 'تم تسجيل الدخول بنجاح';
  static const String successRegister = 'تم إنشاء الحساب بنجاح';
  static const String successLogout = 'تم تسجيل الخروج بنجاح';
  static const String successUpdate = 'تم التحديث بنجاح';
  static const String successDelete = 'تم الحذف بنجاح';
  static const String successUpload = 'تم الرفع بنجاح';
  static const String successSave = 'تم الحفظ بنجاح';
  
  // نصوص عامة
  static const String loading = 'جاري التحميل...';
  static const String retry = 'إعادة المحاولة';
  static const String cancel = 'إلغاء';
  static const String confirm = 'تأكيد';
  static const String save = 'حفظ';
  static const String delete = 'حذف';
  static const String edit = 'تعديل';
  static const String share = 'مشاركة';
  static const String report = 'إبلاغ';
  static const String block = 'حظر';
  static const String unblock = 'إلغاء الحظر';
  static const String follow = 'متابعة';
  static const String unfollow = 'إلغاء المتابعة';
  static const String like = 'إعجاب';
  static const String unlike = 'إلغاء الإعجاب';
  static const String comment = 'تعليق';
  static const String reply = 'رد';
  static const String send = 'إرسال';
  static const String next = 'التالي';
  static const String previous = 'السابق';
  static const String done = 'تم';
  static const String skip = 'تخطي';
  static const String close = 'إغلاق';
  static const String ok = 'موافق';
  static const String yes = 'نعم';
  static const String no = 'لا';
}
