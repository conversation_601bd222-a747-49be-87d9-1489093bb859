// =====================================================
// نظام التسجيل (Logger)
// =====================================================

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// إنشاء مجلد logs إذا لم يكن موجوداً
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// تنسيق الرسائل
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// تنسيق رسائل وحدة التحكم
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    if (stack) {
      return `${timestamp} [${level}]: ${message}\n${stack}`;
    }
    return `${timestamp} [${level}]: ${message}`;
  })
);

// إنشاء Logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'tiktok-clone-api' },
  transports: [
    // ملف الأخطاء
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // ملف جميع الرسائل
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // ملف المعلومات
    new winston.transports.File({
      filename: path.join(logsDir, 'info.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
    })
  ],
});

// إضافة وحدة التحكم في بيئة التطوير
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// دوال مساعدة للتسجيل
const logHelpers = {
  // تسجيل طلب API
  logRequest: (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        method: req.method,
        url: req.originalUrl,
        status: res.statusCode,
        duration: `${duration}ms`,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        userId: req.user?.id || 'anonymous'
      };
      
      if (res.statusCode >= 400) {
        logger.warn('HTTP Request', logData);
      } else {
        logger.info('HTTP Request', logData);
      }
    });
    
    next();
  },
  
  // تسجيل خطأ في قاعدة البيانات
  logDatabaseError: (error, query, params = []) => {
    logger.error('Database Error', {
      error: error.message,
      stack: error.stack,
      query: query,
      params: params,
      timestamp: new Date().toISOString()
    });
  },
  
  // تسجيل نشاط المستخدم
  logUserActivity: (userId, action, details = {}) => {
    logger.info('User Activity', {
      userId,
      action,
      details,
      timestamp: new Date().toISOString()
    });
  },
  
  // تسجيل محاولة تسجيل دخول
  logAuthAttempt: (email, success, ip, userAgent) => {
    const logData = {
      email,
      success,
      ip,
      userAgent,
      timestamp: new Date().toISOString()
    };
    
    if (success) {
      logger.info('Auth Success', logData);
    } else {
      logger.warn('Auth Failed', logData);
    }
  },
  
  // تسجيل رفع ملف
  logFileUpload: (userId, filename, size, type) => {
    logger.info('File Upload', {
      userId,
      filename,
      size,
      type,
      timestamp: new Date().toISOString()
    });
  },
  
  // تسجيل خطأ في API خارجي
  logExternalApiError: (service, error, request = {}) => {
    logger.error('External API Error', {
      service,
      error: error.message,
      stack: error.stack,
      request,
      timestamp: new Date().toISOString()
    });
  },
  
  // تسجيل معاملة مالية
  logTransaction: (userId, type, amount, currency, details = {}) => {
    logger.info('Transaction', {
      userId,
      type,
      amount,
      currency,
      details,
      timestamp: new Date().toISOString()
    });
  },
  
  // تسجيل نشاط مشبوه
  logSuspiciousActivity: (userId, activity, details = {}) => {
    logger.warn('Suspicious Activity', {
      userId,
      activity,
      details,
      timestamp: new Date().toISOString()
    });
  }
};

// دمج logger مع المساعدات
Object.assign(logger, logHelpers);

module.exports = logger;
