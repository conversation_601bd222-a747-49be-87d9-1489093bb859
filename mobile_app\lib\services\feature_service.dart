// =====================================================
// خدمة الميزات الديناميكية
// =====================================================

import 'package:get/get.dart';
import '../constants/app_constants.dart';
import 'storage_service.dart';
import 'api_service.dart';

class FeatureService extends GetxService {
  static FeatureService get instance => Get.find<FeatureService>();
  
  final StorageService _storage = StorageService.instance;
  final ApiService _api = ApiService.instance;
  
  final RxMap<String, dynamic> features = <String, dynamic>{}.obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadFeatures();
  }

  // تحميل الميزات
  Future<void> loadFeatures() async {
    try {
      isLoading.value = true;
      
      // محاولة تحميل من التخزين المحلي أولاً
      final cachedFeatures = _storage.getCache<Map<String, dynamic>>('features');
      if (cachedFeatures != null) {
        features.value = cachedFeatures;
      }
      
      // تحميل من الخادم
      final response = await _api.get('/features/active');
      
      if (response.statusCode == 200) {
        final data = response.data['data']['features'] as Map<String, dynamic>;
        features.value = data;
        
        // حفظ في التخزين المؤقت لمدة ساعة
        await _storage.setCache('features', data, expiry: const Duration(hours: 1));
        
        print('تم تحميل ${data.length} ميزة');
      }
    } catch (e) {
      print('خطأ في تحميل الميزات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // التحقق من تفعيل ميزة
  bool isFeatureEnabled(String featureKey) {
    return features.containsKey(featureKey);
  }

  // الحصول على إعدادات ميزة
  Map<String, dynamic>? getFeatureSettings(String featureKey) {
    final feature = features[featureKey];
    return feature?['settings'];
  }
}
