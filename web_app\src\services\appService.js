// =====================================================
// App Service - خدمة التطبيق العامة
// =====================================================

import { store } from '../store/store';
import { loadFeatures } from '../store/slices/featureSlice';
import { getCurrentUser } from '../store/slices/authSlice';

export const initializeApp = async () => {
  try {
    console.log('🚀 بدء تهيئة التطبيق...');
    
    // تحميل الميزات
    await store.dispatch(loadFeatures());
    console.log('✅ تم تحميل الميزات');
    
    // التحقق من حالة المصادقة
    const state = store.getState();
    if (state.auth.token) {
      try {
        await store.dispatch(getCurrentUser());
        console.log('✅ تم التحقق من المستخدم');
      } catch (error) {
        console.log('⚠️ فشل في التحقق من المستخدم');
      }
    }
    
    console.log('🎉 تم تهيئة التطبيق بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة التطبيق:', error);
  }
};
