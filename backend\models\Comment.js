// =====================================================
// Comment Model - نموذج التعليق
// =====================================================

const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  video: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Video',
    required: true,
    index: true
  },
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
    default: null,
    index: true
  },
  
  // Content
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  mentions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  // Media Attachments
  imageUrl: {
    type: String
  },
  gifUrl: {
    type: String
  },
  sticker: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Sticker'
  },
  
  // Engagement
  likesCount: {
    type: Number,
    default: 0,
    min: 0,
    index: true
  },
  repliesCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Moderation
  isPinned: {
    type: Boolean,
    default: false,
    index: true
  },
  isHidden: {
    type: Boolean,
    default: false,
    index: true
  },
  isSpam: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // AI Analysis
  aiSentiment: {
    type: String,
    enum: ['positive', 'negative', 'neutral']
  },
  aiToxicityScore: {
    type: Number,
    min: 0,
    max: 1
  },
  
  // Timestamps
  editedAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Compound Indexes
commentSchema.index({ video: 1, createdAt: -1 });
commentSchema.index({ user: 1, createdAt: -1 });
commentSchema.index({ parent: 1, createdAt: 1 });
commentSchema.index({ video: 1, isPinned: -1, createdAt: -1 });

// Text search index
commentSchema.index({ content: 'text' });

// Pre-save middleware
commentSchema.pre('save', function(next) {
  // Extract mentions from content
  const mentionRegex = /@(\w+)/g;
  const mentions = [];
  let match;
  
  while ((match = mentionRegex.exec(this.content)) !== null) {
    mentions.push(match[1]);
  }
  
  // You would need to resolve usernames to user IDs here
  // For now, we'll just store the mentions array as is
  
  next();
});

// Post-save middleware to update video comment count
commentSchema.post('save', async function(doc) {
  if (this.isNew) {
    await mongoose.model('Video').findByIdAndUpdate(
      doc.video,
      { $inc: { commentsCount: 1 } }
    );
    
    // If it's a reply, increment parent comment replies count
    if (doc.parent) {
      await mongoose.model('Comment').findByIdAndUpdate(
        doc.parent,
        { $inc: { repliesCount: 1 } }
      );
    }
  }
});

// Post-remove middleware to update video comment count
commentSchema.post('remove', async function(doc) {
  await mongoose.model('Video').findByIdAndUpdate(
    doc.video,
    { $inc: { commentsCount: -1 } }
  );
  
  // If it's a reply, decrement parent comment replies count
  if (doc.parent) {
    await mongoose.model('Comment').findByIdAndUpdate(
      doc.parent,
      { $inc: { repliesCount: -1 } }
    );
  }
  
  // Remove all replies to this comment
  await mongoose.model('Comment').deleteMany({ parent: doc._id });
});

// Method to increment likes count
commentSchema.methods.incrementLikes = function() {
  this.likesCount += 1;
  return this.save();
};

// Method to decrement likes count
commentSchema.methods.decrementLikes = function() {
  if (this.likesCount > 0) {
    this.likesCount -= 1;
  }
  return this.save();
};

// Method to mark as edited
commentSchema.methods.markAsEdited = function() {
  this.editedAt = new Date();
  return this.save();
};

// Static method to get comments for a video
commentSchema.statics.getVideoComments = function(videoId, options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'recent',
    includeReplies = false
  } = options;
  
  const skip = (page - 1) * limit;
  
  let sortOptions = {};
  switch (sortBy) {
    case 'popular':
      sortOptions = { likesCount: -1, createdAt: -1 };
      break;
    case 'oldest':
      sortOptions = { createdAt: 1 };
      break;
    default:
      sortOptions = { isPinned: -1, createdAt: -1 };
  }
  
  const query = {
    video: videoId,
    isHidden: false
  };
  
  if (!includeReplies) {
    query.parent = null;
  }
  
  return this.find(query)
    .sort(sortOptions)
    .skip(skip)
    .limit(limit)
    .populate('user', 'username fullName avatarUrl isVerified')
    .populate('mentions', 'username fullName');
};

// Static method to get replies for a comment
commentSchema.statics.getCommentReplies = function(commentId, options = {}) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'oldest'
  } = options;
  
  const skip = (page - 1) * limit;
  
  let sortOptions = {};
  switch (sortBy) {
    case 'popular':
      sortOptions = { likesCount: -1, createdAt: 1 };
      break;
    default:
      sortOptions = { createdAt: 1 };
  }
  
  return this.find({
    parent: commentId,
    isHidden: false
  })
  .sort(sortOptions)
  .skip(skip)
  .limit(limit)
  .populate('user', 'username fullName avatarUrl isVerified')
  .populate('mentions', 'username fullName');
};

// Static method to search comments
commentSchema.statics.searchComments = function(query, options = {}) {
  const {
    page = 1,
    limit = 20,
    videoId = null
  } = options;
  
  const skip = (page - 1) * limit;
  
  const searchQuery = {
    $text: { $search: query },
    isHidden: false,
    isSpam: false
  };
  
  if (videoId) {
    searchQuery.video = videoId;
  }
  
  return this.find(
    searchQuery,
    { score: { $meta: 'textScore' } }
  )
  .sort({ score: { $meta: 'textScore' } })
  .skip(skip)
  .limit(limit)
  .populate('user', 'username fullName avatarUrl isVerified')
  .populate('video', 'title thumbnailUrl');
};

// Virtual for depth (how deep the reply is)
commentSchema.virtual('depth').get(function() {
  return this.parent ? 1 : 0; // Simple depth calculation
});

// Virtual for is edited
commentSchema.virtual('isEdited').get(function() {
  return !!this.editedAt;
});

module.exports = mongoose.model('Comment', commentSchema);
