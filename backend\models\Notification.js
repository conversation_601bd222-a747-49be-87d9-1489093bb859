// =====================================================
// Notification Model - نموذج الإشعار
// =====================================================

const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  
  // Notification Details
  type: {
    type: String,
    required: true,
    enum: [
      'like', 'comment', 'follow', 'mention', 'share',
      'video_approved', 'video_featured', 'live_start',
      'system', 'promotion', 'achievement', 'reminder',
      'follow_request', 'follow_accepted', 'video_trending',
      'comment_reply', 'video_duet', 'video_stitch'
    ],
    index: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 255
  },
  message: {
    type: String,
    maxlength: 1000
  },
  
  // Related Entities
  video: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Video'
  },
  comment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment'
  },
  
  // Metadata
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  actionUrl: {
    type: String,
    maxlength: 500
  },
  
  // Media
  imageUrl: {
    type: String
  },
  icon: {
    type: String,
    maxlength: 100
  },
  
  // Status
  isRead: {
    type: Boolean,
    default: false,
    index: true
  },
  isSent: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // Delivery Channels
  pushSent: {
    type: Boolean,
    default: false
  },
  emailSent: {
    type: Boolean,
    default: false
  },
  smsSent: {
    type: Boolean,
    default: false
  },
  
  // Scheduling
  scheduledAt: {
    type: Date,
    index: true
  },
  sentAt: {
    type: Date
  },
  readAt: {
    type: Date
  },
  expiresAt: {
    type: Date,
    index: true
  },
  
  // Priority
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
    index: true
  }
}, {
  timestamps: true
});

// Compound Indexes
notificationSchema.index({ user: 1, createdAt: -1 });
notificationSchema.index({ user: 1, isRead: 1, createdAt: -1 });
notificationSchema.index({ type: 1, createdAt: -1 });
notificationSchema.index({ scheduledAt: 1, isSent: 1 });
notificationSchema.index({ expiresAt: 1 });

// TTL Index for expired notifications
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  return this.save();
};

// Method to mark as sent
notificationSchema.methods.markAsSent = function() {
  this.isSent = true;
  this.sentAt = new Date();
  return this.save();
};

// Static method to create notification
notificationSchema.statics.createNotification = async function(data) {
  const {
    userId,
    senderId = null,
    type,
    title,
    message = '',
    videoId = null,
    commentId = null,
    actionUrl = null,
    imageUrl = null,
    icon = null,
    priority = 'normal',
    scheduledAt = null,
    expiresAt = null,
    additionalData = {}
  } = data;
  
  const notification = new this({
    user: userId,
    sender: senderId,
    type,
    title,
    message,
    video: videoId,
    comment: commentId,
    actionUrl,
    imageUrl,
    icon,
    priority,
    scheduledAt,
    expiresAt,
    data: additionalData
  });
  
  await notification.save();
  return notification;
};

// Static method to get user notifications
notificationSchema.statics.getUserNotifications = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    unreadOnly = false,
    type = null
  } = options;
  
  const skip = (page - 1) * limit;
  
  const query = { user: userId };
  
  if (unreadOnly) {
    query.isRead = false;
  }
  
  if (type) {
    query.type = type;
  }
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('sender', 'username fullName avatarUrl isVerified')
    .populate('video', 'title thumbnailUrl')
    .populate('comment', 'content');
};

// Static method to mark all as read
notificationSchema.statics.markAllAsRead = function(userId) {
  return this.updateMany(
    { user: userId, isRead: false },
    { 
      isRead: true,
      readAt: new Date()
    }
  );
};

// Static method to get unread count
notificationSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({
    user: userId,
    isRead: false
  });
};

// Static method to create like notification
notificationSchema.statics.createLikeNotification = async function(videoId, likerId) {
  const video = await mongoose.model('Video').findById(videoId).populate('user');
  const liker = await mongoose.model('User').findById(likerId);
  
  if (!video || !liker || video.user._id.equals(likerId)) {
    return null; // Don't notify if user likes their own video
  }
  
  return this.createNotification({
    userId: video.user._id,
    senderId: likerId,
    type: 'like',
    title: `${liker.fullName} أعجب بفيديوك`,
    message: `أعجب ${liker.fullName} بفيديوك "${video.title || 'فيديو بدون عنوان'}"`,
    videoId: videoId,
    actionUrl: `/video/${videoId}`,
    imageUrl: video.thumbnailUrl,
    icon: 'favorite'
  });
};

// Static method to create comment notification
notificationSchema.statics.createCommentNotification = async function(videoId, commenterId, commentContent) {
  const video = await mongoose.model('Video').findById(videoId).populate('user');
  const commenter = await mongoose.model('User').findById(commenterId);
  
  if (!video || !commenter || video.user._id.equals(commenterId)) {
    return null;
  }
  
  return this.createNotification({
    userId: video.user._id,
    senderId: commenterId,
    type: 'comment',
    title: `${commenter.fullName} علق على فيديوك`,
    message: `علق ${commenter.fullName}: "${commentContent.substring(0, 50)}${commentContent.length > 50 ? '...' : ''}"`,
    videoId: videoId,
    actionUrl: `/video/${videoId}`,
    imageUrl: video.thumbnailUrl,
    icon: 'comment'
  });
};

// Static method to create follow notification
notificationSchema.statics.createFollowNotification = async function(followerId, followingId) {
  const follower = await mongoose.model('User').findById(followerId);
  
  if (!follower) return null;
  
  return this.createNotification({
    userId: followingId,
    senderId: followerId,
    type: 'follow',
    title: `${follower.fullName} بدأ في متابعتك`,
    message: `${follower.fullName} (@${follower.username}) بدأ في متابعتك`,
    actionUrl: `/user/${follower.username}`,
    imageUrl: follower.avatarUrl,
    icon: 'person_add'
  });
};

// Static method to create mention notification
notificationSchema.statics.createMentionNotification = async function(mentionedUserId, mentionerId, videoId, content) {
  const mentioner = await mongoose.model('User').findById(mentionerId);
  const video = await mongoose.model('Video').findById(videoId);
  
  if (!mentioner || !video) return null;
  
  return this.createNotification({
    userId: mentionedUserId,
    senderId: mentionerId,
    type: 'mention',
    title: `${mentioner.fullName} ذكرك في فيديو`,
    message: `ذكرك ${mentioner.fullName} في فيديو: "${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`,
    videoId: videoId,
    actionUrl: `/video/${videoId}`,
    imageUrl: video.thumbnailUrl,
    icon: 'alternate_email'
  });
};

// Static method to create system notification
notificationSchema.statics.createSystemNotification = async function(userId, title, message, data = {}) {
  return this.createNotification({
    userId,
    type: 'system',
    title,
    message,
    icon: 'info',
    priority: 'high',
    additionalData: data
  });
};

// Static method to create achievement notification
notificationSchema.statics.createAchievementNotification = async function(userId, achievement) {
  return this.createNotification({
    userId,
    type: 'achievement',
    title: `إنجاز جديد: ${achievement.title}`,
    message: achievement.description,
    icon: 'emoji_events',
    priority: 'high',
    additionalData: { achievement }
  });
};

// Static method to send scheduled notifications
notificationSchema.statics.sendScheduledNotifications = async function() {
  const now = new Date();
  
  const scheduledNotifications = await this.find({
    scheduledAt: { $lte: now },
    isSent: false
  });
  
  for (const notification of scheduledNotifications) {
    // Here you would integrate with push notification service
    // For now, just mark as sent
    await notification.markAsSent();
  }
  
  return scheduledNotifications.length;
};

// Static method to cleanup expired notifications
notificationSchema.statics.cleanupExpired = function() {
  const now = new Date();
  return this.deleteMany({
    expiresAt: { $lt: now }
  });
};

// Static method to get notification statistics
notificationSchema.statics.getStats = function(userId = null) {
  const matchStage = userId ? { user: mongoose.Types.ObjectId(userId) } : {};
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        unreadCount: {
          $sum: { $cond: [{ $eq: ['$isRead', false] }, 1, 0] }
        },
        sentCount: {
          $sum: { $cond: [{ $eq: ['$isSent', true] }, 1, 0] }
        }
      }
    },
    {
      $group: {
        _id: null,
        totalNotifications: { $sum: '$count' },
        totalUnread: { $sum: '$unreadCount' },
        totalSent: { $sum: '$sentCount' },
        typeBreakdown: {
          $push: {
            type: '$_id',
            count: '$count',
            unreadCount: '$unreadCount',
            sentCount: '$sentCount'
          }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Notification', notificationSchema);
