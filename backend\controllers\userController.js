// =====================================================
// User Controller - متحكم المستخدم
// =====================================================

const User = require('../models/User');
const Follow = require('../models/Follow');
const Video = require('../models/Video');
const { validationResult } = require('express-validator');
const { createResponse, createErrorResponse } = require('../utils/response');
const { uploadImage } = require('../utils/imageProcessor');

class UserController {
  // الحصول على ملف المستخدم
  async getUserProfile(req, res) {
    try {
      const { identifier } = req.params; // username or id
      const currentUserId = req.user?._id;

      let user;
      
      // البحث بالمعرف أو اسم المستخدم
      if (identifier.match(/^[0-9a-fA-F]{24}$/)) {
        user = await User.findById(identifier);
      } else {
        user = await User.findOne({ username: identifier });
      }

      if (!user) {
        return res.status(404).json(
          createErrorResponse('المستخدم غير موجود')
        );
      }

      // التحقق من حالة المتابعة
      let isFollowing = false;
      let isFollowedBy = false;
      
      if (currentUserId && !currentUserId.equals(user._id)) {
        isFollowing = !!(await Follow.isFollowing(currentUserId, user._id));
        isFollowedBy = !!(await Follow.isFollowing(user._id, currentUserId));
      }

      const userProfile = user.toJSON();
      userProfile.isFollowing = isFollowing;
      userProfile.isFollowedBy = isFollowedBy;
      userProfile.isOwnProfile = currentUserId ? currentUserId.equals(user._id) : false;

      res.json(
        createResponse('تم جلب ملف المستخدم بنجاح', {
          user: userProfile
        })
      );
    } catch (error) {
      console.error('Get user profile error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // تحديث الملف الشخصي
  async updateProfile(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          createErrorResponse('بيانات غير صحيحة', errors.array())
        );
      }

      const userId = req.user._id;
      const {
        fullName,
        bio,
        website,
        location,
        birthDate,
        gender,
        language,
        isPrivate
      } = req.body;

      const updateData = {};
      
      if (fullName !== undefined) updateData.fullName = fullName;
      if (bio !== undefined) updateData.bio = bio;
      if (website !== undefined) updateData.website = website;
      if (location !== undefined) updateData.location = location;
      if (birthDate !== undefined) updateData.birthDate = birthDate;
      if (gender !== undefined) updateData.gender = gender;
      if (language !== undefined) updateData.language = language;
      if (isPrivate !== undefined) updateData.isPrivate = isPrivate;

      // رفع الصورة الشخصية إذا تم توفيرها
      if (req.files && req.files.avatar) {
        const avatarResult = await uploadImage(req.files.avatar, 'avatars');
        updateData.avatarUrl = avatarResult.url;
      }

      // رفع صورة الغلاف إذا تم توفيرها
      if (req.files && req.files.cover) {
        const coverResult = await uploadImage(req.files.cover, 'covers');
        updateData.coverUrl = coverResult.url;
      }

      const user = await User.findByIdAndUpdate(
        userId,
        updateData,
        { new: true, runValidators: true }
      );

      res.json(
        createResponse('تم تحديث الملف الشخصي بنجاح', {
          user: user.toJSON()
        })
      );
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في تحديث الملف الشخصي')
      );
    }
  }

  // متابعة مستخدم
  async followUser(req, res) {
    try {
      const { userId } = req.params;
      const followerId = req.user._id;

      if (followerId.equals(userId)) {
        return res.status(400).json(
          createErrorResponse('لا يمكن متابعة نفسك')
        );
      }

      const targetUser = await User.findById(userId);
      if (!targetUser) {
        return res.status(404).json(
          createErrorResponse('المستخدم غير موجود')
        );
      }

      const follow = await Follow.followUser(followerId, userId);

      res.json(
        createResponse(
          follow.status === 'pending' ? 'تم إرسال طلب المتابعة' : 'تم متابعة المستخدم بنجاح',
          {
            status: follow.status,
            followersCount: targetUser.followersCount + (follow.status === 'accepted' ? 1 : 0)
          }
        )
      );
    } catch (error) {
      console.error('Follow user error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في متابعة المستخدم')
      );
    }
  }

  // إلغاء متابعة مستخدم
  async unfollowUser(req, res) {
    try {
      const { userId } = req.params;
      const followerId = req.user._id;

      const targetUser = await User.findById(userId);
      if (!targetUser) {
        return res.status(404).json(
          createErrorResponse('المستخدم غير موجود')
        );
      }

      const unfollowed = await Follow.unfollowUser(followerId, userId);

      if (!unfollowed) {
        return res.status(400).json(
          createErrorResponse('لم تكن تتابع هذا المستخدم')
        );
      }

      res.json(
        createResponse('تم إلغاء متابعة المستخدم بنجاح', {
          followersCount: Math.max(0, targetUser.followersCount - 1)
        })
      );
    } catch (error) {
      console.error('Unfollow user error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في إلغاء المتابعة')
      );
    }
  }

  // الحصول على المتابعين
  async getFollowers(req, res) {
    try {
      const { userId } = req.params;
      const {
        page = 1,
        limit = 20
      } = req.query;

      const followers = await Follow.getFollowers(userId, {
        page: parseInt(page),
        limit: parseInt(limit)
      });

      const total = await Follow.countDocuments({
        following: userId,
        status: 'accepted'
      });

      res.json(
        createResponse('تم جلب المتابعين بنجاح', {
          followers: followers.map(f => f.follower),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Get followers error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // الحصول على المتابَعين
  async getFollowing(req, res) {
    try {
      const { userId } = req.params;
      const {
        page = 1,
        limit = 20
      } = req.query;

      const following = await Follow.getFollowing(userId, {
        page: parseInt(page),
        limit: parseInt(limit)
      });

      const total = await Follow.countDocuments({
        follower: userId,
        status: 'accepted'
      });

      res.json(
        createResponse('تم جلب المتابَعين بنجاح', {
          following: following.map(f => f.following),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Get following error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // البحث عن المستخدمين
  async searchUsers(req, res) {
    try {
      const {
        q: query,
        page = 1,
        limit = 20
      } = req.query;

      if (!query) {
        return res.status(400).json(
          createErrorResponse('نص البحث مطلوب')
        );
      }

      const skip = (page - 1) * limit;

      const users = await User.find({
        $or: [
          { username: { $regex: query, $options: 'i' } },
          { fullName: { $regex: query, $options: 'i' } }
        ],
        isActive: true
      })
      .select('username fullName avatarUrl isVerified followersCount')
      .sort({ followersCount: -1, isVerified: -1 })
      .skip(skip)
      .limit(parseInt(limit));

      const total = await User.countDocuments({
        $or: [
          { username: { $regex: query, $options: 'i' } },
          { fullName: { $regex: query, $options: 'i' } }
        ],
        isActive: true
      });

      res.json(
        createResponse('تم البحث بنجاح', {
          users,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Search users error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في البحث')
      );
    }
  }

  // اقتراحات المتابعة
  async getFollowSuggestions(req, res) {
    try {
      const userId = req.user._id;
      const { limit = 10 } = req.query;

      const suggestions = await Follow.getFollowSuggestions(userId, parseInt(limit));

      res.json(
        createResponse('تم جلب اقتراحات المتابعة بنجاح', {
          suggestions
        })
      );
    } catch (error) {
      console.error('Get follow suggestions error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // الحصول على طلبات المتابعة المعلقة
  async getPendingFollowRequests(req, res) {
    try {
      const userId = req.user._id;
      const {
        page = 1,
        limit = 20
      } = req.query;

      const requests = await Follow.getPendingRequests(userId, {
        page: parseInt(page),
        limit: parseInt(limit)
      });

      const total = await Follow.countDocuments({
        following: userId,
        status: 'pending'
      });

      res.json(
        createResponse('تم جلب طلبات المتابعة بنجاح', {
          requests: requests.map(r => r.follower),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Get pending follow requests error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // قبول طلب المتابعة
  async acceptFollowRequest(req, res) {
    try {
      const { followerId } = req.params;
      const userId = req.user._id;

      const follow = await Follow.findOne({
        follower: followerId,
        following: userId,
        status: 'pending'
      });

      if (!follow) {
        return res.status(404).json(
          createErrorResponse('طلب المتابعة غير موجود')
        );
      }

      await follow.accept();

      res.json(
        createResponse('تم قبول طلب المتابعة بنجاح')
      );
    } catch (error) {
      console.error('Accept follow request error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // رفض طلب المتابعة
  async rejectFollowRequest(req, res) {
    try {
      const { followerId } = req.params;
      const userId = req.user._id;

      const follow = await Follow.findOne({
        follower: followerId,
        following: userId,
        status: 'pending'
      });

      if (!follow) {
        return res.status(404).json(
          createErrorResponse('طلب المتابعة غير موجود')
        );
      }

      await follow.remove();

      res.json(
        createResponse('تم رفض طلب المتابعة بنجاح')
      );
    } catch (error) {
      console.error('Reject follow request error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // حظر مستخدم
  async blockUser(req, res) {
    try {
      const { userId } = req.params;
      const currentUserId = req.user._id;

      if (currentUserId.equals(userId)) {
        return res.status(400).json(
          createErrorResponse('لا يمكن حظر نفسك')
        );
      }

      // إزالة المتابعة المتبادلة
      await Follow.deleteMany({
        $or: [
          { follower: currentUserId, following: userId },
          { follower: userId, following: currentUserId }
        ]
      });

      // هنا يمكن إضافة نموذج Block منفصل
      // للبساطة، سنستخدم Follow مع status = 'blocked'
      const existingBlock = await Follow.findOne({
        follower: currentUserId,
        following: userId
      });

      if (!existingBlock) {
        const block = new Follow({
          follower: currentUserId,
          following: userId,
          status: 'blocked'
        });
        await block.save();
      } else {
        await existingBlock.block();
      }

      res.json(
        createResponse('تم حظر المستخدم بنجاح')
      );
    } catch (error) {
      console.error('Block user error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // إلغاء حظر مستخدم
  async unblockUser(req, res) {
    try {
      const { userId } = req.params;
      const currentUserId = req.user._id;

      await Follow.deleteOne({
        follower: currentUserId,
        following: userId,
        status: 'blocked'
      });

      res.json(
        createResponse('تم إلغاء حظر المستخدم بنجاح')
      );
    } catch (error) {
      console.error('Unblock user error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }
}

module.exports = new UserController();
