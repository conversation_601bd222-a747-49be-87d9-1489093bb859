// =====================================================
// مسارات الميزات الديناميكية
// =====================================================

const express = require('express');
const Joi = require('joi');
const { query } = require('../../database/config/database');
const { verifyToken, requireRole, optionalAuth } = require('../middleware/auth');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// مخططات التحقق من البيانات
const updateFeatureSchema = Joi.object({
  status: Joi.number().integer().min(0).max(1).optional(),
  settings: Joi.object().optional()
});

const createFeatureSchema = Joi.object({
  feature_key: Joi.string().required(),
  name_ar: Joi.string().required(),
  name_en: Joi.string().required(),
  description_ar: Joi.string().optional(),
  description_en: Joi.string().optional(),
  status: Joi.number().integer().min(0).max(1).default(1),
  settings: Joi.object().optional()
});

// =====================================================
// الحصول على جميع الميزات (عامة)
// =====================================================
router.get('/', optionalAuth, async (req, res, next) => {
  try {
    const { status, category, search } = req.query;
    
    let whereClause = '1=1';
    const params = [];
    
    // فلترة حسب الحالة
    if (status !== undefined) {
      whereClause += ' AND status = ?';
      params.push(parseInt(status));
    }
    
    // البحث في الاسم أو الوصف
    if (search) {
      whereClause += ' AND (name_ar LIKE ? OR name_en LIKE ? OR description_ar LIKE ? OR description_en LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }
    
    const features = await query(
      `SELECT id, feature_key, name_ar, name_en, description_ar, description_en, 
              status, settings, created_at, updated_at 
       FROM features 
       WHERE ${whereClause} 
       ORDER BY id ASC`,
      params
    );
    
    // إحصائيات الميزات
    const stats = await query(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive
      FROM features
    `);
    
    res.json({
      success: true,
      data: {
        features,
        stats: stats[0],
        total: features.length
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// الحصول على الميزات المفعلة فقط
// =====================================================
router.get('/active', async (req, res, next) => {
  try {
    const activeFeatures = await query(
      `SELECT feature_key, name_ar, name_en, settings 
       FROM features 
       WHERE status = 1 
       ORDER BY id ASC`
    );
    
    // تحويل إلى كائن للوصول السريع
    const featuresMap = {};
    activeFeatures.forEach(feature => {
      featuresMap[feature.feature_key] = {
        name_ar: feature.name_ar,
        name_en: feature.name_en,
        settings: feature.settings
      };
    });
    
    res.json({
      success: true,
      data: {
        features: featuresMap,
        count: activeFeatures.length
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// الحصول على ميزة محددة
// =====================================================
router.get('/:featureKey', async (req, res, next) => {
  try {
    const { featureKey } = req.params;
    
    const features = await query(
      'SELECT * FROM features WHERE feature_key = ?',
      [featureKey]
    );
    
    if (features.length === 0) {
      throw new NotFoundError('الميزة غير موجودة');
    }
    
    res.json({
      success: true,
      data: {
        feature: features[0]
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// التحقق من حالة ميزة
// =====================================================
router.get('/:featureKey/status', async (req, res, next) => {
  try {
    const { featureKey } = req.params;
    
    const features = await query(
      'SELECT status, settings FROM features WHERE feature_key = ?',
      [featureKey]
    );
    
    if (features.length === 0) {
      throw new NotFoundError('الميزة غير موجودة');
    }
    
    const feature = features[0];
    
    res.json({
      success: true,
      data: {
        feature_key: featureKey,
        enabled: feature.status === 1,
        settings: feature.settings
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// إنشاء ميزة جديدة (للمدراء فقط)
// =====================================================
router.post('/', verifyToken, requireRole('admin', 'super_admin'), async (req, res, next) => {
  try {
    // التحقق من صحة البيانات
    const { error, value } = createFeatureSchema.validate(req.body);
    if (error) {
      throw new ValidationError(error.details[0].message);
    }
    
    const { feature_key, name_ar, name_en, description_ar, description_en, status, settings } = value;
    
    // التحقق من عدم وجود الميزة مسبقاً
    const existingFeature = await query(
      'SELECT id FROM features WHERE feature_key = ?',
      [feature_key]
    );
    
    if (existingFeature.length > 0) {
      throw new ValidationError('مفتاح الميزة موجود مسبقاً');
    }
    
    // إنشاء الميزة
    const result = await query(
      `INSERT INTO features (feature_key, name_ar, name_en, description_ar, description_en, status, settings) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [feature_key, name_ar, name_en, description_ar, description_en, status, JSON.stringify(settings)]
    );
    
    // الحصول على الميزة المنشأة
    const newFeature = await query(
      'SELECT * FROM features WHERE id = ?',
      [result.insertId]
    );
    
    // تسجيل النشاط
    logger.logUserActivity(req.user.id, 'feature_created', { feature_key, name_ar });
    
    res.status(201).json({
      success: true,
      message: 'تم إنشاء الميزة بنجاح',
      data: {
        feature: newFeature[0]
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// تحديث ميزة (للمدراء فقط)
// =====================================================
router.put('/:featureKey', verifyToken, requireRole('admin', 'super_admin'), async (req, res, next) => {
  try {
    const { featureKey } = req.params;
    
    // التحقق من صحة البيانات
    const { error, value } = updateFeatureSchema.validate(req.body);
    if (error) {
      throw new ValidationError(error.details[0].message);
    }
    
    // التحقق من وجود الميزة
    const existingFeature = await query(
      'SELECT * FROM features WHERE feature_key = ?',
      [featureKey]
    );
    
    if (existingFeature.length === 0) {
      throw new NotFoundError('الميزة غير موجودة');
    }
    
    const updateFields = [];
    const updateValues = [];
    
    if (value.status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(value.status);
    }
    
    if (value.settings !== undefined) {
      updateFields.push('settings = ?');
      updateValues.push(JSON.stringify(value.settings));
    }
    
    if (updateFields.length === 0) {
      throw new ValidationError('لا توجد حقول للتحديث');
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(featureKey);
    
    // تحديث الميزة
    await query(
      `UPDATE features SET ${updateFields.join(', ')} WHERE feature_key = ?`,
      updateValues
    );
    
    // الحصول على الميزة المحدثة
    const updatedFeature = await query(
      'SELECT * FROM features WHERE feature_key = ?',
      [featureKey]
    );
    
    // تسجيل النشاط
    logger.logUserActivity(req.user.id, 'feature_updated', { 
      feature_key: featureKey, 
      changes: value 
    });
    
    res.json({
      success: true,
      message: 'تم تحديث الميزة بنجاح',
      data: {
        feature: updatedFeature[0]
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// تفعيل/تعطيل ميزة (للمدراء فقط)
// =====================================================
router.patch('/:featureKey/toggle', verifyToken, requireRole('admin', 'super_admin'), async (req, res, next) => {
  try {
    const { featureKey } = req.params;
    
    // الحصول على حالة الميزة الحالية
    const features = await query(
      'SELECT status FROM features WHERE feature_key = ?',
      [featureKey]
    );
    
    if (features.length === 0) {
      throw new NotFoundError('الميزة غير موجودة');
    }
    
    const currentStatus = features[0].status;
    const newStatus = currentStatus === 1 ? 0 : 1;
    
    // تحديث حالة الميزة
    await query(
      'UPDATE features SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE feature_key = ?',
      [newStatus, featureKey]
    );
    
    // تسجيل النشاط
    const action = newStatus === 1 ? 'feature_enabled' : 'feature_disabled';
    logger.logUserActivity(req.user.id, action, { feature_key: featureKey });
    
    res.json({
      success: true,
      message: `تم ${newStatus === 1 ? 'تفعيل' : 'تعطيل'} الميزة بنجاح`,
      data: {
        feature_key: featureKey,
        status: newStatus,
        enabled: newStatus === 1
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// حذف ميزة (للمدراء العليا فقط)
// =====================================================
router.delete('/:featureKey', verifyToken, requireRole('super_admin'), async (req, res, next) => {
  try {
    const { featureKey } = req.params;
    
    // التحقق من وجود الميزة
    const existingFeature = await query(
      'SELECT * FROM features WHERE feature_key = ?',
      [featureKey]
    );
    
    if (existingFeature.length === 0) {
      throw new NotFoundError('الميزة غير موجودة');
    }
    
    // حذف الميزة
    await query(
      'DELETE FROM features WHERE feature_key = ?',
      [featureKey]
    );
    
    // تسجيل النشاط
    logger.logUserActivity(req.user.id, 'feature_deleted', { 
      feature_key: featureKey,
      feature_name: existingFeature[0].name_ar
    });
    
    res.json({
      success: true,
      message: 'تم حذف الميزة بنجاح'
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// إحصائيات الميزات (للمدراء فقط)
// =====================================================
router.get('/admin/stats', verifyToken, requireRole('admin', 'super_admin'), async (req, res, next) => {
  try {
    const stats = await query(`
      SELECT 
        COUNT(*) as total_features,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_features,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive_features,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_features_last_month
      FROM features
    `);
    
    const recentUpdates = await query(`
      SELECT feature_key, name_ar, status, updated_at
      FROM features 
      ORDER BY updated_at DESC 
      LIMIT 10
    `);
    
    res.json({
      success: true,
      data: {
        stats: stats[0],
        recent_updates: recentUpdates
      }
    });
    
  } catch (error) {
    next(error);
  }
});

module.exports = router;
