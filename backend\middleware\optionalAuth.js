// =====================================================
// Optional Authentication Middleware - وسطاء المصادقة الاختيارية
// =====================================================

const jwt = require('jsonwebtoken');
const User = require('../models/User');

// وسطاء المصادقة الاختيارية
const optionalAuth = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    // إذا لم يكن هناك رمز، متابعة بدون مستخدم
    if (!token) {
      req.user = null;
      req.isAuthenticated = false;
      return next();
    }

    // محاولة التحقق من صحة الرمز
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // البحث عن المستخدم
      const user = await User.findById(decoded.userId);
      
      // التحقق من وجود المستخدم وحالته
      if (user && user.isActive && !user.isBanned) {
        req.user = user;
        req.token = token;
        req.isAuthenticated = true;
      } else {
        req.user = null;
        req.isAuthenticated = false;
      }
    } catch (jwtError) {
      // في حالة خطأ JWT، متابعة بدون مستخدم
      req.user = null;
      req.isAuthenticated = false;
    }
    
    next();
  } catch (error) {
    // في حالة أي خطأ آخر، متابعة بدون مستخدم
    console.error('Optional auth middleware error:', error);
    req.user = null;
    req.isAuthenticated = false;
    next();
  }
};

// دالة استخراج الرمز من الطلب
const extractToken = (req) => {
  // من الهيدر Authorization
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // من الكوكيز
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }

  // من query parameter
  if (req.query && req.query.token) {
    return req.query.token;
  }

  // من الهيدر x-access-token
  if (req.headers['x-access-token']) {
    return req.headers['x-access-token'];
  }

  return null;
};

// وسطاء للتحقق من المصادقة مع إرجاع معلومات إضافية
const optionalAuthWithInfo = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    // معلومات المصادقة الافتراضية
    req.authInfo = {
      isAuthenticated: false,
      user: null,
      token: null,
      tokenExpiry: null,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      timestamp: new Date()
    };
    
    if (!token) {
      return next();
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId);
      
      if (user && user.isActive && !user.isBanned) {
        req.user = user;
        req.token = token;
        req.isAuthenticated = true;
        
        // تحديث معلومات المصادقة
        req.authInfo = {
          isAuthenticated: true,
          user: user.toJSON(),
          token: token,
          tokenExpiry: new Date(decoded.exp * 1000),
          userAgent: req.get('User-Agent'),
          ip: req.ip,
          timestamp: new Date(),
          userId: user._id,
          roles: user.roles || ['user'],
          permissions: user.permissions || []
        };
      }
    } catch (jwtError) {
      // معلومات خطأ JWT
      req.authInfo.error = {
        type: 'JWT_ERROR',
        message: jwtError.message,
        name: jwtError.name
      };
    }
    
    next();
  } catch (error) {
    console.error('Optional auth with info middleware error:', error);
    req.authInfo.error = {
      type: 'MIDDLEWARE_ERROR',
      message: error.message
    };
    next();
  }
};

// وسطاء للتحقق من الصلاحيات الاختيارية
const optionalPermission = (permission) => {
  return (req, res, next) => {
    // إضافة معلومات الصلاحية
    req.hasPermission = false;
    req.checkedPermission = permission;
    
    if (!req.user) {
      return next();
    }

    const userPermissions = req.user.permissions || [];
    const userRoles = req.user.roles || ['user'];

    // التحقق من الصلاحية المباشرة
    if (userPermissions.includes(permission)) {
      req.hasPermission = true;
      return next();
    }

    // التحقق من صلاحيات الأدوار
    const rolePermissions = {
      'super_admin': ['*'],
      'admin': [
        'users.read', 'users.write', 'users.delete',
        'videos.read', 'videos.write', 'videos.delete', 'videos.moderate',
        'comments.read', 'comments.write', 'comments.delete', 'comments.moderate',
        'reports.read', 'reports.write', 'reports.resolve',
        'analytics.read'
      ],
      'moderator': [
        'videos.read', 'videos.moderate',
        'comments.read', 'comments.moderate',
        'reports.read', 'reports.resolve'
      ],
      'creator': [
        'videos.read', 'videos.write',
        'comments.read', 'comments.write',
        'analytics.read'
      ],
      'user': [
        'videos.read',
        'comments.read', 'comments.write'
      ]
    };

    const hasPermission = userRoles.some(role => {
      const permissions = rolePermissions[role] || [];
      return permissions.includes('*') || permissions.includes(permission);
    });

    req.hasPermission = hasPermission;
    next();
  };
};

// وسطاء للتحقق من الأدوار الاختيارية
const optionalRole = (roles) => {
  return (req, res, next) => {
    // إضافة معلومات الدور
    req.hasRole = false;
    req.checkedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!req.user) {
      return next();
    }

    const userRoles = req.user.roles || ['user'];
    const hasRole = req.checkedRoles.some(role => userRoles.includes(role));

    req.hasRole = hasRole;
    next();
  };
};

// وسطاء للتحقق من ملكية المورد الاختيارية
const optionalOwnership = (getResourceUserId) => {
  return async (req, res, next) => {
    // إضافة معلومات الملكية
    req.isOwner = false;
    req.resourceUserId = null;
    
    try {
      if (!req.user) {
        return next();
      }

      const resourceUserId = await getResourceUserId(req);
      req.resourceUserId = resourceUserId;
      
      if (resourceUserId) {
        req.isOwner = req.user._id.equals(resourceUserId);
      }
    } catch (error) {
      console.error('Optional ownership middleware error:', error);
    }
    
    next();
  };
};

// وسطاء لتسجيل النشاط الاختياري
const optionalActivityLog = (action) => {
  return (req, res, next) => {
    // إضافة معلومات النشاط
    req.activityInfo = {
      action: action,
      timestamp: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      method: req.method,
      userId: req.user?._id || null,
      isAuthenticated: !!req.user
    };
    
    // تسجيل النشاط إذا كان المستخدم مصادق عليه
    if (req.user) {
      // يمكن إضافة تسجيل النشاط في قاعدة البيانات هنا
      console.log(`User activity: ${req.user._id} performed ${action} on ${req.path}`);
    }
    
    next();
  };
};

// وسطاء للتحقق من حالة المستخدم
const checkUserStatus = (req, res, next) => {
  if (!req.user) {
    return next();
  }

  // إضافة معلومات حالة المستخدم
  req.userStatus = {
    isActive: req.user.isActive,
    isBanned: req.user.isBanned,
    isVerified: req.user.emailVerifiedAt ? true : false,
    is2FAEnabled: req.user.twoFactorEnabled || false,
    lastLoginAt: req.user.lastLoginAt,
    accountAge: req.user.createdAt ? Date.now() - req.user.createdAt.getTime() : 0
  };

  // تحديث آخر نشاط
  if (req.user.isActive && !req.user.isBanned) {
    req.user.lastActiveAt = new Date();
    req.user.save().catch(err => console.error('Error updating last active:', err));
  }

  next();
};

module.exports = {
  optionalAuth,
  optionalAuthWithInfo,
  optionalPermission,
  optionalRole,
  optionalOwnership,
  optionalActivityLog,
  checkUserStatus,
  extractToken
};
