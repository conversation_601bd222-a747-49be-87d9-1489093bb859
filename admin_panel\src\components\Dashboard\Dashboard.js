// =====================================================
// Admin Dashboard Component - مكون لوحة التحكم الإدارية
// =====================================================

import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  IconButton,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  TrendingUp,
  People,
  VideoLibrary,
  Comment,
  Favorite,
  Visibility,
  Warning,
  CheckCircle,
  Schedule,
  MoreVert,
  Refresh
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useDispatch, useSelector } from 'react-redux';
import { fetchDashboardStats } from '../../store/slices/adminSlice';

// Styled Components
const StatsCard = styled(Card)(({ theme, color = 'primary' }) => ({
  background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,
  color: 'white',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '100px',
    height: '100px',
    background: `radial-gradient(circle, ${alpha(theme.palette.common.white, 0.1)} 0%, transparent 70%)`,
    transform: 'translate(30px, -30px)',
  },
}));

const MetricBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 60,
  height: 60,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.common.white, 0.2),
}));

const Dashboard = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { dashboardStats, loading } = useSelector(state => state.admin);

  const [timeRange, setTimeRange] = useState('7d');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(fetchDashboardStats({ timeRange }));
  }, [dispatch, timeRange]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchDashboardStats({ timeRange }));
    setRefreshing(false);
  };

  // Sample data - في التطبيق الحقيقي سيأتي من الخادم
  const stats = dashboardStats || {
    totalUsers: 15420,
    totalVideos: 8934,
    totalComments: 45678,
    totalLikes: 234567,
    totalViews: 1234567,
    pendingReports: 23,
    activeUsers: 3456,
    newUsersToday: 89,
    videosUploadedToday: 156,
    revenueToday: 2340,
  };

  const chartData = [
    { name: 'يناير', users: 4000, videos: 2400, revenue: 2400 },
    { name: 'فبراير', users: 3000, videos: 1398, revenue: 2210 },
    { name: 'مارس', users: 2000, videos: 9800, revenue: 2290 },
    { name: 'أبريل', users: 2780, videos: 3908, revenue: 2000 },
    { name: 'مايو', users: 1890, videos: 4800, revenue: 2181 },
    { name: 'يونيو', users: 2390, videos: 3800, revenue: 2500 },
    { name: 'يوليو', users: 3490, videos: 4300, revenue: 2100 },
  ];

  const pieData = [
    { name: 'فيديوهات عامة', value: 65, color: theme.palette.primary.main },
    { name: 'فيديوهات خاصة', value: 25, color: theme.palette.secondary.main },
    { name: 'فيديوهات محذوفة', value: 10, color: theme.palette.error.main },
  ];

  const recentActivities = [
    {
      id: 1,
      user: 'أحمد محمد',
      action: 'رفع فيديو جديد',
      time: 'منذ 5 دقائق',
      avatar: '/avatars/user1.jpg',
      type: 'video'
    },
    {
      id: 2,
      user: 'فاطمة علي',
      action: 'تسجيل حساب جديد',
      time: 'منذ 15 دقيقة',
      avatar: '/avatars/user2.jpg',
      type: 'user'
    },
    {
      id: 3,
      user: 'محمد أحمد',
      action: 'إبلاغ عن محتوى',
      time: 'منذ 30 دقيقة',
      avatar: '/avatars/user3.jpg',
      type: 'report'
    },
    {
      id: 4,
      user: 'سارة محمود',
      action: 'تعليق على فيديو',
      time: 'منذ ساعة',
      avatar: '/avatars/user4.jpg',
      type: 'comment'
    },
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'video': return <VideoLibrary color="primary" />;
      case 'user': return <People color="success" />;
      case 'report': return <Warning color="error" />;
      case 'comment': return <Comment color="info" />;
      default: return <CheckCircle />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          لوحة التحكم
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
          </IconButton>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard color="primary">
            <MetricBox>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  {stats.totalUsers.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  إجمالي المستخدمين
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.6 }}>
                  +{stats.newUsersToday} اليوم
                </Typography>
              </Box>
              <IconWrapper>
                <People sx={{ fontSize: 30 }} />
              </IconWrapper>
            </MetricBox>
          </StatsCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard color="secondary">
            <MetricBox>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  {stats.totalVideos.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  إجمالي الفيديوهات
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.6 }}>
                  +{stats.videosUploadedToday} اليوم
                </Typography>
              </Box>
              <IconWrapper>
                <VideoLibrary sx={{ fontSize: 30 }} />
              </IconWrapper>
            </MetricBox>
          </StatsCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard color="success">
            <MetricBox>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  {stats.totalViews.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  إجمالي المشاهدات
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.6 }}>
                  +12% هذا الأسبوع
                </Typography>
              </Box>
              <IconWrapper>
                <Visibility sx={{ fontSize: 30 }} />
              </IconWrapper>
            </MetricBox>
          </StatsCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard color="error">
            <MetricBox>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  {stats.pendingReports}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  التقارير المعلقة
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.6 }}>
                  تحتاج مراجعة
                </Typography>
              </Box>
              <IconWrapper>
                <Warning sx={{ fontSize: 30 }} />
              </IconWrapper>
            </MetricBox>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Line Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              إحصائيات النمو الشهرية
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="users" 
                  stroke={theme.palette.primary.main} 
                  strokeWidth={3}
                  name="المستخدمين"
                />
                <Line 
                  type="monotone" 
                  dataKey="videos" 
                  stroke={theme.palette.secondary.main} 
                  strokeWidth={3}
                  name="الفيديوهات"
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Pie Chart */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              توزيع الفيديوهات
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Activities and Quick Stats */}
      <Grid container spacing={3}>
        {/* Recent Activities */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              النشاطات الأخيرة
            </Typography>
            <List>
              {recentActivities.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar src={activity.avatar} alt={activity.user}>
                        {activity.user.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={activity.user}
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getActivityIcon(activity.type)}
                          <Typography variant="body2">
                            {activity.action}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            • {activity.time}
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton size="small">
                        <MoreVert />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < recentActivities.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              إحصائيات سريعة
            </Typography>
            <Box sx={{ space: 2 }}>
              {/* Active Users */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">المستخدمين النشطين</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.activeUsers.toLocaleString()}
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={75} 
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              {/* Storage Usage */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">استخدام التخزين</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    2.4TB / 5TB
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={48} 
                  color="warning"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              {/* Bandwidth Usage */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">استخدام النطاق الترددي</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    890GB / 2TB
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={44} 
                  color="info"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              {/* Server Load */}
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">حمولة الخادم</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    32%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={32} 
                  color="success"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Loading Overlay */}
      {loading && (
        <Box sx={{ position: 'fixed', top: 0, left: 0, right: 0, zIndex: 9999 }}>
          <LinearProgress />
        </Box>
      )}
    </Box>
  );
};

export default Dashboard;
