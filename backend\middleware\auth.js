// =====================================================
// Authentication Middleware - وسطاء المصادقة
// =====================================================

const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { createErrorResponse } = require('../utils/response');

// وسطاء المصادقة الإجبارية
const auth = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return res.status(401).json(
        createErrorResponse('رمز المصادقة مطلوب')
      );
    }

    // التحقق من صحة الرمز
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // البحث عن المستخدم
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json(
        createErrorResponse('المستخدم غير موجود')
      );
    }

    // التحقق من حالة المستخدم
    if (!user.isActive) {
      return res.status(401).json(
        createErrorResponse('الحساب معطل')
      );
    }

    if (user.isBanned) {
      return res.status(401).json(
        createErrorResponse('الحساب محظور')
      );
    }

    // إضافة المستخدم إلى الطلب
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json(
        createErrorResponse('رمز المصادقة غير صحيح')
      );
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json(
        createErrorResponse('رمز المصادقة منتهي الصلاحية')
      );
    }

    console.error('Auth middleware error:', error);
    return res.status(500).json(
      createErrorResponse('خطأ في المصادقة')
    );
  }
};

// وسطاء المصادقة الاختيارية
const optionalAuth = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return next(); // لا يوجد رمز، متابعة بدون مستخدم
    }

    // التحقق من صحة الرمز
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // البحث عن المستخدم
    const user = await User.findById(decoded.userId);
    if (user && user.isActive && !user.isBanned) {
      req.user = user;
      req.token = token;
    }
    
    next();
  } catch (error) {
    // في حالة الخطأ، متابعة بدون مستخدم
    next();
  }
};

// وسطاء التحقق من الأدوار
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(
        createErrorResponse('المصادقة مطلوبة')
      );
    }

    const userRoles = req.user.roles || ['user'];
    const hasRole = roles.some(role => userRoles.includes(role));

    if (!hasRole) {
      return res.status(403).json(
        createErrorResponse('ليس لديك صلاحية للوصول لهذا المورد')
      );
    }

    next();
  };
};

// وسطاء التحقق من الصلاحيات
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(
        createErrorResponse('المصادقة مطلوبة')
      );
    }

    const userPermissions = req.user.permissions || [];
    const userRoles = req.user.roles || ['user'];

    // التحقق من الصلاحية المباشرة
    if (userPermissions.includes(permission)) {
      return next();
    }

    // التحقق من صلاحيات الأدوار
    const rolePermissions = {
      'super_admin': ['*'], // جميع الصلاحيات
      'admin': [
        'users.read', 'users.write', 'users.delete',
        'videos.read', 'videos.write', 'videos.delete', 'videos.moderate',
        'comments.read', 'comments.write', 'comments.delete', 'comments.moderate',
        'reports.read', 'reports.write', 'reports.resolve',
        'analytics.read'
      ],
      'moderator': [
        'videos.read', 'videos.moderate',
        'comments.read', 'comments.moderate',
        'reports.read', 'reports.resolve'
      ],
      'creator': [
        'videos.read', 'videos.write',
        'comments.read', 'comments.write',
        'analytics.read'
      ],
      'user': [
        'videos.read',
        'comments.read', 'comments.write'
      ]
    };

    const hasPermission = userRoles.some(role => {
      const permissions = rolePermissions[role] || [];
      return permissions.includes('*') || permissions.includes(permission);
    });

    if (!hasPermission) {
      return res.status(403).json(
        createErrorResponse('ليس لديك صلاحية لتنفيذ هذا الإجراء')
      );
    }

    next();
  };
};

// وسطاء التحقق من ملكية المورد
const requireOwnership = (getResourceUserId) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json(
          createErrorResponse('المصادقة مطلوبة')
        );
      }

      const resourceUserId = await getResourceUserId(req);
      
      if (!resourceUserId) {
        return res.status(404).json(
          createErrorResponse('المورد غير موجود')
        );
      }

      // التحقق من الملكية أو الصلاحيات الإدارية
      const isOwner = req.user._id.equals(resourceUserId);
      const isAdmin = req.user.roles.includes('admin') || req.user.roles.includes('super_admin');

      if (!isOwner && !isAdmin) {
        return res.status(403).json(
          createErrorResponse('ليس لديك صلاحية للوصول لهذا المورد')
        );
      }

      next();
    } catch (error) {
      console.error('Ownership middleware error:', error);
      return res.status(500).json(
        createErrorResponse('خطأ في التحقق من الملكية')
      );
    }
  };
};

// وسطاء التحقق من التحقق من البريد الإلكتروني
const requireEmailVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json(
      createErrorResponse('المصادقة مطلوبة')
    );
  }

  if (!req.user.emailVerifiedAt) {
    return res.status(403).json(
      createErrorResponse('يجب تأكيد البريد الإلكتروني أولاً')
    );
  }

  next();
};

// وسطاء التحقق من المصادقة الثنائية
const require2FA = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json(
      createErrorResponse('المصادقة مطلوبة')
    );
  }

  if (req.user.twoFactorEnabled && !req.user.twoFactorVerified) {
    return res.status(403).json(
      createErrorResponse('المصادقة الثنائية مطلوبة')
    );
  }

  next();
};

// دالة استخراج الرمز من الطلب
const extractToken = (req) => {
  // من الهيدر Authorization
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // من الكوكيز
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }

  // من query parameter
  if (req.query && req.query.token) {
    return req.query.token;
  }

  return null;
};

// دالة التحقق من انتهاء صلاحية الرمز
const isTokenExpired = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) {
      return true;
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
};

// دالة إنشاء رمز مؤقت للعمليات الحساسة
const createTemporaryToken = (userId, action, expiresIn = '15m') => {
  return jwt.sign(
    { 
      userId, 
      action, 
      type: 'temporary',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn }
  );
};

// دالة التحقق من الرمز المؤقت
const verifyTemporaryToken = (token, expectedAction) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'temporary' || decoded.action !== expectedAction) {
      return null;
    }
    
    return decoded;
  } catch (error) {
    return null;
  }
};

module.exports = {
  auth,
  optionalAuth,
  requireRole,
  requirePermission,
  requireOwnership,
  requireEmailVerification,
  require2FA,
  extractToken,
  isTokenExpired,
  createTemporaryToken,
  verifyTemporaryToken
};
