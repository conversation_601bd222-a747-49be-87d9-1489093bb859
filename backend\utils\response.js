// =====================================================
// Response Utilities - أدوات الاستجابة
// =====================================================

// إنشاء استجابة نجاح موحدة
const createResponse = (message, data = null, meta = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };

  if (data !== null) {
    response.data = data;
  }

  if (meta !== null) {
    response.meta = meta;
  }

  return response;
};

// إنشاء استجابة خطأ موحدة
const createErrorResponse = (message, errors = null, code = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  if (errors !== null) {
    response.errors = errors;
  }

  if (code !== null) {
    response.code = code;
  }

  return response;
};

// إنشاء استجابة مع ترقيم الصفحات
const createPaginatedResponse = (message, data, pagination) => {
  return createResponse(message, data, { pagination });
};

// إنشاء استجابة للقوائم
const createListResponse = (message, items, total, page = 1, limit = 20) => {
  const pagination = {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    pages: Math.ceil(total / limit),
    hasNext: page < Math.ceil(total / limit),
    hasPrev: page > 1
  };

  return createPaginatedResponse(message, items, pagination);
};

// إنشاء استجابة للبحث
const createSearchResponse = (message, results, query, total, page = 1, limit = 20) => {
  const pagination = {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    pages: Math.ceil(total / limit),
    hasNext: page < Math.ceil(total / limit),
    hasPrev: page > 1
  };

  const meta = {
    query,
    pagination
  };

  return createResponse(message, results, meta);
};

// إنشاء استجابة للإحصائيات
const createStatsResponse = (message, stats, period = null) => {
  const meta = {
    generatedAt: new Date().toISOString()
  };

  if (period) {
    meta.period = period;
  }

  return createResponse(message, stats, meta);
};

// إنشاء استجابة للملفات المرفوعة
const createUploadResponse = (message, fileInfo) => {
  const data = {
    file: {
      url: fileInfo.url,
      filename: fileInfo.filename,
      originalName: fileInfo.originalName,
      size: fileInfo.size,
      mimeType: fileInfo.mimeType,
      uploadedAt: new Date().toISOString()
    }
  };

  return createResponse(message, data);
};

// إنشاء استجابة للعمليات المجمعة
const createBatchResponse = (message, results) => {
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  const meta = {
    total: results.length,
    successful,
    failed,
    successRate: (successful / results.length) * 100
  };

  return createResponse(message, results, meta);
};

// إنشاء استجابة للتحقق من صحة البيانات
const createValidationErrorResponse = (errors) => {
  const formattedErrors = {};

  if (Array.isArray(errors)) {
    errors.forEach(error => {
      const field = error.param || error.path || 'general';
      if (!formattedErrors[field]) {
        formattedErrors[field] = [];
      }
      formattedErrors[field].push(error.msg || error.message);
    });
  } else if (typeof errors === 'object') {
    Object.keys(errors).forEach(field => {
      formattedErrors[field] = Array.isArray(errors[field]) 
        ? errors[field] 
        : [errors[field]];
    });
  }

  return createErrorResponse('بيانات غير صحيحة', formattedErrors, 'VALIDATION_ERROR');
};

// إنشاء استجابة للأخطاء المخصصة
const createCustomErrorResponse = (message, code, details = null) => {
  const response = createErrorResponse(message, null, code);
  
  if (details) {
    response.details = details;
  }

  return response;
};

// إنشاء استجابة للحالة
const createStatusResponse = (status, message, data = null) => {
  return {
    status,
    message,
    data,
    timestamp: new Date().toISOString()
  };
};

// إنشاء استجابة للإشعارات
const createNotificationResponse = (type, title, message, data = null) => {
  return {
    notification: {
      type, // success, info, warning, error
      title,
      message,
      data,
      timestamp: new Date().toISOString()
    }
  };
};

// إنشاء استجابة للتقدم
const createProgressResponse = (message, progress, total = 100) => {
  const percentage = Math.round((progress / total) * 100);
  
  return {
    success: true,
    message,
    progress: {
      current: progress,
      total,
      percentage,
      completed: percentage === 100
    },
    timestamp: new Date().toISOString()
  };
};

// إنشاء استجابة للكاش
const createCachedResponse = (data, cacheInfo) => {
  return {
    ...data,
    cache: {
      hit: true,
      generatedAt: cacheInfo.generatedAt,
      expiresAt: cacheInfo.expiresAt,
      ttl: cacheInfo.ttl
    }
  };
};

// إنشاء استجابة للتصدير
const createExportResponse = (message, exportInfo) => {
  return createResponse(message, {
    export: {
      id: exportInfo.id,
      format: exportInfo.format,
      downloadUrl: exportInfo.downloadUrl,
      expiresAt: exportInfo.expiresAt,
      size: exportInfo.size,
      recordsCount: exportInfo.recordsCount
    }
  });
};

// إنشاء استجابة للاستيراد
const createImportResponse = (message, importInfo) => {
  return createResponse(message, {
    import: {
      id: importInfo.id,
      status: importInfo.status,
      processedRecords: importInfo.processedRecords,
      totalRecords: importInfo.totalRecords,
      successfulRecords: importInfo.successfulRecords,
      failedRecords: importInfo.failedRecords,
      errors: importInfo.errors || []
    }
  });
};

// إنشاء استجابة للصحة
const createHealthResponse = (status, checks = {}) => {
  return {
    status, // healthy, unhealthy, degraded
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.APP_VERSION || '1.0.0',
    checks
  };
};

// إنشاء استجابة للمعلومات
const createInfoResponse = (info) => {
  return {
    info: {
      ...info,
      timestamp: new Date().toISOString()
    }
  };
};

// إنشاء استجابة للتحديثات المباشرة
const createLiveUpdateResponse = (type, data) => {
  return {
    type, // update, delete, create
    data,
    timestamp: new Date().toISOString()
  };
};

// إنشاء استجابة للأحداث
const createEventResponse = (event, data) => {
  return {
    event,
    data,
    timestamp: new Date().toISOString(),
    id: require('crypto').randomUUID()
  };
};

// معالج الأخطاء العام
const handleError = (error, req = null) => {
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req?.url,
    method: req?.method,
    timestamp: new Date().toISOString()
  });

  // أخطاء قاعدة البيانات
  if (error.name === 'ValidationError') {
    return createValidationErrorResponse(Object.values(error.errors));
  }

  if (error.name === 'CastError') {
    return createErrorResponse('معرف غير صحيح', null, 'INVALID_ID');
  }

  if (error.code === 11000) {
    return createErrorResponse('البيانات موجودة بالفعل', null, 'DUPLICATE_ENTRY');
  }

  // أخطاء JWT
  if (error.name === 'JsonWebTokenError') {
    return createErrorResponse('رمز المصادقة غير صحيح', null, 'INVALID_TOKEN');
  }

  if (error.name === 'TokenExpiredError') {
    return createErrorResponse('رمز المصادقة منتهي الصلاحية', null, 'TOKEN_EXPIRED');
  }

  // خطأ عام
  return createErrorResponse(
    process.env.NODE_ENV === 'production' 
      ? 'حدث خطأ في الخادم' 
      : error.message,
    null,
    'INTERNAL_ERROR'
  );
};

module.exports = {
  createResponse,
  createErrorResponse,
  createPaginatedResponse,
  createListResponse,
  createSearchResponse,
  createStatsResponse,
  createUploadResponse,
  createBatchResponse,
  createValidationErrorResponse,
  createCustomErrorResponse,
  createStatusResponse,
  createNotificationResponse,
  createProgressResponse,
  createCachedResponse,
  createExportResponse,
  createImportResponse,
  createHealthResponse,
  createInfoResponse,
  createLiveUpdateResponse,
  createEventResponse,
  handleError
};
