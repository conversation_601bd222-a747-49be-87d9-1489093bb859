// =====================================================
// هجرة إنشاء الجداول الأساسية
// =====================================================

const fs = require('fs').promises;
const path = require('path');
const { query } = require('../config/database');

const migration = {
  name: '001_create_tables',
  description: 'إنشاء جميع الجداول الأساسية للمشروع',
  
  // تنفيذ الهجرة
  up: async () => {
    try {
      console.log('🚀 بدء تنفيذ هجرة إنشاء الجداول...');
      
      // قراءة ملف SQL الخاص بالمخطط
      const schemaPath = path.join(__dirname, '../schema/database_schema.sql');
      const schemaSql = await fs.readFile(schemaPath, 'utf8');
      
      // تقسيم الاستعلامات
      const queries = schemaSql
        .split(';')
        .map(q => q.trim())
        .filter(q => q.length > 0 && !q.startsWith('--') && !q.startsWith('/*'));
      
      // تنفيذ كل استعلام
      for (let i = 0; i < queries.length; i++) {
        const sql = queries[i];
        if (sql.trim()) {
          try {
            await query(sql);
            console.log(`✅ تم تنفيذ الاستعلام ${i + 1}/${queries.length}`);
          } catch (error) {
            if (!error.message.includes('already exists')) {
              throw error;
            }
            console.log(`⚠️ الجدول موجود مسبقاً - الاستعلام ${i + 1}`);
          }
        }
      }
      
      console.log('✅ تم إنشاء جميع الجداول بنجاح');
      return true;
      
    } catch (error) {
      console.error('❌ خطأ في إنشاء الجداول:', error);
      throw error;
    }
  },
  
  // التراجع عن الهجرة
  down: async () => {
    try {
      console.log('🔄 بدء التراجع عن هجرة الجداول...');
      
      // قائمة الجداول للحذف (بالترتيب العكسي لتجنب مشاكل المفاتيح الخارجية)
      const tables = [
        'notifications',
        'reports', 
        'coin_transactions',
        'user_coins',
        'gifts',
        'challenges',
        'hashtags',
        'music',
        'live_streams',
        'messages',
        'comments',
        'likes',
        'follows',
        'videos',
        'users',
        'features'
      ];
      
      // حذف الجداول
      for (const table of tables) {
        try {
          await query(`DROP TABLE IF EXISTS ${table}`);
          console.log(`✅ تم حذف الجدول: ${table}`);
        } catch (error) {
          console.error(`❌ خطأ في حذف الجدول ${table}:`, error);
        }
      }
      
      console.log('✅ تم التراجع عن الهجرة بنجاح');
      return true;
      
    } catch (error) {
      console.error('❌ خطأ في التراجع عن الهجرة:', error);
      throw error;
    }
  }
};

module.exports = migration;
