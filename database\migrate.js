#!/usr/bin/env node

// =====================================================
// أداة إدارة الهجرات والبذور
// =====================================================

const fs = require('fs').promises;
const path = require('path');
const { createPool, testConnection, closePool } = require('./config/database');

class DatabaseManager {
  constructor() {
    this.migrationsPath = path.join(__dirname, 'migrations');
    this.seedersPath = path.join(__dirname, 'seeds');
  }

  // تشغيل جميع الهجرات
  async runMigrations() {
    try {
      console.log('🚀 بدء تشغيل الهجرات...\n');
      
      // إنشاء pool والاتصال
      createPool();
      const connected = await testConnection();
      if (!connected) {
        throw new Error('فشل الاتصال بقاعدة البيانات');
      }

      // قراءة ملفات الهجرات
      const migrationFiles = await fs.readdir(this.migrationsPath);
      const migrations = migrationFiles
        .filter(file => file.endsWith('.js'))
        .sort();

      if (migrations.length === 0) {
        console.log('⚠️ لا توجد هجرات للتشغيل');
        return;
      }

      // تشغيل كل هجرة
      for (const migrationFile of migrations) {
        console.log(`📁 تشغيل الهجرة: ${migrationFile}`);
        const migrationPath = path.join(this.migrationsPath, migrationFile);
        const migration = require(migrationPath);
        
        await migration.up();
        console.log(`✅ تم تشغيل الهجرة: ${migration.name}\n`);
      }

      console.log('🎉 تم تشغيل جميع الهجرات بنجاح!');
      
    } catch (error) {
      console.error('❌ خطأ في تشغيل الهجرات:', error);
      throw error;
    }
  }

  // التراجع عن الهجرات
  async rollbackMigrations() {
    try {
      console.log('🔄 بدء التراجع عن الهجرات...\n');
      
      createPool();
      const connected = await testConnection();
      if (!connected) {
        throw new Error('فشل الاتصال بقاعدة البيانات');
      }

      // قراءة ملفات الهجرات بالترتيب العكسي
      const migrationFiles = await fs.readdir(this.migrationsPath);
      const migrations = migrationFiles
        .filter(file => file.endsWith('.js'))
        .sort()
        .reverse();

      // التراجع عن كل هجرة
      for (const migrationFile of migrations) {
        console.log(`📁 التراجع عن الهجرة: ${migrationFile}`);
        const migrationPath = path.join(this.migrationsPath, migrationFile);
        const migration = require(migrationPath);
        
        if (migration.down) {
          await migration.down();
          console.log(`✅ تم التراجع عن الهجرة: ${migration.name}\n`);
        }
      }

      console.log('🎉 تم التراجع عن جميع الهجرات بنجاح!');
      
    } catch (error) {
      console.error('❌ خطأ في التراجع عن الهجرات:', error);
      throw error;
    }
  }

  // تشغيل البذور
  async runSeeders() {
    try {
      console.log('🌱 بدء تشغيل البذور...\n');
      
      createPool();
      const connected = await testConnection();
      if (!connected) {
        throw new Error('فشل الاتصال بقاعدة البيانات');
      }

      // قراءة ملفات البذور
      const seederFiles = await fs.readdir(this.seedersPath);
      const seeders = seederFiles
        .filter(file => file.endsWith('.js'))
        .sort();

      if (seeders.length === 0) {
        console.log('⚠️ لا توجد بذور للتشغيل');
        return;
      }

      // تشغيل كل بذرة
      for (const seederFile of seeders) {
        console.log(`📁 تشغيل البذرة: ${seederFile}`);
        const seederPath = path.join(this.seedersPath, seederFile);
        const seeder = require(seederPath);
        
        await seeder.run();
        console.log(`✅ تم تشغيل البذرة: ${seeder.name}\n`);
      }

      console.log('🎉 تم تشغيل جميع البذور بنجاح!');
      
    } catch (error) {
      console.error('❌ خطأ في تشغيل البذور:', error);
      throw error;
    }
  }

  // إعادة تعيين قاعدة البيانات
  async resetDatabase() {
    try {
      console.log('🔄 بدء إعادة تعيين قاعدة البيانات...\n');
      
      await this.rollbackMigrations();
      await this.runMigrations();
      await this.runSeeders();
      
      console.log('🎉 تم إعادة تعيين قاعدة البيانات بنجاح!');
      
    } catch (error) {
      console.error('❌ خطأ في إعادة تعيين قاعدة البيانات:', error);
      throw error;
    }
  }

  // إغلاق الاتصال
  async close() {
    await closePool();
  }
}

// تشغيل الأداة من سطر الأوامر
async function main() {
  const dbManager = new DatabaseManager();
  const command = process.argv[2];

  try {
    switch (command) {
      case 'migrate':
        await dbManager.runMigrations();
        break;
      
      case 'rollback':
        await dbManager.rollbackMigrations();
        break;
      
      case 'seed':
        await dbManager.runSeeders();
        break;
      
      case 'reset':
        await dbManager.resetDatabase();
        break;
      
      default:
        console.log(`
📚 أداة إدارة قاعدة البيانات

الاستخدام:
  node migrate.js <command>

الأوامر المتاحة:
  migrate   - تشغيل جميع الهجرات
  rollback  - التراجع عن جميع الهجرات
  seed      - تشغيل جميع البذور
  reset     - إعادة تعيين قاعدة البيانات (rollback + migrate + seed)

أمثلة:
  node migrate.js migrate
  node migrate.js seed
  node migrate.js reset
        `);
        break;
    }
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    process.exit(1);
  } finally {
    await dbManager.close();
    process.exit(0);
  }
}

// تشغيل الأداة إذا تم استدعاؤها مباشرة
if (require.main === module) {
  main();
}

module.exports = DatabaseManager;
