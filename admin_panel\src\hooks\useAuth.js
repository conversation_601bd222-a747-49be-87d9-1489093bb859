// =====================================================
// useAuth Hook - هوك المصادقة للإدارة
// =====================================================

import { useState, useEffect } from 'react';

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  useEffect(() => {
    // محاكاة التحقق من المصادقة
    setTimeout(() => {
      setIsAuthenticated(false); // تغيير إلى true للاختبار
      setUser({
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        roles: ['admin']
      });
      setIsLoading(false);
    }, 1000);
  }, []);

  const logout = () => {
    setIsAuthenticated(false);
    setUser(null);
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    logout
  };
};
