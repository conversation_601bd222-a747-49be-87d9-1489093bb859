#!/bin/bash

# =====================================================
# TikTok Clone Monitoring Script - سكريبت المراقبة
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="tiktok-clone"
LOG_FILE="/var/log/tiktok-clone/monitoring.log"
ALERT_EMAIL=${ALERT_EMAIL:-"<EMAIL>"}
SLACK_WEBHOOK=${SLACK_WEBHOOK:-""}
CHECK_INTERVAL=${CHECK_INTERVAL:-60}  # seconds

# Service URLs
BACKEND_URL=${BACKEND_URL:-"http://localhost:5000"}
WEB_APP_URL=${WEB_APP_URL:-"http://localhost:3000"}
ADMIN_PANEL_URL=${ADMIN_PANEL_URL:-"http://localhost:3001"}

# Database Configuration
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"27017"}
REDIS_HOST=${REDIS_HOST:-"localhost"}
REDIS_PORT=${REDIS_PORT:-"6379"}

# Thresholds
CPU_THRESHOLD=${CPU_THRESHOLD:-80}
MEMORY_THRESHOLD=${MEMORY_THRESHOLD:-85}
DISK_THRESHOLD=${DISK_THRESHOLD:-90}
RESPONSE_TIME_THRESHOLD=${RESPONSE_TIME_THRESHOLD:-5000}  # milliseconds

# Functions
log_info() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${BLUE}$message${NC}"
    echo "$message" >> "$LOG_FILE" 2>/dev/null || true
}

log_success() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS] $1"
    echo -e "${GREEN}$message${NC}"
    echo "$message" >> "$LOG_FILE" 2>/dev/null || true
}

log_warning() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING] $1"
    echo -e "${YELLOW}$message${NC}"
    echo "$message" >> "$LOG_FILE" 2>/dev/null || true
}

log_error() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$message${NC}"
    echo "$message" >> "$LOG_FILE" 2>/dev/null || true
}

log_critical() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [CRITICAL] $1"
    echo -e "${RED}🚨 $message 🚨${NC}"
    echo "$message" >> "$LOG_FILE" 2>/dev/null || true
}

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")" 2>/dev/null || true

check_service_health() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    log_info "Checking $service_name health..."
    
    # Check if service is responding
    local response_time=$(curl -o /dev/null -s -w "%{time_total}" -m 10 "$url/health" 2>/dev/null || echo "timeout")
    local http_code=$(curl -o /dev/null -s -w "%{http_code}" -m 10 "$url/health" 2>/dev/null || echo "000")
    
    if [ "$http_code" = "$expected_status" ]; then
        local response_ms=$(echo "$response_time * 1000" | bc 2>/dev/null || echo "0")
        log_success "$service_name is healthy (${response_ms}ms)"
        
        # Check response time
        if (( $(echo "$response_ms > $RESPONSE_TIME_THRESHOLD" | bc -l 2>/dev/null || echo 0) )); then
            log_warning "$service_name response time is high: ${response_ms}ms"
            send_alert "WARNING" "$service_name response time is high: ${response_ms}ms"
        fi
        
        return 0
    else
        log_error "$service_name is not healthy (HTTP $http_code)"
        send_alert "CRITICAL" "$service_name is not responding (HTTP $http_code)"
        return 1
    fi
}

check_database_health() {
    log_info "Checking MongoDB health..."
    
    # Check MongoDB connection
    if command -v mongosh &> /dev/null; then
        local mongo_status=$(mongosh --host "$DB_HOST:$DB_PORT" --eval "db.adminCommand('ping').ok" --quiet 2>/dev/null || echo "0")
        if [ "$mongo_status" = "1" ]; then
            log_success "MongoDB is healthy"
            
            # Check database size and connections
            local db_stats=$(mongosh --host "$DB_HOST:$DB_PORT" --eval "
                var stats = db.stats();
                var serverStatus = db.serverStatus();
                print(JSON.stringify({
                    dataSize: stats.dataSize,
                    indexSize: stats.indexSize,
                    connections: serverStatus.connections.current
                }));
            " --quiet 2>/dev/null || echo "{}")
            
            log_info "Database stats: $db_stats"
            return 0
        else
            log_error "MongoDB is not responding"
            send_alert "CRITICAL" "MongoDB is not responding"
            return 1
        fi
    else
        log_warning "mongosh not available, skipping detailed MongoDB check"
        return 0
    fi
}

check_redis_health() {
    log_info "Checking Redis health..."
    
    if command -v redis-cli &> /dev/null; then
        local redis_response=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping 2>/dev/null || echo "ERROR")
        if [ "$redis_response" = "PONG" ]; then
            log_success "Redis is healthy"
            
            # Check Redis memory usage
            local redis_info=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" info memory 2>/dev/null || echo "")
            if [ -n "$redis_info" ]; then
                local used_memory=$(echo "$redis_info" | grep "used_memory_human:" | cut -d: -f2 | tr -d '\r')
                log_info "Redis memory usage: $used_memory"
            fi
            return 0
        else
            log_error "Redis is not responding"
            send_alert "CRITICAL" "Redis is not responding"
            return 1
        fi
    else
        log_warning "redis-cli not available, skipping Redis check"
        return 0
    fi
}

check_system_resources() {
    log_info "Checking system resources..."
    
    # Check CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d',' -f1 2>/dev/null || echo "0")
    cpu_usage=${cpu_usage%.*}  # Remove decimal part
    
    if [ "$cpu_usage" -gt "$CPU_THRESHOLD" ]; then
        log_warning "High CPU usage: ${cpu_usage}%"
        send_alert "WARNING" "High CPU usage: ${cpu_usage}%"
    else
        log_success "CPU usage is normal: ${cpu_usage}%"
    fi
    
    # Check memory usage
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    
    if [ "$memory_usage" -gt "$MEMORY_THRESHOLD" ]; then
        log_warning "High memory usage: ${memory_usage}%"
        send_alert "WARNING" "High memory usage: ${memory_usage}%"
    else
        log_success "Memory usage is normal: ${memory_usage}%"
    fi
    
    # Check disk usage
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1)
    
    if [ "$disk_usage" -gt "$DISK_THRESHOLD" ]; then
        log_warning "High disk usage: ${disk_usage}%"
        send_alert "WARNING" "High disk usage: ${disk_usage}%"
    else
        log_success "Disk usage is normal: ${disk_usage}%"
    fi
    
    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
    local cpu_cores=$(nproc)
    local load_percentage=$(echo "scale=2; $load_avg / $cpu_cores * 100" | bc 2>/dev/null || echo "0")
    
    log_info "System load: $load_avg (${load_percentage}% of $cpu_cores cores)"
    
    if (( $(echo "$load_percentage > 80" | bc -l 2>/dev/null || echo 0) )); then
        log_warning "High system load: $load_avg"
        send_alert "WARNING" "High system load: $load_avg"
    fi
}

check_docker_containers() {
    if ! command -v docker &> /dev/null; then
        log_warning "Docker not available, skipping container checks"
        return
    fi
    
    log_info "Checking Docker containers..."
    
    # List of expected containers
    local expected_containers=("tiktok-clone-backend" "tiktok-clone-web" "tiktok-clone-admin" "mongodb" "redis")
    
    for container in "${expected_containers[@]}"; do
        local container_status=$(docker ps --filter "name=$container" --format "{{.Status}}" 2>/dev/null || echo "Not found")
        
        if [[ "$container_status" == *"Up"* ]]; then
            log_success "Container $container is running"
            
            # Check container resource usage
            local container_stats=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" "$container" 2>/dev/null || echo "")
            if [ -n "$container_stats" ]; then
                log_info "Container stats: $container_stats"
            fi
        else
            log_error "Container $container is not running: $container_status"
            send_alert "CRITICAL" "Container $container is not running"
        fi
    done
}

check_ssl_certificates() {
    log_info "Checking SSL certificates..."
    
    # List of domains to check
    local domains=("your-domain.com" "api.your-domain.com" "admin.your-domain.com")
    
    for domain in "${domains[@]}"; do
        if command -v openssl &> /dev/null; then
            local cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "")
            
            if [ -n "$cert_info" ]; then
                local expiry_date=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
                local expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null || echo "0")
                local current_timestamp=$(date +%s)
                local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
                
                if [ "$days_until_expiry" -lt 30 ]; then
                    log_warning "SSL certificate for $domain expires in $days_until_expiry days"
                    send_alert "WARNING" "SSL certificate for $domain expires in $days_until_expiry days"
                else
                    log_success "SSL certificate for $domain is valid ($days_until_expiry days remaining)"
                fi
            else
                log_warning "Could not check SSL certificate for $domain"
            fi
        else
            log_warning "OpenSSL not available, skipping SSL certificate checks"
            break
        fi
    done
}

check_log_errors() {
    log_info "Checking for recent errors in logs..."
    
    # List of log files to check
    local log_files=(
        "/var/log/tiktok-clone/backend.log"
        "/var/log/tiktok-clone/web.log"
        "/var/log/tiktok-clone/admin.log"
        "/var/log/nginx/error.log"
    )
    
    local error_count=0
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            # Check for errors in the last 5 minutes
            local recent_errors=$(grep -i "error\|critical\|fatal" "$log_file" | grep "$(date '+%Y-%m-%d %H:%M' -d '5 minutes ago')" | wc -l 2>/dev/null || echo "0")
            
            if [ "$recent_errors" -gt 0 ]; then
                log_warning "Found $recent_errors recent errors in $log_file"
                error_count=$((error_count + recent_errors))
            fi
        fi
    done
    
    if [ "$error_count" -gt 10 ]; then
        log_error "High number of recent errors: $error_count"
        send_alert "CRITICAL" "High number of recent errors: $error_count"
    elif [ "$error_count" -gt 0 ]; then
        log_warning "Found $error_count recent errors in logs"
    else
        log_success "No recent errors found in logs"
    fi
}

send_alert() {
    local severity=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Email alert
    if [ -n "$ALERT_EMAIL" ] && command -v mail &> /dev/null; then
        echo "TikTok Clone Alert - $severity

Time: $timestamp
Message: $message

This is an automated alert from the TikTok Clone monitoring system." | mail -s "[$severity] TikTok Clone Alert" "$ALERT_EMAIL"
    fi
    
    # Slack alert
    if [ -n "$SLACK_WEBHOOK" ]; then
        local color="warning"
        case "$severity" in
            "CRITICAL") color="danger" ;;
            "WARNING") color="warning" ;;
            "INFO") color="good" ;;
        esac
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"TikTok Clone Alert - $severity\",
                    \"text\": \"$message\",
                    \"footer\": \"TikTok Clone Monitoring\",
                    \"ts\": $(date +%s)
                }]
            }" \
            "$SLACK_WEBHOOK" > /dev/null 2>&1
    fi
}

generate_health_report() {
    log_info "Generating health report..."
    
    local report_file="/tmp/tiktok-clone-health-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
TikTok Clone Health Report
==========================
Generated: $(date)
Hostname: $(hostname)

System Information:
- OS: $(uname -a)
- Uptime: $(uptime)
- Load Average: $(uptime | awk -F'load average:' '{print $2}')

Resource Usage:
- CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%
- Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')
- Disk: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')

Service Status:
EOF
    
    # Check each service and add to report
    echo "- Backend API: $(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/health" 2>/dev/null || echo "ERROR")" >> "$report_file"
    echo "- Web Application: $(curl -s -o /dev/null -w "%{http_code}" "$WEB_APP_URL" 2>/dev/null || echo "ERROR")" >> "$report_file"
    echo "- Admin Panel: $(curl -s -o /dev/null -w "%{http_code}" "$ADMIN_PANEL_URL" 2>/dev/null || echo "ERROR")" >> "$report_file"
    
    # Add database status
    if command -v mongosh &> /dev/null; then
        local mongo_status=$(mongosh --host "$DB_HOST:$DB_PORT" --eval "db.adminCommand('ping').ok" --quiet 2>/dev/null || echo "0")
        echo "- MongoDB: $([ "$mongo_status" = "1" ] && echo "OK" || echo "ERROR")" >> "$report_file"
    fi
    
    if command -v redis-cli &> /dev/null; then
        local redis_status=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping 2>/dev/null || echo "ERROR")
        echo "- Redis: $([ "$redis_status" = "PONG" ] && echo "OK" || echo "ERROR")" >> "$report_file"
    fi
    
    echo "" >> "$report_file"
    echo "Report generated by TikTok Clone monitoring system" >> "$report_file"
    
    log_success "Health report generated: $report_file"
    echo "$report_file"
}

# Main monitoring functions
run_health_checks() {
    log_info "Starting health checks..."
    
    local start_time=$(date +%s)
    local failed_checks=0
    
    # Service health checks
    check_service_health "Backend API" "$BACKEND_URL" || ((failed_checks++))
    check_service_health "Web Application" "$WEB_APP_URL" || ((failed_checks++))
    check_service_health "Admin Panel" "$ADMIN_PANEL_URL" || ((failed_checks++))
    
    # Database checks
    check_database_health || ((failed_checks++))
    check_redis_health || ((failed_checks++))
    
    # System checks
    check_system_resources
    check_docker_containers
    check_ssl_certificates
    check_log_errors
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ "$failed_checks" -eq 0 ]; then
        log_success "All health checks passed ($duration seconds)"
    else
        log_error "$failed_checks health checks failed ($duration seconds)"
        send_alert "CRITICAL" "$failed_checks health checks failed"
    fi
    
    return $failed_checks
}

continuous_monitoring() {
    log_info "Starting continuous monitoring (interval: ${CHECK_INTERVAL}s)..."
    
    while true; do
        run_health_checks
        log_info "Waiting $CHECK_INTERVAL seconds until next check..."
        sleep "$CHECK_INTERVAL"
    done
}

# Handle script arguments
case "${1:-check}" in
    "check")
        run_health_checks
        ;;
    "monitor")
        continuous_monitoring
        ;;
    "report")
        generate_health_report
        ;;
    "test-alert")
        send_alert "INFO" "This is a test alert from TikTok Clone monitoring system"
        log_info "Test alert sent"
        ;;
    *)
        echo "Usage: $0 [check|monitor|report|test-alert]"
        echo "  check      - Run health checks once (default)"
        echo "  monitor    - Run continuous monitoring"
        echo "  report     - Generate health report"
        echo "  test-alert - Send test alert"
        echo ""
        echo "Environment variables:"
        echo "  CHECK_INTERVAL     - Monitoring interval in seconds (default: 60)"
        echo "  ALERT_EMAIL        - Email for alerts"
        echo "  SLACK_WEBHOOK      - Slack webhook URL for alerts"
        echo "  CPU_THRESHOLD      - CPU usage threshold % (default: 80)"
        echo "  MEMORY_THRESHOLD   - Memory usage threshold % (default: 85)"
        echo "  DISK_THRESHOLD     - Disk usage threshold % (default: 90)"
        exit 1
        ;;
esac
