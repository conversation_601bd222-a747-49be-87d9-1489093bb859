// =====================================================
// مسارات الرسائل
// =====================================================

const express = require('express');
const router = express.Router();

// TODO: تطوير مسارات الرسائل
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'مسارات الرسائل قيد التطوير',
    endpoints: [
      'GET /messages - الحصول على الرسائل',
      'POST /messages - إرسال رسالة',
      'GET /messages/:id - الحصول على رسالة محددة',
      'PUT /messages/:id - تحديث رسالة',
      'DELETE /messages/:id - حذف رسالة',
      'POST /messages/:id/read - تعليم كمقروءة'
    ]
  });
});

module.exports = router;
