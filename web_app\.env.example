# =====================================================
# Environment Variables - TikTok Clone Web App
# =====================================================

# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_SOCKET_URL=http://localhost:5000
REACT_APP_WS_URL=ws://localhost:5000

# App Configuration
REACT_APP_NAME="TikTok Clone"
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION="TikTok Clone - Revolutionary Social Media Platform"

# Features
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_LIVE_STREAMING=true
REACT_APP_ENABLE_MESSAGING=true

# Social Media Integration
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
REACT_APP_FACEBOOK_APP_ID=your_facebook_app_id
REACT_APP_APPLE_CLIENT_ID=your_apple_client_id

# Analytics
REACT_APP_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
REACT_APP_MIXPANEL_TOKEN=your_mixpanel_token
REACT_APP_HOTJAR_ID=your_hotjar_id

# Firebase (for push notifications)
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789
REACT_APP_FIREBASE_APP_ID=1:123456789:web:abcdef123456
REACT_APP_FIREBASE_VAPID_KEY=your_vapid_key

# CDN & Assets
REACT_APP_CDN_URL=https://cdn.tiktokclone.com
REACT_APP_ASSETS_URL=https://assets.tiktokclone.com

# Development
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=info

# Build Configuration
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false

# Performance
REACT_APP_LAZY_LOADING=true
REACT_APP_IMAGE_OPTIMIZATION=true
REACT_APP_CACHE_DURATION=3600

# Security
REACT_APP_CSP_ENABLED=true
REACT_APP_HTTPS_ONLY=false
