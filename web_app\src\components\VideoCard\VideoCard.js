// =====================================================
// Video Card Component - مكون بطاقة الفيديو
// =====================================================

import React, { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Avatar,
  Box,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Badge,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  MoreVert,
  Bookmark,
  BookmarkBorder,
  Report,
  Link as LinkIcon,
  Download,
  Visibility,
  AccessTime
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { toggleVideoLike, addToFavorites, removeFromFavorites } from '../../store/slices/videoSlice';
import VideoPlayer from '../VideoPlayer/VideoPlayer';

// Styled Components
const StyledCard = styled(Card)(({ theme }) => ({
  position: 'relative',
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

const VideoOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(transparent 60%, rgba(0,0,0,0.8))',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-end',
  padding: theme.spacing(2),
  opacity: 0,
  transition: 'opacity 0.3s ease',
  '&.visible': {
    opacity: 1,
  },
}));

const PlayButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  backgroundColor: 'rgba(255,255,255,0.9)',
  color: theme.palette.primary.main,
  width: 64,
  height: 64,
  '&:hover': {
    backgroundColor: 'rgba(255,255,255,1)',
    transform: 'translate(-50%, -50%) scale(1.1)',
  },
}));

const StatsChip = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  backgroundColor: 'rgba(0,0,0,0.7)',
  color: 'white',
  fontSize: '0.75rem',
}));

const UserInfo = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(1),
}));

const ActionButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.text.secondary,
  '&.active': {
    color: theme.palette.primary.main,
  },
  '&.liked': {
    color: theme.palette.error.main,
  },
}));

const VideoCard = ({
  video,
  size = 'medium',
  showUser = true,
  showActions = true,
  autoPlay = false,
  onClick,
  className,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { currentUser } = useSelector(state => state.auth);
  const { favorites } = useSelector(state => state.video);
  
  // State
  const [isPlaying, setIsPlaying] = useState(false);
  const [showOverlay, setShowOverlay] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [isHovered, setIsHovered] = useState(false);
  
  // Refs
  const cardRef = useRef(null);
  
  // Computed values
  const isLiked = video.isLiked || false;
  const isFavorited = favorites.includes(video.id);
  const isOwner = currentUser?.id === video.user?.id;
  
  // Card dimensions based on size
  const cardDimensions = {
    small: { width: 200, height: 280 },
    medium: { width: 280, height: 380 },
    large: { width: 360, height: 480 },
  };
  
  const { width, height } = cardDimensions[size] || cardDimensions.medium;

  // Handlers
  const handleCardClick = (event) => {
    if (event.target.closest('.action-button') || event.target.closest('.menu-button')) {
      return;
    }
    
    if (onClick) {
      onClick(video);
    } else {
      navigate(`/video/${video.id}`);
    }
  };

  const handlePlayPause = (event) => {
    event.stopPropagation();
    setIsPlaying(!isPlaying);
  };

  const handleLike = async (event) => {
    event.stopPropagation();
    
    if (!currentUser) {
      navigate('/login');
      return;
    }

    try {
      await dispatch(toggleVideoLike(video.id)).unwrap();
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  const handleComment = (event) => {
    event.stopPropagation();
    navigate(`/video/${video.id}`, { state: { showComments: true } });
  };

  const handleShare = async (event) => {
    event.stopPropagation();
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: video.title,
          text: video.description,
          url: `${window.location.origin}/video/${video.id}`,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      await navigator.clipboard.writeText(`${window.location.origin}/video/${video.id}`);
      // Show snackbar notification
    }
  };

  const handleFavorite = async (event) => {
    event.stopPropagation();
    
    if (!currentUser) {
      navigate('/login');
      return;
    }

    try {
      if (isFavorited) {
        await dispatch(removeFromFavorites(video.id)).unwrap();
      } else {
        await dispatch(addToFavorites(video.id)).unwrap();
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const handleMenuOpen = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleUserClick = (event) => {
    event.stopPropagation();
    navigate(`/profile/${video.user.username}`);
  };

  const handleReport = () => {
    handleMenuClose();
    // Open report dialog
  };

  const handleCopyLink = async () => {
    handleMenuClose();
    await navigator.clipboard.writeText(`${window.location.origin}/video/${video.id}`);
    // Show snackbar notification
  };

  const handleDownload = () => {
    handleMenuClose();
    // Implement download functionality
  };

  // Format numbers
  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Format duration
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <StyledCard
      ref={cardRef}
      className={`video-card ${className || ''}`}
      sx={{ width, height }}
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      {...props}
    >
      {/* Video/Thumbnail */}
      <Box sx={{ position: 'relative', height: '70%' }}>
        {isPlaying ? (
          <VideoPlayer
            video={video}
            autoPlay={true}
            muted={true}
            controls={false}
            onPause={() => setIsPlaying(false)}
          />
        ) : (
          <CardMedia
            component="img"
            image={video.thumbnailUrl}
            alt={video.title}
            sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
        )}

        {/* Play Button */}
        {!isPlaying && (
          <PlayButton onClick={handlePlayPause}>
            <PlayArrow sx={{ fontSize: 32 }} />
          </PlayButton>
        )}

        {/* Duration */}
        <StatsChip
          icon={<AccessTime sx={{ fontSize: 16 }} />}
          label={formatDuration(video.duration)}
          size="small"
        />

        {/* Views */}
        <StatsChip
          icon={<Visibility sx={{ fontSize: 16 }} />}
          label={formatNumber(video.viewsCount)}
          size="small"
          sx={{ top: 40 }}
        />

        {/* Overlay */}
        <VideoOverlay className={isHovered ? 'visible' : ''}>
          {showUser && (
            <UserInfo>
              <Avatar
                src={video.user?.avatarUrl}
                alt={video.user?.fullName}
                sx={{ width: 32, height: 32, cursor: 'pointer' }}
                onClick={handleUserClick}
              />
              <Box>
                <Typography
                  variant="body2"
                  sx={{ color: 'white', fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={handleUserClick}
                >
                  {video.user?.fullName}
                </Typography>
                <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)' }}>
                  @{video.user?.username}
                </Typography>
              </Box>
            </UserInfo>
          )}
        </VideoOverlay>
      </Box>

      {/* Content */}
      <CardContent sx={{ height: '30%', p: 2 }}>
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: 'bold',
            mb: 0.5,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
          }}
        >
          {video.title}
        </Typography>

        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 1,
            WebkitBoxOrient: 'vertical',
          }}
        >
          {video.description}
        </Typography>

        <Typography variant="caption" color="text.secondary">
          {formatDistanceToNow(new Date(video.createdAt), {
            addSuffix: true,
            locale: ar,
          })}
        </Typography>
      </CardContent>

      {/* Actions */}
      {showActions && (
        <CardActions sx={{ justifyContent: 'space-between', px: 2, py: 1 }}>
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {/* Like */}
            <Tooltip title={isLiked ? 'إلغاء الإعجاب' : 'إعجاب'}>
              <ActionButton
                className={`action-button ${isLiked ? 'liked' : ''}`}
                onClick={handleLike}
                size="small"
              >
                <Badge badgeContent={formatNumber(video.likesCount)} color="primary">
                  {isLiked ? <Favorite /> : <FavoriteBorder />}
                </Badge>
              </ActionButton>
            </Tooltip>

            {/* Comment */}
            <Tooltip title="تعليق">
              <ActionButton className="action-button" onClick={handleComment} size="small">
                <Badge badgeContent={formatNumber(video.commentsCount)} color="primary">
                  <Comment />
                </Badge>
              </ActionButton>
            </Tooltip>

            {/* Share */}
            <Tooltip title="مشاركة">
              <ActionButton className="action-button" onClick={handleShare} size="small">
                <Share />
              </ActionButton>
            </Tooltip>
          </Box>

          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {/* Favorite */}
            <Tooltip title={isFavorited ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}>
              <ActionButton
                className={`action-button ${isFavorited ? 'active' : ''}`}
                onClick={handleFavorite}
                size="small"
              >
                {isFavorited ? <Bookmark /> : <BookmarkBorder />}
              </ActionButton>
            </Tooltip>

            {/* Menu */}
            <Tooltip title="المزيد">
              <ActionButton
                className="menu-button"
                onClick={handleMenuOpen}
                size="small"
              >
                <MoreVert />
              </ActionButton>
            </Tooltip>
          </Box>
        </CardActions>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleCopyLink}>
          <ListItemIcon>
            <LinkIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>نسخ الرابط</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleDownload}>
          <ListItemIcon>
            <Download fontSize="small" />
          </ListItemIcon>
          <ListItemText>تحميل</ListItemText>
        </MenuItem>

        {!isOwner && (
          <MenuItem onClick={handleReport}>
            <ListItemIcon>
              <Report fontSize="small" />
            </ListItemIcon>
            <ListItemText>إبلاغ</ListItemText>
          </MenuItem>
        )}
      </Menu>
    </StyledCard>
  );
};

export default VideoCard;
