// =====================================================
// Feature Slice - إدارة حالة الميزات الديناميكية
// =====================================================

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { featureService } from '../../services/featureService';

const initialState = {
  features: {},
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const loadFeatures = createAsyncThunk(
  'feature/loadFeatures',
  async (_, { rejectWithValue }) => {
    try {
      const response = await featureService.getActiveFeatures();
      return response.data;
    } catch (error) {
      return rejectWithValue('فشل في تحميل الميزات');
    }
  }
);

const featureSlice = createSlice({
  name: 'feature',
  initialState,
  reducers: {
    setFeatures: (state, action) => {
      state.features = action.payload;
      state.lastUpdated = Date.now();
    },
    updateFeature: (state, action) => {
      const { featureKey, settings } = action.payload;
      if (state.features[featureKey]) {
        state.features[featureKey].settings = settings;
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadFeatures.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadFeatures.fulfilled, (state, action) => {
        state.isLoading = false;
        state.features = action.payload.features;
        state.lastUpdated = Date.now();
      })
      .addCase(loadFeatures.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { setFeatures, updateFeature, clearError } = featureSlice.actions;

// Selectors
export const selectFeatures = (state) => state.feature.features;
export const selectIsFeatureEnabled = (featureKey) => (state) => 
  !!state.feature.features[featureKey];
export const selectFeatureSettings = (featureKey) => (state) => 
  state.feature.features[featureKey]?.settings || {};

export default featureSlice.reducer;
