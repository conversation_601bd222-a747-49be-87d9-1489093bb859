#!/bin/bash

# =====================================================
# TikTok Clone - Setup Script
# =====================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Main setup function
main() {
    print_info "🚀 بدء إعداد مشروع TikTok Clone..."
    
    # Check system requirements
    check_requirements
    
    # Setup backend
    setup_backend
    
    # Setup web app
    setup_web_app
    
    # Setup admin panel
    setup_admin_panel
    
    # Setup Flutter app
    setup_flutter_app
    
    # Setup database
    setup_database
    
    # Final steps
    final_setup
    
    print_status "🎉 تم إعداد المشروع بنجاح!"
    print_info "استخدم ./start.sh لتشغيل المشروع"
}

# Check system requirements
check_requirements() {
    print_info "التحقق من متطلبات النظام..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ مطلوب. الإصدار الحالي: $(node --version)"
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm غير مثبت"
        exit 1
    fi
    
    # Check Git
    if ! command_exists git; then
        print_warning "Git غير مثبت. يُنصح بتثبيته"
    fi
    
    # Check Docker (optional)
    if ! command_exists docker; then
        print_warning "Docker غير مثبت. لن تتمكن من استخدام Docker Compose"
    fi
    
    # Check Flutter (optional)
    if ! command_exists flutter; then
        print_warning "Flutter غير مثبت. لن تتمكن من تشغيل التطبيق المحمول"
    else
        print_info "Flutter version: $(flutter --version | head -n1)"
    fi
    
    print_status "تم التحقق من المتطلبات"
}

# Setup backend
setup_backend() {
    print_info "إعداد Backend..."
    
    cd backend
    
    # Install dependencies
    print_info "تثبيت مكتبات Backend..."
    npm install
    
    # Copy environment file
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_info "تم إنشاء ملف .env"
        print_warning "يرجى تحديث ملف .env بالإعدادات الصحيحة"
    fi
    
    # Create directories
    mkdir -p uploads logs temp
    
    cd ..
    print_status "تم إعداد Backend"
}

# Setup web app
setup_web_app() {
    print_info "إعداد Web App..."
    
    cd web_app
    
    # Install dependencies
    print_info "تثبيت مكتبات Web App..."
    npm install
    
    # Copy environment file
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_info "تم إنشاء ملف .env للـ Web App"
    fi
    
    cd ..
    print_status "تم إعداد Web App"
}

# Setup admin panel
setup_admin_panel() {
    print_info "إعداد Admin Panel..."
    
    cd admin_panel
    
    # Install dependencies
    print_info "تثبيت مكتبات Admin Panel..."
    npm install
    
    # Copy environment file
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_info "تم إنشاء ملف .env للـ Admin Panel"
    fi
    
    cd ..
    print_status "تم إعداد Admin Panel"
}

# Setup Flutter app
setup_flutter_app() {
    if command_exists flutter; then
        print_info "إعداد Flutter App..."
        
        cd mobile_app
        
        # Get dependencies
        print_info "تثبيت مكتبات Flutter..."
        flutter pub get
        
        # Run flutter doctor
        print_info "فحص إعداد Flutter..."
        flutter doctor
        
        cd ..
        print_status "تم إعداد Flutter App"
    else
        print_warning "تم تخطي إعداد Flutter App (Flutter غير مثبت)"
    fi
}

# Setup database
setup_database() {
    print_info "إعداد قاعدة البيانات..."
    
    # Check if MongoDB is running
    if command_exists mongosh; then
        print_info "MongoDB متوفر"
    elif command_exists mongo; then
        print_info "MongoDB متوفر (إصدار قديم)"
    else
        print_warning "MongoDB غير مثبت أو غير متوفر"
        print_info "يرجى تثبيت MongoDB أو استخدام Docker"
    fi
    
    # Check if Redis is running
    if command_exists redis-cli; then
        if redis-cli ping >/dev/null 2>&1; then
            print_status "Redis يعمل"
        else
            print_warning "Redis غير متصل"
        fi
    else
        print_warning "Redis غير مثبت"
    fi
}

# Final setup steps
final_setup() {
    print_info "الخطوات النهائية..."
    
    # Make scripts executable
    chmod +x start.sh
    chmod +x scripts/*.sh
    
    # Create logs directory
    mkdir -p logs
    
    # Git setup (if git is available)
    if command_exists git && [ ! -d ".git" ]; then
        print_info "إعداد Git repository..."
        git init
        git add .
        git commit -m "Initial commit: TikTok Clone project setup"
    fi
    
    print_status "تم إكمال الإعداد"
}

# Run main function
main "$@"
