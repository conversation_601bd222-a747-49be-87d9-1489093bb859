// =====================================================
// Video Model - نموذج الفيديو
// =====================================================

const mongoose = require('mongoose');

const videoSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  title: {
    type: String,
    trim: true,
    maxlength: 255
  },
  description: {
    type: String,
    trim: true,
    maxlength: 2000
  },
  videoUrl: {
    type: String,
    required: true
  },
  thumbnailUrl: {
    type: String
  },
  duration: {
    type: Number,
    required: true,
    min: 1,
    max: 600 // 10 minutes max
  },
  fileSize: {
    type: Number,
    min: 0
  },
  resolution: {
    type: String,
    match: /^\d+x\d+$/
  },
  format: {
    type: String,
    default: 'mp4',
    enum: ['mp4', 'mov', 'avi', 'mkv', 'webm']
  },
  
  // Content Details
  hashtags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  mentions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  location: {
    type: String,
    maxlength: 100
  },
  music: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music'
  },
  
  // Privacy & Visibility
  privacy: {
    type: String,
    enum: ['public', 'friends', 'private'],
    default: 'public',
    index: true
  },
  allowComments: {
    type: Boolean,
    default: true
  },
  allowDuet: {
    type: Boolean,
    default: true
  },
  allowStitch: {
    type: Boolean,
    default: true
  },
  allowDownload: {
    type: Boolean,
    default: false
  },
  
  // Engagement Stats
  viewsCount: {
    type: Number,
    default: 0,
    min: 0,
    index: true
  },
  likesCount: {
    type: Number,
    default: 0,
    min: 0,
    index: true
  },
  commentsCount: {
    type: Number,
    default: 0,
    min: 0
  },
  sharesCount: {
    type: Number,
    default: 0,
    min: 0
  },
  downloadsCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Content Moderation
  isApproved: {
    type: Boolean,
    default: true,
    index: true
  },
  isFeatured: {
    type: Boolean,
    default: false,
    index: true
  },
  isTrending: {
    type: Boolean,
    default: false,
    index: true
  },
  contentWarning: {
    type: Boolean,
    default: false
  },
  ageRestriction: {
    type: String,
    enum: ['none', '13+', '16+', '18+'],
    default: 'none'
  },
  
  // AI Analysis
  aiTags: [{
    tag: String,
    confidence: {
      type: Number,
      min: 0,
      max: 1
    }
  }],
  aiSentiment: {
    type: String,
    enum: ['positive', 'negative', 'neutral']
  },
  aiContentScore: {
    type: Number,
    min: 0,
    max: 10
  },
  aiQualityScore: {
    type: Number,
    min: 0,
    max: 10
  },
  
  // Processing Status
  processingStatus: {
    type: String,
    enum: ['uploading', 'processing', 'ready', 'failed'],
    default: 'uploading',
    index: true
  },
  processingProgress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  processingError: {
    type: String
  },
  
  // Video Qualities
  qualities: [{
    resolution: String,
    url: String,
    fileSize: Number,
    bitrate: Number
  }],
  
  // Timestamps
  publishedAt: {
    type: Date,
    index: true
  },
  scheduledAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Compound Indexes
videoSchema.index({ user: 1, createdAt: -1 });
videoSchema.index({ publishedAt: -1, privacy: 1 });
videoSchema.index({ hashtags: 1 });
videoSchema.index({ viewsCount: -1 });
videoSchema.index({ likesCount: -1 });
videoSchema.index({ isTrending: 1, createdAt: -1 });
videoSchema.index({ isFeatured: 1, createdAt: -1 });

// Text search index
videoSchema.index({
  title: 'text',
  description: 'text',
  hashtags: 'text'
});

// Pre-save middleware
videoSchema.pre('save', function(next) {
  // Auto-publish if not scheduled
  if (!this.publishedAt && !this.scheduledAt && this.processingStatus === 'ready') {
    this.publishedAt = new Date();
  }
  
  // Clean hashtags
  if (this.hashtags) {
    this.hashtags = this.hashtags
      .map(tag => tag.replace(/^#/, '').toLowerCase().trim())
      .filter(tag => tag.length > 0 && tag.length <= 50);
  }
  
  next();
});

// Virtual for engagement rate
videoSchema.virtual('engagementRate').get(function() {
  if (this.viewsCount === 0) return 0;
  return ((this.likesCount + this.commentsCount + this.sharesCount) / this.viewsCount) * 100;
});

// Method to increment view count
videoSchema.methods.incrementViews = function() {
  this.viewsCount += 1;
  return this.save();
};

// Method to increment likes count
videoSchema.methods.incrementLikes = function() {
  this.likesCount += 1;
  return this.save();
};

// Method to decrement likes count
videoSchema.methods.decrementLikes = function() {
  if (this.likesCount > 0) {
    this.likesCount -= 1;
  }
  return this.save();
};

// Method to increment comments count
videoSchema.methods.incrementComments = function() {
  this.commentsCount += 1;
  return this.save();
};

// Method to decrement comments count
videoSchema.methods.decrementComments = function() {
  if (this.commentsCount > 0) {
    this.commentsCount -= 1;
  }
  return this.save();
};

// Method to increment shares count
videoSchema.methods.incrementShares = function() {
  this.sharesCount += 1;
  return this.save();
};

// Static method to get trending videos
videoSchema.statics.getTrending = function(limit = 20) {
  return this.find({
    privacy: 'public',
    isApproved: true,
    publishedAt: { $exists: true }
  })
  .sort({ 
    isTrending: -1,
    viewsCount: -1,
    likesCount: -1,
    createdAt: -1 
  })
  .limit(limit)
  .populate('user', 'username fullName avatarUrl isVerified')
  .populate('music', 'title artist');
};

// Static method to get featured videos
videoSchema.statics.getFeatured = function(limit = 10) {
  return this.find({
    privacy: 'public',
    isApproved: true,
    isFeatured: true,
    publishedAt: { $exists: true }
  })
  .sort({ createdAt: -1 })
  .limit(limit)
  .populate('user', 'username fullName avatarUrl isVerified')
  .populate('music', 'title artist');
};

// Static method to search videos
videoSchema.statics.searchVideos = function(query, options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'relevance'
  } = options;
  
  const skip = (page - 1) * limit;
  
  let sortOptions = {};
  switch (sortBy) {
    case 'recent':
      sortOptions = { createdAt: -1 };
      break;
    case 'popular':
      sortOptions = { viewsCount: -1 };
      break;
    case 'likes':
      sortOptions = { likesCount: -1 };
      break;
    default:
      sortOptions = { score: { $meta: 'textScore' } };
  }
  
  return this.find(
    {
      $text: { $search: query },
      privacy: 'public',
      isApproved: true,
      publishedAt: { $exists: true }
    },
    { score: { $meta: 'textScore' } }
  )
  .sort(sortOptions)
  .skip(skip)
  .limit(limit)
  .populate('user', 'username fullName avatarUrl isVerified')
  .populate('music', 'title artist');
};

module.exports = mongoose.model('Video', videoSchema);
