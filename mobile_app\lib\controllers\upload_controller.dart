// =====================================================
// Upload Controller - متحكم الرفع
// =====================================================

import 'dart:io';
import 'dart:typed_data';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/video_model.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';

class UploadController extends GetxController {
  final ApiService _apiService = Get.find<ApiService>();
  final StorageService _storageService = Get.find<StorageService>();
  final ImagePicker _imagePicker = ImagePicker();

  // حالة الرفع
  final RxBool _isUploading = false.obs;
  final RxBool _isProcessing = false.obs;
  final RxDouble _uploadProgress = 0.0.obs;
  final RxString _uploadStatus = ''.obs;

  // ملف الفيديو
  final Rx<File?> _videoFile = Rx<File?>(null);
  final Rx<File?> _thumbnailFile = Rx<File?>(null);
  final Rx<VideoPlayerController?> _videoController = Rx<VideoPlayerController?>(null);

  // معلومات الفيديو
  final RxString _title = ''.obs;
  final RxString _description = ''.obs;
  final RxList<String> _hashtags = <String>[].obs;
  final RxString _selectedMusic = ''.obs;
  final RxBool _allowComments = true.obs;
  final RxBool _allowDuet = true.obs;
  final RxString _privacy = 'public'.obs;

  // معلومات تقنية
  final RxInt _videoDuration = 0.obs;
  final RxString _videoResolution = ''.obs;
  final RxInt _videoSize = 0.obs;
  final RxString _videoFormat = ''.obs;

  // المسودات
  final RxList<Map<String, dynamic>> _drafts = <Map<String, dynamic>>[].obs;

  // Getters
  bool get isUploading => _isUploading.value;
  bool get isProcessing => _isProcessing.value;
  double get uploadProgress => _uploadProgress.value;
  String get uploadStatus => _uploadStatus.value;
  File? get videoFile => _videoFile.value;
  File? get thumbnailFile => _thumbnailFile.value;
  VideoPlayerController? get videoController => _videoController.value;
  String get title => _title.value;
  String get description => _description.value;
  List<String> get hashtags => _hashtags;
  String get selectedMusic => _selectedMusic.value;
  bool get allowComments => _allowComments.value;
  bool get allowDuet => _allowDuet.value;
  String get privacy => _privacy.value;
  int get videoDuration => _videoDuration.value;
  String get videoResolution => _videoResolution.value;
  int get videoSize => _videoSize.value;
  String get videoFormat => _videoFormat.value;
  List<Map<String, dynamic>> get drafts => _drafts;

  @override
  void onInit() {
    super.onInit();
    _loadDrafts();
  }

  @override
  void onClose() {
    _disposeVideoController();
    super.onClose();
  }

  // ==================== Video Selection ====================

  // اختيار فيديو من المعرض
  Future<void> pickVideoFromGallery() async {
    try {
      // طلب الأذونات
      if (!await _requestPermissions()) {
        Get.snackbar('خطأ', 'يجب منح الأذونات للوصول للمعرض');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 10),
      );

      if (video != null) {
        await _processSelectedVideo(File(video.path));
      }
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في اختيار الفيديو');
      print('Error picking video: $e');
    }
  }

  // تسجيل فيديو جديد
  Future<void> recordVideo() async {
    try {
      // طلب الأذونات
      if (!await _requestPermissions()) {
        Get.snackbar('خطأ', 'يجب منح الأذونات للكاميرا');
        return;
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 10),
      );

      if (video != null) {
        await _processSelectedVideo(File(video.path));
      }
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تسجيل الفيديو');
      print('Error recording video: $e');
    }
  }

  // معالجة الفيديو المحدد
  Future<void> _processSelectedVideo(File videoFile) async {
    try {
      _isProcessing.value = true;
      _uploadStatus.value = 'معالجة الفيديو...';

      // التحقق من صحة الفيديو
      if (!await _validateVideo(videoFile)) {
        return;
      }

      // حفظ ملف الفيديو
      _videoFile.value = videoFile;

      // إنشاء مشغل الفيديو
      await _initializeVideoController();

      // استخراج معلومات الفيديو
      await _extractVideoInfo();

      // إنشاء صورة مصغرة
      await _generateThumbnail();

      _uploadStatus.value = 'جاهز للرفع';
      Get.snackbar('نجح', 'تم تحديد الفيديو بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في معالجة الفيديو');
      print('Error processing video: $e');
    } finally {
      _isProcessing.value = false;
    }
  }

  // ==================== Video Processing ====================

  // التحقق من صحة الفيديو
  Future<bool> _validateVideo(File videoFile) async {
    try {
      // التحقق من وجود الملف
      if (!await videoFile.exists()) {
        Get.snackbar('خطأ', 'الملف غير موجود');
        return false;
      }

      // التحقق من حجم الملف
      final fileSize = await videoFile.length();
      if (fileSize > AppConstants.maxVideoSize) {
        Get.snackbar('خطأ', 'حجم الفيديو كبير جداً (الحد الأقصى: ${AppConstants.maxVideoSize ~/ (1024 * 1024)}MB)');
        return false;
      }

      // التحقق من تنسيق الملف
      final extension = videoFile.path.split('.').last.toLowerCase();
      if (!AppConstants.supportedVideoFormats.contains(extension)) {
        Get.snackbar('خطأ', 'تنسيق الفيديو غير مدعوم');
        return false;
      }

      return true;
    } catch (e) {
      print('Error validating video: $e');
      return false;
    }
  }

  // تهيئة مشغل الفيديو
  Future<void> _initializeVideoController() async {
    if (_videoFile.value == null) return;

    try {
      _disposeVideoController();
      
      _videoController.value = VideoPlayerController.file(_videoFile.value!);
      await _videoController.value!.initialize();
      
      // تشغيل الفيديو في حلقة
      _videoController.value!.setLooping(true);
    } catch (e) {
      print('Error initializing video controller: $e');
    }
  }

  // استخراج معلومات الفيديو
  Future<void> _extractVideoInfo() async {
    if (_videoController.value == null) return;

    try {
      final controller = _videoController.value!;
      
      _videoDuration.value = controller.value.duration.inSeconds;
      _videoResolution.value = '${controller.value.size.width.toInt()}x${controller.value.size.height.toInt()}';
      _videoSize.value = await _videoFile.value!.length();
      _videoFormat.value = _videoFile.value!.path.split('.').last.toUpperCase();
    } catch (e) {
      print('Error extracting video info: $e');
    }
  }

  // إنشاء صورة مصغرة
  Future<void> _generateThumbnail() async {
    if (_videoFile.value == null) return;

    try {
      final uint8list = await VideoThumbnail.thumbnailData(
        video: _videoFile.value!.path,
        imageFormat: ImageFormat.JPEG,
        maxWidth: 640,
        maxHeight: 360,
        quality: 80,
      );

      if (uint8list != null) {
        // حفظ الصورة المصغرة في ملف مؤقت
        final tempDir = await getTemporaryDirectory();
        final thumbnailPath = '${tempDir.path}/thumbnail_${DateTime.now().millisecondsSinceEpoch}.jpg';
        
        final thumbnailFile = File(thumbnailPath);
        await thumbnailFile.writeAsBytes(uint8list);
        
        _thumbnailFile.value = thumbnailFile;
      }
    } catch (e) {
      print('Error generating thumbnail: $e');
    }
  }

  // ==================== Upload Process ====================

  // رفع الفيديو
  Future<void> uploadVideo() async {
    if (_videoFile.value == null) {
      Get.snackbar('خطأ', 'يجب اختيار فيديو أولاً');
      return;
    }

    if (_title.value.trim().isEmpty) {
      Get.snackbar('خطأ', 'يجب إدخال عنوان للفيديو');
      return;
    }

    try {
      _isUploading.value = true;
      _uploadProgress.value = 0.0;
      _uploadStatus.value = 'بدء الرفع...';

      // رفع الفيديو للخادم
      final response = await _apiService.uploadVideo(
        videoFile: _videoFile.value!,
        title: _title.value,
        description: _description.value,
        thumbnailFile: _thumbnailFile.value,
        hashtags: _hashtags.isNotEmpty ? _hashtags : null,
        musicId: _selectedMusic.value.isNotEmpty ? _selectedMusic.value : null,
        allowComments: _allowComments.value,
        allowDuet: _allowDuet.value,
        privacy: _privacy.value,
      );

      if (response.success && response.data != null) {
        _uploadStatus.value = 'تم الرفع بنجاح';
        _uploadProgress.value = 1.0;
        
        Get.snackbar('نجح', 'تم رفع الفيديو بنجاح');
        
        // مسح البيانات
        _clearUploadData();
        
        // العودة للصفحة الرئيسية
        Get.offAllNamed('/home');
      } else {
        Get.snackbar('خطأ', response.error ?? 'فشل في رفع الفيديو');
      }
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء رفع الفيديو');
      print('Error uploading video: $e');
    } finally {
      _isUploading.value = false;
    }
  }

  // ==================== Draft Management ====================

  // حفظ كمسودة
  Future<void> saveDraft() async {
    if (_videoFile.value == null) {
      Get.snackbar('خطأ', 'لا يوجد فيديو لحفظه');
      return;
    }

    try {
      final draft = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'videoPath': _videoFile.value!.path,
        'thumbnailPath': _thumbnailFile.value?.path,
        'title': _title.value,
        'description': _description.value,
        'hashtags': _hashtags.toList(),
        'selectedMusic': _selectedMusic.value,
        'allowComments': _allowComments.value,
        'allowDuet': _allowDuet.value,
        'privacy': _privacy.value,
        'duration': _videoDuration.value,
        'resolution': _videoResolution.value,
        'size': _videoSize.value,
        'format': _videoFormat.value,
        'createdAt': DateTime.now().toIso8601String(),
      };

      await _storageService.saveDraft(draft);
      _loadDrafts();
      
      Get.snackbar('نجح', 'تم حفظ المسودة');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في حفظ المسودة');
      print('Error saving draft: $e');
    }
  }

  // تحميل المسودات
  void _loadDrafts() {
    _drafts.value = _storageService.getDrafts();
  }

  // تحميل مسودة
  Future<void> loadDraft(Map<String, dynamic> draft) async {
    try {
      _isProcessing.value = true;
      
      // تحميل ملف الفيديو
      final videoPath = draft['videoPath'] as String;
      if (await File(videoPath).exists()) {
        _videoFile.value = File(videoPath);
        await _initializeVideoController();
      } else {
        Get.snackbar('خطأ', 'ملف الفيديو غير موجود');
        return;
      }

      // تحميل الصورة المصغرة
      final thumbnailPath = draft['thumbnailPath'] as String?;
      if (thumbnailPath != null && await File(thumbnailPath).exists()) {
        _thumbnailFile.value = File(thumbnailPath);
      }

      // تحميل البيانات
      _title.value = draft['title'] ?? '';
      _description.value = draft['description'] ?? '';
      _hashtags.value = List<String>.from(draft['hashtags'] ?? []);
      _selectedMusic.value = draft['selectedMusic'] ?? '';
      _allowComments.value = draft['allowComments'] ?? true;
      _allowDuet.value = draft['allowDuet'] ?? true;
      _privacy.value = draft['privacy'] ?? 'public';
      _videoDuration.value = draft['duration'] ?? 0;
      _videoResolution.value = draft['resolution'] ?? '';
      _videoSize.value = draft['size'] ?? 0;
      _videoFormat.value = draft['format'] ?? '';

      Get.snackbar('نجح', 'تم تحميل المسودة');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحميل المسودة');
      print('Error loading draft: $e');
    } finally {
      _isProcessing.value = false;
    }
  }

  // حذف مسودة
  Future<void> deleteDraft(String draftId) async {
    try {
      await _storageService.deleteDraft(draftId);
      _loadDrafts();
      Get.snackbar('نجح', 'تم حذف المسودة');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في حذف المسودة');
      print('Error deleting draft: $e');
    }
  }

  // ==================== Data Management ====================

  // تحديث العنوان
  void updateTitle(String title) {
    _title.value = title;
  }

  // تحديث الوصف
  void updateDescription(String description) {
    _description.value = description;
  }

  // إضافة هاشتاغ
  void addHashtag(String hashtag) {
    if (hashtag.isNotEmpty && !_hashtags.contains(hashtag)) {
      _hashtags.add(hashtag);
    }
  }

  // إزالة هاشتاغ
  void removeHashtag(String hashtag) {
    _hashtags.remove(hashtag);
  }

  // تحديث الموسيقى المحددة
  void updateSelectedMusic(String musicId) {
    _selectedMusic.value = musicId;
  }

  // تحديث إعدادات الخصوصية
  void updatePrivacySettings({
    bool? allowComments,
    bool? allowDuet,
    String? privacy,
  }) {
    if (allowComments != null) _allowComments.value = allowComments;
    if (allowDuet != null) _allowDuet.value = allowDuet;
    if (privacy != null) _privacy.value = privacy;
  }

  // مسح بيانات الرفع
  void _clearUploadData() {
    _disposeVideoController();
    _videoFile.value = null;
    _thumbnailFile.value = null;
    _title.value = '';
    _description.value = '';
    _hashtags.clear();
    _selectedMusic.value = '';
    _allowComments.value = true;
    _allowDuet.value = true;
    _privacy.value = 'public';
    _videoDuration.value = 0;
    _videoResolution.value = '';
    _videoSize.value = 0;
    _videoFormat.value = '';
    _uploadProgress.value = 0.0;
    _uploadStatus.value = '';
  }

  // ==================== Utility Methods ====================

  // طلب الأذونات
  Future<bool> _requestPermissions() async {
    final cameraStatus = await Permission.camera.request();
    final storageStatus = await Permission.storage.request();
    
    return cameraStatus.isGranted && storageStatus.isGranted;
  }

  // تحرير مشغل الفيديو
  void _disposeVideoController() {
    _videoController.value?.dispose();
    _videoController.value = null;
  }

  // إلغاء الرفع
  void cancelUpload() {
    _clearUploadData();
    _isUploading.value = false;
    _isProcessing.value = false;
  }
}
