// =====================================================
// Feature Manager Component - مكون إدارة الميزات
// =====================================================

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Paper,
  useTheme
} from '@mui/material';
import {
  ExpandMore,
  Settings,
  Info,
  Warning,
  CheckCircle,
  Cancel,
  Edit,
  Save,
  Refresh,
  Add,
  Delete,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useDispatch, useSelector } from 'react-redux';
import { fetchFeatures, updateFeature, createFeature } from '../../store/slices/adminSlice';

// Styled Components
const FeatureCard = styled(Card)(({ theme, status }) => ({
  border: `2px solid ${
    status === 'active' ? theme.palette.success.main :
    status === 'inactive' ? theme.palette.error.main :
    status === 'beta' ? theme.palette.warning.main :
    theme.palette.grey[300]
  }`,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[4],
  },
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  backgroundColor: 
    status === 'active' ? theme.palette.success.main :
    status === 'inactive' ? theme.palette.error.main :
    status === 'beta' ? theme.palette.warning.main :
    status === 'development' ? theme.palette.info.main :
    theme.palette.grey[500],
  color: 'white',
  fontWeight: 'bold',
}));

const FeatureManager = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { features, loading } = useSelector(state => state.admin);

  // State
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showInactive, setShowInactive] = useState(true);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    status: 'development',
    isEnabled: false,
    config: {},
    dependencies: [],
    rolloutPercentage: 0,
    targetAudience: 'all',
    startDate: '',
    endDate: '',
  });

  useEffect(() => {
    dispatch(fetchFeatures());
  }, [dispatch]);

  // Sample features data - في التطبيق الحقيقي سيأتي من الخادم
  const sampleFeatures = [
    {
      id: 1,
      name: 'التوأم الإبداعي',
      description: 'نسخة ذكية تتعلم من أسلوب المستخدم وتقترح محتوى مشابه',
      category: 'ai',
      status: 'active',
      isEnabled: true,
      rolloutPercentage: 100,
      targetAudience: 'all',
      usageCount: 15420,
      config: {
        learningRate: 0.01,
        maxSuggestions: 10,
        updateInterval: '24h'
      }
    },
    {
      id: 2,
      name: 'الرادار الاجتماعي',
      description: 'اكتشاف المحتوى والأشخاص القريبين جغرافياً',
      category: 'social',
      status: 'beta',
      isEnabled: true,
      rolloutPercentage: 50,
      targetAudience: 'premium',
      usageCount: 8934,
      config: {
        radius: 10,
        maxResults: 20,
        privacyLevel: 'medium'
      }
    },
    {
      id: 3,
      name: 'توليد فيديو بالصوت',
      description: 'إنشاء محتوى فيديو بالأوامر الصوتية باستخدام الذكاء الاصطناعي',
      category: 'ai',
      status: 'development',
      isEnabled: false,
      rolloutPercentage: 0,
      targetAudience: 'developers',
      usageCount: 0,
      config: {
        maxDuration: 60,
        quality: 'hd',
        languages: ['ar', 'en']
      }
    },
    {
      id: 4,
      name: 'وضع الفن الفوري',
      description: 'تحويل الفيديوهات لأعمال فنية باستخدام فلاتر AI متقدمة',
      category: 'creative',
      status: 'active',
      isEnabled: true,
      rolloutPercentage: 80,
      targetAudience: 'creators',
      usageCount: 23456,
      config: {
        styles: ['impressionist', 'abstract', 'realistic'],
        processingTime: 30,
        quality: 'high'
      }
    },
    {
      id: 5,
      name: 'البث الوهمي',
      description: 'بث مباشر ذكي يستخدم AI لإنشاء محتوى تفاعلي',
      category: 'streaming',
      status: 'inactive',
      isEnabled: false,
      rolloutPercentage: 0,
      targetAudience: 'all',
      usageCount: 0,
      config: {
        maxViewers: 1000,
        interactionDelay: 2,
        contentTypes: ['qa', 'entertainment', 'education']
      }
    }
  ];

  const allFeatures = features.length > 0 ? features : sampleFeatures;

  // Categories
  const categories = [
    { value: 'all', label: 'جميع الفئات' },
    { value: 'ai', label: 'ذكاء اصطناعي' },
    { value: 'social', label: 'تفاعل اجتماعي' },
    { value: 'creative', label: 'إبداع وتصميم' },
    { value: 'streaming', label: 'بث مباشر' },
    { value: 'analytics', label: 'تحليلات' },
    { value: 'security', label: 'أمان' },
    { value: 'performance', label: 'أداء' },
  ];

  // Filter features
  const filteredFeatures = allFeatures.filter(feature => {
    const matchesCategory = selectedCategory === 'all' || feature.category === selectedCategory;
    const matchesSearch = feature.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         feature.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesVisibility = showInactive || feature.status !== 'inactive';
    
    return matchesCategory && matchesSearch && matchesVisibility;
  });

  // Handlers
  const handleFeatureToggle = async (featureId, enabled) => {
    try {
      await dispatch(updateFeature({
        id: featureId,
        updates: { isEnabled: enabled }
      })).unwrap();
    } catch (error) {
      console.error('Error updating feature:', error);
    }
  };

  const handleEditFeature = (feature) => {
    setSelectedFeature(feature);
    setFormData({
      name: feature.name,
      description: feature.description,
      category: feature.category,
      status: feature.status,
      isEnabled: feature.isEnabled,
      config: feature.config,
      dependencies: feature.dependencies || [],
      rolloutPercentage: feature.rolloutPercentage,
      targetAudience: feature.targetAudience,
      startDate: feature.startDate || '',
      endDate: feature.endDate || '',
    });
    setEditDialogOpen(true);
  };

  const handleSaveFeature = async () => {
    try {
      if (selectedFeature) {
        await dispatch(updateFeature({
          id: selectedFeature.id,
          updates: formData
        })).unwrap();
      } else {
        await dispatch(createFeature(formData)).unwrap();
      }
      setEditDialogOpen(false);
      setSelectedFeature(null);
    } catch (error) {
      console.error('Error saving feature:', error);
    }
  };

  const handleCloseDialog = () => {
    setEditDialogOpen(false);
    setSelectedFeature(null);
    setFormData({
      name: '',
      description: '',
      category: '',
      status: 'development',
      isEnabled: false,
      config: {},
      dependencies: [],
      rolloutPercentage: 0,
      targetAudience: 'all',
      startDate: '',
      endDate: '',
    });
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <CheckCircle color="success" />;
      case 'inactive': return <Cancel color="error" />;
      case 'beta': return <Warning color="warning" />;
      case 'development': return <Settings color="info" />;
      default: return <Info />;
    }
  };

  const getUsageColor = (count) => {
    if (count > 20000) return 'success';
    if (count > 10000) return 'warning';
    if (count > 0) return 'info';
    return 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          إدارة الميزات الديناميكية
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setEditDialogOpen(true)}
          >
            إضافة ميزة جديدة
          </Button>
          <IconButton onClick={() => dispatch(fetchFeatures())}>
            <Refresh />
          </IconButton>
        </Box>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="البحث في الميزات"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              size="small"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>الفئة</InputLabel>
              <Select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                label="الفئة"
              >
                {categories.map(category => (
                  <MenuItem key={category.value} value={category.value}>
                    {category.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControlLabel
              control={
                <Switch
                  checked={showInactive}
                  onChange={(e) => setShowInactive(e.target.checked)}
                />
              }
              label="إظهار الميزات غير النشطة"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <Typography variant="body2" color="text.secondary">
              {filteredFeatures.length} ميزة من أصل {allFeatures.length}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Features Grid */}
      <Grid container spacing={3}>
        {filteredFeatures.map((feature) => (
          <Grid item xs={12} md={6} lg={4} key={feature.id}>
            <FeatureCard status={feature.status}>
              <CardContent>
                {/* Header */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {feature.name}
                    </Typography>
                    <StatusChip
                      status={feature.status}
                      label={
                        feature.status === 'active' ? 'نشط' :
                        feature.status === 'inactive' ? 'غير نشط' :
                        feature.status === 'beta' ? 'تجريبي' :
                        'قيد التطوير'
                      }
                      size="small"
                    />
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getStatusIcon(feature.status)}
                    <IconButton size="small" onClick={() => handleEditFeature(feature)}>
                      <Edit />
                    </IconButton>
                  </Box>
                </Box>

                {/* Description */}
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {feature.description}
                </Typography>

                {/* Stats */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Chip
                    label={`${feature.usageCount.toLocaleString()} استخدام`}
                    color={getUsageColor(feature.usageCount)}
                    size="small"
                  />
                  <Chip
                    label={`${feature.rolloutPercentage}% نشر`}
                    variant="outlined"
                    size="small"
                  />
                </Box>

                {/* Toggle */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">
                    الجمهور المستهدف: {
                      feature.targetAudience === 'all' ? 'الجميع' :
                      feature.targetAudience === 'premium' ? 'المميز' :
                      feature.targetAudience === 'creators' ? 'المبدعين' :
                      'المطورين'
                    }
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={feature.isEnabled}
                        onChange={(e) => handleFeatureToggle(feature.id, e.target.checked)}
                        disabled={feature.status === 'development'}
                      />
                    }
                    label={feature.isEnabled ? 'مفعل' : 'معطل'}
                  />
                </Box>

                {/* Configuration Preview */}
                {Object.keys(feature.config).length > 0 && (
                  <Accordion sx={{ mt: 2 }}>
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="body2">إعدادات الميزة</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List dense>
                        {Object.entries(feature.config).map(([key, value]) => (
                          <ListItem key={key}>
                            <ListItemText
                              primary={key}
                              secondary={typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </AccordionDetails>
                  </Accordion>
                )}
              </CardContent>
            </FeatureCard>
          </Grid>
        ))}
      </Grid>

      {/* Edit Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedFeature ? 'تحرير الميزة' : 'إضافة ميزة جديدة'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم الميزة"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الفئة</InputLabel>
                <Select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  label="الفئة"
                >
                  {categories.slice(1).map(category => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="الوصف"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الحالة</InputLabel>
                <Select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  label="الحالة"
                >
                  <MenuItem value="development">قيد التطوير</MenuItem>
                  <MenuItem value="beta">تجريبي</MenuItem>
                  <MenuItem value="active">نشط</MenuItem>
                  <MenuItem value="inactive">غير نشط</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الجمهور المستهدف</InputLabel>
                <Select
                  value={formData.targetAudience}
                  onChange={(e) => setFormData({ ...formData, targetAudience: e.target.value })}
                  label="الجمهور المستهدف"
                >
                  <MenuItem value="all">الجميع</MenuItem>
                  <MenuItem value="premium">المميز</MenuItem>
                  <MenuItem value="creators">المبدعين</MenuItem>
                  <MenuItem value="developers">المطورين</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="نسبة النشر (%)"
                value={formData.rolloutPercentage}
                onChange={(e) => setFormData({ ...formData, rolloutPercentage: parseInt(e.target.value) })}
                inputProps={{ min: 0, max: 100 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isEnabled}
                    onChange={(e) => setFormData({ ...formData, isEnabled: e.target.checked })}
                  />
                }
                label="تفعيل الميزة"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          <Button onClick={handleSaveFeature} variant="contained">
            حفظ
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FeatureManager;
