// =====================================================
// Follow Model - نموذج المتابعة
// =====================================================

const mongoose = require('mongoose');

const followSchema = new mongoose.Schema({
  follower: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  following: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // Follow Status
  status: {
    type: String,
    enum: ['pending', 'accepted', 'blocked'],
    default: 'accepted',
    index: true
  },
  
  // Notifications
  notificationsEnabled: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound Indexes
followSchema.index({ follower: 1, following: 1 }, { unique: true });
followSchema.index({ following: 1, status: 1, createdAt: -1 });
followSchema.index({ follower: 1, status: 1, createdAt: -1 });

// Validation to prevent self-following
followSchema.pre('save', function(next) {
  if (this.follower.equals(this.following)) {
    const error = new Error('Users cannot follow themselves');
    return next(error);
  }
  next();
});

// Post-save middleware to update follow counts
followSchema.post('save', async function(doc) {
  if (this.isNew && doc.status === 'accepted') {
    // Increment follower's following count
    await mongoose.model('User').findByIdAndUpdate(
      doc.follower,
      { $inc: { followingCount: 1 } }
    );
    
    // Increment following's followers count
    await mongoose.model('User').findByIdAndUpdate(
      doc.following,
      { $inc: { followersCount: 1 } }
    );
  }
});

// Post-remove middleware to update follow counts
followSchema.post('remove', async function(doc) {
  if (doc.status === 'accepted') {
    // Decrement follower's following count
    await mongoose.model('User').findByIdAndUpdate(
      doc.follower,
      { $inc: { followingCount: -1 } }
    );
    
    // Decrement following's followers count
    await mongoose.model('User').findByIdAndUpdate(
      doc.following,
      { $inc: { followersCount: -1 } }
    );
  }
});

// Method to accept follow request
followSchema.methods.accept = function() {
  this.status = 'accepted';
  return this.save();
};

// Method to block follow
followSchema.methods.block = function() {
  this.status = 'blocked';
  return this.save();
};

// Static method to follow user
followSchema.statics.followUser = async function(followerId, followingId) {
  // Check if already following
  const existingFollow = await this.findOne({
    follower: followerId,
    following: followingId
  });
  
  if (existingFollow) {
    if (existingFollow.status === 'blocked') {
      throw new Error('Cannot follow this user');
    }
    return existingFollow;
  }
  
  // Check if target user is private
  const targetUser = await mongoose.model('User').findById(followingId);
  const status = targetUser.isPrivate ? 'pending' : 'accepted';
  
  const follow = new this({
    follower: followerId,
    following: followingId,
    status: status
  });
  
  await follow.save();
  return follow;
};

// Static method to unfollow user
followSchema.statics.unfollowUser = async function(followerId, followingId) {
  const follow = await this.findOne({
    follower: followerId,
    following: followingId
  });
  
  if (follow) {
    await follow.remove();
    return true;
  }
  
  return false;
};

// Static method to check if user is following another user
followSchema.statics.isFollowing = function(followerId, followingId) {
  return this.exists({
    follower: followerId,
    following: followingId,
    status: 'accepted'
  });
};

// Static method to get followers
followSchema.statics.getFollowers = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    status = 'accepted'
  } = options;
  
  const skip = (page - 1) * limit;
  
  return this.find({
    following: userId,
    status: status
  })
  .sort({ createdAt: -1 })
  .skip(skip)
  .limit(limit)
  .populate('follower', 'username fullName avatarUrl isVerified followersCount');
};

// Static method to get following
followSchema.statics.getFollowing = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    status = 'accepted'
  } = options;
  
  const skip = (page - 1) * limit;
  
  return this.find({
    follower: userId,
    status: status
  })
  .sort({ createdAt: -1 })
  .skip(skip)
  .limit(limit)
  .populate('following', 'username fullName avatarUrl isVerified followersCount');
};

// Static method to get mutual follows
followSchema.statics.getMutualFollows = async function(userId1, userId2) {
  const user1Following = await this.find({
    follower: userId1,
    status: 'accepted'
  }).select('following');
  
  const user1FollowingIds = user1Following.map(f => f.following);
  
  return this.find({
    follower: userId2,
    following: { $in: user1FollowingIds },
    status: 'accepted'
  })
  .populate('following', 'username fullName avatarUrl isVerified');
};

// Static method to get follow suggestions
followSchema.statics.getFollowSuggestions = async function(userId, limit = 10) {
  // Get users that the current user's followers are following
  const userFollowing = await this.find({
    follower: userId,
    status: 'accepted'
  }).select('following');
  
  const followingIds = userFollowing.map(f => f.following);
  
  // Find users followed by people the current user follows
  const suggestions = await this.aggregate([
    {
      $match: {
        follower: { $in: followingIds },
        following: { $ne: mongoose.Types.ObjectId(userId) },
        status: 'accepted'
      }
    },
    {
      $group: {
        _id: '$following',
        count: { $sum: 1 }
      }
    },
    {
      $lookup: {
        from: 'follows',
        let: { suggestedUserId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$follower', mongoose.Types.ObjectId(userId)] },
                  { $eq: ['$following', '$$suggestedUserId'] }
                ]
              }
            }
          }
        ],
        as: 'alreadyFollowing'
      }
    },
    {
      $match: {
        alreadyFollowing: { $size: 0 }
      }
    },
    {
      $sort: { count: -1 }
    },
    {
      $limit: limit
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        user: {
          _id: 1,
          username: 1,
          fullName: 1,
          avatarUrl: 1,
          isVerified: 1,
          followersCount: 1
        },
        mutualFollowsCount: '$count'
      }
    }
  ]);
  
  return suggestions;
};

// Static method to get pending follow requests
followSchema.statics.getPendingRequests = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20
  } = options;
  
  const skip = (page - 1) * limit;
  
  return this.find({
    following: userId,
    status: 'pending'
  })
  .sort({ createdAt: -1 })
  .skip(skip)
  .limit(limit)
  .populate('follower', 'username fullName avatarUrl isVerified');
};

module.exports = mongoose.model('Follow', followSchema);
