// =====================================================
// Feature Service - خدمة الميزات الديناميكية
// =====================================================

import { apiService } from './apiService';

export const featureService = {
  // الحصول على جميع الميزات
  getAllFeatures: async (params = {}) => {
    const response = await apiService.get('/features', { params });
    return response.data;
  },

  // الحصول على الميزات المفعلة فقط
  getActiveFeatures: async () => {
    const response = await apiService.get('/features/active');
    return response.data;
  },

  // الحصول على ميزة محددة
  getFeature: async (featureKey) => {
    const response = await apiService.get(`/features/${featureKey}`);
    return response.data;
  },

  // التحقق من حالة ميزة
  checkFeatureStatus: async (featureKey) => {
    const response = await apiService.get(`/features/${featureKey}/status`);
    return response.data;
  },

  // إنشاء ميزة جديدة (للمدراء فقط)
  createFeature: async (featureData) => {
    const response = await apiService.post('/features', featureData);
    return response.data;
  },

  // تحديث ميزة (للمدراء فقط)
  updateFeature: async (featureKey, updateData) => {
    const response = await apiService.put(`/features/${featureKey}`, updateData);
    return response.data;
  },

  // تفعيل/تعطيل ميزة (للمدراء فقط)
  toggleFeature: async (featureKey) => {
    const response = await apiService.patch(`/features/${featureKey}/toggle`);
    return response.data;
  },

  // حذف ميزة (للمدراء العليا فقط)
  deleteFeature: async (featureKey) => {
    const response = await apiService.delete(`/features/${featureKey}`);
    return response.data;
  },

  // إحصائيات الميزات (للمدراء فقط)
  getFeatureStats: async () => {
    const response = await apiService.get('/features/admin/stats');
    return response.data;
  },
};
