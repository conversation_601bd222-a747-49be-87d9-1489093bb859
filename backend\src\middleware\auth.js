// =====================================================
// وسطاء المصادقة والتفويض
// =====================================================

const jwt = require('jsonwebtoken');
const { query } = require('../../database/config/database');
const { AuthenticationError, AuthorizationError } = require('./errorHandler');
const logger = require('../utils/logger');

// التحقق من صحة الرمز المميز
const verifyToken = async (req, res, next) => {
  try {
    // الحصول على الرمز من الهيدر
    let token;
    
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    } else if (req.cookies.jwt) {
      token = req.cookies.jwt;
    }
    
    if (!token) {
      throw new AuthenticationError('يرجى تسجيل الدخول للوصول لهذا المورد');
    }
    
    // التحقق من صحة الرمز
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // التحقق من وجود المستخدم
    const users = await query(
      'SELECT id, username, email, is_active, is_banned, is_verified FROM users WHERE id = ?',
      [decoded.id]
    );
    
    if (users.length === 0) {
      throw new AuthenticationError('المستخدم غير موجود');
    }
    
    const user = users[0];
    
    // التحقق من حالة المستخدم
    if (!user.is_active) {
      throw new AuthenticationError('الحساب غير مفعل');
    }
    
    if (user.is_banned) {
      throw new AuthenticationError('الحساب محظور');
    }
    
    // إضافة المستخدم للطلب
    req.user = user;
    
    // تسجيل النشاط
    logger.logUserActivity(user.id, 'api_access', {
      endpoint: req.originalUrl,
      method: req.method
    });
    
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      next(new AuthenticationError('رمز المصادقة غير صحيح'));
    } else if (error.name === 'TokenExpiredError') {
      next(new AuthenticationError('انتهت صلاحية رمز المصادقة'));
    } else {
      next(error);
    }
  }
};

// التحقق من التوثيق (اختياري)
const optionalAuth = async (req, res, next) => {
  try {
    let token;
    
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    } else if (req.cookies.jwt) {
      token = req.cookies.jwt;
    }
    
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const users = await query(
        'SELECT id, username, email, is_active, is_banned FROM users WHERE id = ?',
        [decoded.id]
      );
      
      if (users.length > 0 && users[0].is_active && !users[0].is_banned) {
        req.user = users[0];
      }
    }
    
    next();
    
  } catch (error) {
    // في حالة الخطأ، نتجاهله ونكمل بدون مستخدم
    next();
  }
};

// التحقق من التوثيق المطلوب
const requireAuth = (req, res, next) => {
  if (!req.user) {
    throw new AuthenticationError('يرجى تسجيل الدخول');
  }
  next();
};

// التحقق من التوثيق المطلوب
const requireVerification = (req, res, next) => {
  if (!req.user.is_verified) {
    throw new AuthenticationError('يرجى تأكيد البريد الإلكتروني أولاً');
  }
  next();
};

// التحقق من الصلاحيات
const requireRole = (...roles) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('يرجى تسجيل الدخول');
      }
      
      // الحصول على صلاحيات المستخدم
      const userRoles = await query(
        `SELECT r.name FROM user_roles ur 
         JOIN roles r ON ur.role_id = r.id 
         WHERE ur.user_id = ?`,
        [req.user.id]
      );
      
      const userRoleNames = userRoles.map(role => role.name);
      
      // التحقق من وجود الصلاحية المطلوبة
      const hasRole = roles.some(role => userRoleNames.includes(role));
      
      if (!hasRole) {
        throw new AuthorizationError(`يتطلب صلاحية: ${roles.join(' أو ')}`);
      }
      
      req.userRoles = userRoleNames;
      next();
      
    } catch (error) {
      next(error);
    }
  };
};

// التحقق من ملكية المورد
const requireOwnership = (resourceType) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('يرجى تسجيل الدخول');
      }
      
      const resourceId = req.params.id;
      let query_str = '';
      
      switch (resourceType) {
        case 'video':
          query_str = 'SELECT user_id FROM videos WHERE id = ?';
          break;
        case 'comment':
          query_str = 'SELECT user_id FROM comments WHERE id = ?';
          break;
        case 'live':
          query_str = 'SELECT user_id FROM live_streams WHERE id = ?';
          break;
        default:
          throw new Error('نوع المورد غير مدعوم');
      }
      
      const resources = await query(query_str, [resourceId]);
      
      if (resources.length === 0) {
        throw new NotFoundError('المورد غير موجود');
      }
      
      if (resources[0].user_id !== req.user.id) {
        throw new AuthorizationError('ليس لديك صلاحية للوصول لهذا المورد');
      }
      
      next();
      
    } catch (error) {
      next(error);
    }
  };
};

// التحقق من حالة الميزة
const requireFeature = (featureKey) => {
  return async (req, res, next) => {
    try {
      const features = await query(
        'SELECT status FROM features WHERE feature_key = ?',
        [featureKey]
      );
      
      if (features.length === 0 || !features[0].status) {
        throw new AuthorizationError('هذه الميزة غير متاحة حالياً');
      }
      
      next();
      
    } catch (error) {
      next(error);
    }
  };
};

// تحديد معدل الطلبات للمستخدم
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (req, res, next) => {
    const userId = req.user?.id || req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // تنظيف الطلبات القديمة
    if (requests.has(userId)) {
      const userRequests = requests.get(userId).filter(time => time > windowStart);
      requests.set(userId, userRequests);
    } else {
      requests.set(userId, []);
    }
    
    const userRequests = requests.get(userId);
    
    if (userRequests.length >= maxRequests) {
      throw new RateLimitError('تم تجاوز الحد المسموح من الطلبات');
    }
    
    userRequests.push(now);
    next();
  };
};

// إنشاء رمز مميز
const generateToken = (payload, expiresIn = process.env.JWT_EXPIRE || '7d') => {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
};

// إنشاء رمز التحديث
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, { 
    expiresIn: process.env.JWT_REFRESH_EXPIRE || '30d' 
  });
};

// التحقق من رمز التحديث
const verifyRefreshToken = (token) => {
  return jwt.verify(token, process.env.JWT_REFRESH_SECRET);
};

module.exports = {
  verifyToken,
  optionalAuth,
  requireAuth,
  requireVerification,
  requireRole,
  requireOwnership,
  requireFeature,
  userRateLimit,
  generateToken,
  generateRefreshToken,
  verifyRefreshToken
};
