# 📚 TikTok Clone API Documentation

## 🌐 Base URL
```
Development: http://localhost:5000/api
Production: https://api.tiktokclone.com/api
```

## 🔐 Authentication

### Register User
```http
POST /auth/register
```

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "fullName": "string",
  "phone": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "token": "jwt_token",
  "refreshToken": "refresh_token",
  "user": {
    "id": "user_id",
    "username": "string",
    "email": "string",
    "fullName": "string",
    "isVerified": false
  }
}
```

### Login User
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "string",
  "password": "string",
  "remember": "boolean (optional)"
}
```

### Refresh Token
```http
POST /auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "string"
}
```

### Logout
```http
POST /auth/logout
```

**Headers:**
```
Authorization: Bearer <token>
```

## 👤 Users

### Get User Profile
```http
GET /users/:id
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "string",
    "username": "string",
    "fullName": "string",
    "bio": "string",
    "avatarUrl": "string",
    "followersCount": "number",
    "followingCount": "number",
    "videosCount": "number",
    "isVerified": "boolean",
    "isFollowing": "boolean"
  }
}
```

### Update Profile
```http
PUT /users/profile
```

**Headers:**
```
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**Request Body:**
```json
{
  "fullName": "string (optional)",
  "bio": "string (optional)",
  "website": "string (optional)",
  "avatar": "file (optional)"
}
```

### Follow User
```http
POST /users/:id/follow
```

### Unfollow User
```http
DELETE /users/:id/follow
```

### Get Followers
```http
GET /users/:id/followers?page=1&limit=20
```

### Get Following
```http
GET /users/:id/following?page=1&limit=20
```

## 🎥 Videos

### Get Feed
```http
GET /videos/feed?page=1&limit=20
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `category`: Video category filter

**Response:**
```json
{
  "success": true,
  "videos": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "videoUrl": "string",
      "thumbnailUrl": "string",
      "duration": "number",
      "viewsCount": "number",
      "likesCount": "number",
      "commentsCount": "number",
      "sharesCount": "number",
      "user": {
        "id": "string",
        "username": "string",
        "avatarUrl": "string",
        "isVerified": "boolean"
      },
      "music": {
        "id": "string",
        "title": "string",
        "artist": "string"
      },
      "hashtags": ["string"],
      "isLiked": "boolean",
      "createdAt": "string"
    }
  ],
  "pagination": {
    "page": "number",
    "limit": "number",
    "total": "number",
    "pages": "number"
  }
}
```

### Upload Video
```http
POST /videos/upload
```

**Headers:**
```
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**Request Body:**
```json
{
  "video": "file",
  "thumbnail": "file (optional)",
  "title": "string (optional)",
  "description": "string (optional)",
  "hashtags": "string (comma-separated)",
  "musicId": "string (optional)",
  "privacy": "public|friends|private"
}
```

### Get Video Details
```http
GET /videos/:id
```

### Like Video
```http
POST /videos/:id/like
```

### Unlike Video
```http
DELETE /videos/:id/like
```

### Share Video
```http
POST /videos/:id/share
```

### Delete Video
```http
DELETE /videos/:id
```

## 💬 Comments

### Get Comments
```http
GET /videos/:videoId/comments?page=1&limit=20
```

### Add Comment
```http
POST /videos/:videoId/comments
```

**Request Body:**
```json
{
  "content": "string",
  "parentId": "string (optional for replies)"
}
```

### Like Comment
```http
POST /comments/:id/like
```

### Delete Comment
```http
DELETE /comments/:id
```

## 🎵 Music

### Get Trending Music
```http
GET /music/trending?page=1&limit=20
```

### Search Music
```http
GET /music/search?q=query&page=1&limit=20
```

### Get Music Details
```http
GET /music/:id
```

## 🔍 Search

### Search Everything
```http
GET /search?q=query&type=all&page=1&limit=20
```

**Query Parameters:**
- `q`: Search query
- `type`: `all|users|videos|hashtags|music`
- `page`: Page number
- `limit`: Items per page

### Search Users
```http
GET /search/users?q=query&page=1&limit=20
```

### Search Videos
```http
GET /search/videos?q=query&page=1&limit=20
```

### Search Hashtags
```http
GET /search/hashtags?q=query&page=1&limit=20
```

## 📱 Live Streaming

### Start Live Stream
```http
POST /live/start
```

**Request Body:**
```json
{
  "title": "string",
  "description": "string (optional)",
  "isPrivate": "boolean"
}
```

### End Live Stream
```http
POST /live/:streamId/end
```

### Get Live Streams
```http
GET /live?page=1&limit=20
```

### Join Live Stream
```http
POST /live/:streamId/join
```

## 💌 Messages

### Get Conversations
```http
GET /messages/conversations?page=1&limit=20
```

### Get Messages
```http
GET /messages/conversations/:conversationId?page=1&limit=50
```

### Send Message
```http
POST /messages/conversations/:conversationId
```

**Request Body:**
```json
{
  "content": "string",
  "type": "text|image|video|audio",
  "media": "file (optional)"
}
```

## 🔔 Notifications

### Get Notifications
```http
GET /notifications?page=1&limit=20&unread=false
```

### Mark as Read
```http
PUT /notifications/:id/read
```

### Mark All as Read
```http
PUT /notifications/read-all
```

## 📊 Analytics

### Get User Analytics
```http
GET /analytics/user
```

### Get Video Analytics
```http
GET /analytics/videos/:videoId
```

## ⚙️ Features (Dynamic)

### Get Available Features
```http
GET /features
```

### Toggle Feature
```http
POST /features/:featureKey/toggle
```

## 🚨 Error Responses

All endpoints may return these error responses:

### 400 Bad Request
```json
{
  "success": false,
  "message": "Validation error message",
  "errors": {
    "field": ["error message"]
  }
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Access denied"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Resource not found"
}
```

### 429 Too Many Requests
```json
{
  "success": false,
  "message": "Rate limit exceeded",
  "retryAfter": 60
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error"
}
```

## 📝 Rate Limiting

- **General API**: 100 requests per 15 minutes per IP
- **Authentication**: 5 requests per 15 minutes per IP
- **Upload**: 10 requests per hour per user
- **Live Streaming**: 3 streams per day per user

## 🔒 Security Headers

All responses include security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`

## 📱 Mobile App Integration

### Deep Links
```
tiktokclone://video/:videoId
tiktokclone://user/:username
tiktokclone://hashtag/:hashtag
tiktokclone://live/:streamId
```

### Push Notifications
Register device token:
```http
POST /notifications/register-device
```

**Request Body:**
```json
{
  "token": "device_token",
  "platform": "ios|android"
}
```
