# 🚀 دليل البدء السريع - TikTok Clone

## 📋 نظرة عامة

مرحباً بك في مشروع TikTok Clone الثوري! هذا الدليل سيساعدك على تشغيل جميع مكونات المشروع بسهولة.

## 🔧 المتطلبات الأساسية

قبل البدء، تأكد من تثبيت:

### للتطوير العام
- **Node.js** 18.0.0 أو أحدث
- **npm** أو **yarn**
- **Git**

### لتطبيق Flutter
- **Flutter SDK** 3.16.0 أو أحدث
- **Android Studio** (للأندرويد)
- **Xcode** (لـ iOS - Mac فقط)

### لقاعدة البيانات
- **MongoDB** 6.0 أو أحدث
- **Redis** 7.0 أو أحدث

## 🏗️ إعداد البيئة

### 1. استن<PERSON><PERSON><PERSON> المشروع
```bash
git clone <repository-url>
cd tiktok_clone
```

### 2. إعداد قاعدة البيانات
```bash
# تشغيل MongoDB
mongod

# تشغيل Redis
redis-server
```

## 🔧 تشغيل Backend API

```bash
# الانتقال لمجلد Backend
cd backend

# تثبيت المكتبات
npm install

# نسخ ملف البيئة
cp .env.example .env

# تحرير متغيرات البيئة
nano .env

# تشغيل الخادم
npm run dev
```

### متغيرات البيئة المطلوبة (.env)
```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/tiktok_clone
REDIS_URL=redis://localhost:6379
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_refresh_secret_here
```

## 📱 تشغيل تطبيق Flutter

```bash
# الانتقال لمجلد التطبيق
cd mobile_app

# تثبيت المكتبات
flutter pub get

# تشغيل التطبيق (Android)
flutter run

# تشغيل التطبيق (iOS)
flutter run -d ios
```

### إعداد إضافي لـ Flutter
```bash
# فحص إعداد Flutter
flutter doctor

# إنشاء مشروع للويب (اختياري)
flutter config --enable-web
```

## 🌐 تشغيل موقع الويب

```bash
# الانتقال لمجلد الموقع
cd web_app

# تثبيت المكتبات
npm install

# تشغيل الموقع
npm start
```

الموقع سيعمل على: `http://localhost:3000`

## ⚙️ تشغيل لوحة التحكم الإدارية

```bash
# الانتقال لمجلد لوحة التحكم
cd admin_panel

# تثبيت المكتبات
npm install

# تشغيل لوحة التحكم
npm start
```

لوحة التحكم ستعمل على: `http://localhost:3001`

## 🔗 الروابط والمنافذ

| المكون | الرابط | المنفذ |
|--------|--------|--------|
| Backend API | http://localhost:5000 | 5000 |
| موقع الويب | http://localhost:3000 | 3000 |
| لوحة التحكم | http://localhost:3001 | 3001 |
| تطبيق Flutter | محاكي/جهاز | - |

## 🧪 اختبار النظام

### اختبار Backend API
```bash
cd backend
npm test
```

### اختبار موقع الويب
```bash
cd web_app
npm test
```

### اختبار تطبيق Flutter
```bash
cd mobile_app
flutter test
```

## 🐛 حل المشاكل الشائعة

### مشكلة في Backend
```bash
# تحقق من حالة MongoDB
mongod --version

# تحقق من حالة Redis
redis-cli ping
```

### مشكلة في Flutter
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# إعادة بناء المشروع
flutter build apk
```

### مشكلة في React
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

## 📊 مراقبة النظام

### عرض سجلات Backend
```bash
cd backend
npm run logs
```

### مراقبة الأداء
- Backend API: `http://localhost:5000/health`
- قاعدة البيانات: MongoDB Compass
- Redis: Redis CLI

## 🎯 الخطوات التالية

1. **تخصيص التصميم** - عدل الألوان والشعارات
2. **إضافة ميزات جديدة** - استخدم نظام الميزات الديناميكية
3. **تكوين الإنتاج** - إعداد خوادم الإنتاج
4. **اختبارات شاملة** - تشغيل جميع الاختبارات

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. تحقق من [التوثيق](docs/)
2. راجع [الأسئلة الشائعة](docs/FAQ.md)
3. تواصل مع فريق الدعم

---

🎉 **مبروك! مشروعك جاهز للعمل!** 🎉

استمتع بتطوير تطبيق TikTok Clone الثوري الخاص بك!
