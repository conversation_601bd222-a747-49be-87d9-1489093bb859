// =====================================================
// Like Model - نموذج الإعجاب
// =====================================================

const mongoose = require('mongoose');

const likeSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  target: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    refPath: 'targetType',
    index: true
  },
  targetType: {
    type: String,
    required: true,
    enum: ['Video', 'Comment'],
    index: true
  },
  
  // Like Type (for future emoji reactions)
  type: {
    type: String,
    enum: ['like', 'love', 'laugh', 'wow', 'sad', 'angry'],
    default: 'like',
    index: true
  }
}, {
  timestamps: true
});

// Compound Indexes
likeSchema.index({ user: 1, target: 1, targetType: 1 }, { unique: true });
likeSchema.index({ target: 1, targetType: 1, createdAt: -1 });
likeSchema.index({ user: 1, createdAt: -1 });

// Post-save middleware to update like counts
likeSchema.post('save', async function(doc) {
  if (this.isNew) {
    if (doc.targetType === 'Video') {
      await mongoose.model('Video').findByIdAndUpdate(
        doc.target,
        { $inc: { likesCount: 1 } }
      );
    } else if (doc.targetType === 'Comment') {
      await mongoose.model('Comment').findByIdAndUpdate(
        doc.target,
        { $inc: { likesCount: 1 } }
      );
    }
  }
});

// Post-remove middleware to update like counts
likeSchema.post('remove', async function(doc) {
  if (doc.targetType === 'Video') {
    await mongoose.model('Video').findByIdAndUpdate(
      doc.target,
      { $inc: { likesCount: -1 } }
    );
  } else if (doc.targetType === 'Comment') {
    await mongoose.model('Comment').findByIdAndUpdate(
      doc.target,
      { $inc: { likesCount: -1 } }
    );
  }
});

// Static method to toggle like
likeSchema.statics.toggleLike = async function(userId, targetId, targetType, likeType = 'like') {
  const existingLike = await this.findOne({
    user: userId,
    target: targetId,
    targetType: targetType
  });
  
  if (existingLike) {
    // Unlike
    await existingLike.remove();
    return { action: 'unliked', like: null };
  } else {
    // Like
    const newLike = new this({
      user: userId,
      target: targetId,
      targetType: targetType,
      type: likeType
    });
    await newLike.save();
    return { action: 'liked', like: newLike };
  }
};

// Static method to check if user liked target
likeSchema.statics.isLikedByUser = function(userId, targetId, targetType) {
  return this.exists({
    user: userId,
    target: targetId,
    targetType: targetType
  });
};

// Static method to get likes for target
likeSchema.statics.getLikesForTarget = function(targetId, targetType, options = {}) {
  const {
    page = 1,
    limit = 20,
    type = null
  } = options;
  
  const skip = (page - 1) * limit;
  
  const query = {
    target: targetId,
    targetType: targetType
  };
  
  if (type) {
    query.type = type;
  }
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('user', 'username fullName avatarUrl isVerified');
};

// Static method to get user's likes
likeSchema.statics.getUserLikes = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    targetType = null
  } = options;
  
  const skip = (page - 1) * limit;
  
  const query = { user: userId };
  
  if (targetType) {
    query.targetType = targetType;
  }
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('target');
};

// Static method to get like statistics
likeSchema.statics.getLikeStats = function(targetId, targetType) {
  return this.aggregate([
    {
      $match: {
        target: mongoose.Types.ObjectId(targetId),
        targetType: targetType
      }
    },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$count' },
        breakdown: {
          $push: {
            type: '$_id',
            count: '$count'
          }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Like', likeSchema);
