// =====================================================
// معالج الأخطاء العام
// =====================================================

const logger = require('../utils/logger');

// فئات الأخطاء المخصصة
class AppError extends Error {
  constructor(message, statusCode, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, field = null) {
    super(message, 400);
    this.field = field;
    this.type = 'validation';
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'غير مصرح لك بالوصول') {
    super(message, 401);
    this.type = 'authentication';
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'ليس لديك صلاحية للقيام بهذا الإجراء') {
    super(message, 403);
    this.type = 'authorization';
  }
}

class NotFoundError extends AppError {
  constructor(message = 'المورد غير موجود') {
    super(message, 404);
    this.type = 'not_found';
  }
}

class ConflictError extends AppError {
  constructor(message = 'تعارض في البيانات') {
    super(message, 409);
    this.type = 'conflict';
  }
}

class RateLimitError extends AppError {
  constructor(message = 'تم تجاوز الحد المسموح من الطلبات') {
    super(message, 429);
    this.type = 'rate_limit';
  }
}

// معالج أخطاء قاعدة البيانات
const handleDatabaseError = (error) => {
  let message = 'خطأ في قاعدة البيانات';
  let statusCode = 500;
  
  // خطأ في الاتصال
  if (error.code === 'ECONNREFUSED') {
    message = 'فشل الاتصال بقاعدة البيانات';
    statusCode = 503;
  }
  
  // خطأ في المفتاح المكرر
  else if (error.code === 'ER_DUP_ENTRY') {
    message = 'البيانات موجودة مسبقاً';
    statusCode = 409;
  }
  
  // خطأ في المفتاح الخارجي
  else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
    message = 'مرجع غير صحيح في البيانات';
    statusCode = 400;
  }
  
  // خطأ في صيغة SQL
  else if (error.code === 'ER_PARSE_ERROR') {
    message = 'خطأ في صيغة الاستعلام';
    statusCode = 500;
  }
  
  // خطأ في الجدول غير موجود
  else if (error.code === 'ER_NO_SUCH_TABLE') {
    message = 'الجدول غير موجود';
    statusCode = 500;
  }
  
  return new AppError(message, statusCode);
};

// معالج أخطاء JWT
const handleJWTError = (error) => {
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('رمز المصادقة غير صحيح');
  }
  
  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('انتهت صلاحية رمز المصادقة');
  }
  
  if (error.name === 'NotBeforeError') {
    return new AuthenticationError('رمز المصادقة غير نشط بعد');
  }
  
  return new AuthenticationError('خطأ في المصادقة');
};

// معالج أخطاء التحقق من البيانات
const handleValidationError = (error) => {
  if (error.details && error.details[0]) {
    const message = error.details[0].message;
    const field = error.details[0].path ? error.details[0].path[0] : null;
    return new ValidationError(message, field);
  }
  
  return new ValidationError('بيانات غير صحيحة');
};

// معالج أخطاء Multer (رفع الملفات)
const handleMulterError = (error) => {
  if (error.code === 'LIMIT_FILE_SIZE') {
    return new ValidationError('حجم الملف كبير جداً');
  }
  
  if (error.code === 'LIMIT_FILE_COUNT') {
    return new ValidationError('عدد الملفات كبير جداً');
  }
  
  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return new ValidationError('نوع الملف غير مدعوم');
  }
  
  return new ValidationError('خطأ في رفع الملف');
};

// إرسال استجابة الخطأ في بيئة التطوير
const sendErrorDev = (err, res) => {
  res.status(err.statusCode).json({
    status: err.status,
    error: err,
    message: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString()
  });
};

// إرسال استجابة الخطأ في بيئة الإنتاج
const sendErrorProd = (err, res) => {
  // أخطاء تشغيلية: إرسال رسالة للعميل
  if (err.isOperational) {
    res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
      timestamp: new Date().toISOString()
    });
  } 
  // أخطاء برمجية: عدم تسريب التفاصيل
  else {
    logger.error('Programming Error', err);
    
    res.status(500).json({
      status: 'error',
      message: 'حدث خطأ في الخادم',
      timestamp: new Date().toISOString()
    });
  }
};

// معالج الأخطاء الرئيسي
const errorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';
  
  // تسجيل الخطأ
  logger.error('Error Handler', {
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  });
  
  let error = { ...err };
  error.message = err.message;
  
  // معالجة أنواع الأخطاء المختلفة
  if (err.code && err.code.startsWith('ER_')) {
    error = handleDatabaseError(err);
  }
  
  if (err.name && err.name.includes('JsonWebToken')) {
    error = handleJWTError(err);
  }
  
  if (err.isJoi) {
    error = handleValidationError(err);
  }
  
  if (err.code && err.code.startsWith('LIMIT_')) {
    error = handleMulterError(err);
  }
  
  // إرسال الاستجابة حسب البيئة
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
};

// معالج الأخطاء غير المتوقعة
const handleUncaughtException = () => {
  process.on('uncaughtException', (err) => {
    logger.error('Uncaught Exception', err);
    console.log('💥 Uncaught Exception! Shutting down...');
    process.exit(1);
  });
};

const handleUnhandledRejection = () => {
  process.on('unhandledRejection', (err) => {
    logger.error('Unhandled Rejection', err);
    console.log('💥 Unhandled Rejection! Shutting down...');
    process.exit(1);
  });
};

// تصدير الوحدات
module.exports = {
  errorHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  handleUncaughtException,
  handleUnhandledRejection
};
