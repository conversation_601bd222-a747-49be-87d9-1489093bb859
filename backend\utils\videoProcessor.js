// =====================================================
// Video Processor Utilities - أدوات معالجة الفيديو
// =====================================================

const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const sharp = require('sharp');

// تكوين مسارات FFmpeg
if (process.env.FFMPEG_PATH) {
  ffmpeg.setFfmpegPath(process.env.FFMPEG_PATH);
}
if (process.env.FFPROBE_PATH) {
  ffmpeg.setFfprobePath(process.env.FFPROBE_PATH);
}

// إعدادات معالجة الفيديو
const VIDEO_SETTINGS = {
  qualities: {
    '240p': { width: 426, height: 240, bitrate: '400k' },
    '360p': { width: 640, height: 360, bitrate: '800k' },
    '480p': { width: 854, height: 480, bitrate: '1200k' },
    '720p': { width: 1280, height: 720, bitrate: '2500k' },
    '1080p': { width: 1920, height: 1080, bitrate: '5000k' }
  },
  formats: ['mp4', 'webm'],
  maxDuration: 600, // 10 minutes
  maxFileSize: 500 * 1024 * 1024, // 500MB
  thumbnailCount: 5,
  previewDuration: 3 // 3 seconds preview
};

// رفع ومعالجة الفيديو
const uploadVideo = async (videoFile, thumbnailFile = null) => {
  try {
    const videoId = crypto.randomUUID();
    const uploadDir = path.join('uploads', 'videos', videoId);
    
    // إنشاء مجلد الفيديو
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // نسخ الملف الأصلي
    const originalPath = path.join(uploadDir, 'original' + path.extname(videoFile.originalname));
    fs.copyFileSync(videoFile.path, originalPath);

    // الحصول على معلومات الفيديو
    const videoInfo = await getVideoInfo(originalPath);
    
    // التحقق من صحة الفيديو
    validateVideo(videoInfo);

    // معالجة الصورة المصغرة
    let thumbnailUrl = null;
    if (thumbnailFile) {
      thumbnailUrl = await processThumbnail(thumbnailFile, uploadDir);
    } else {
      thumbnailUrl = await generateThumbnail(originalPath, uploadDir);
    }

    // إنشاء معاينة الفيديو
    const previewUrl = await generatePreview(originalPath, uploadDir);

    return {
      videoId,
      videoUrl: `/uploads/videos/${videoId}/original${path.extname(videoFile.originalname)}`,
      thumbnailUrl,
      previewUrl,
      duration: videoInfo.duration,
      fileSize: videoInfo.size,
      resolution: `${videoInfo.width}x${videoInfo.height}`,
      format: videoInfo.format,
      originalPath,
      uploadDir
    };
  } catch (error) {
    console.error('Upload video error:', error);
    throw new Error('فشل في رفع الفيديو: ' + error.message);
  }
};

// معالجة الفيديو في الخلفية
const processVideo = async (videoId, originalPath) => {
  try {
    const uploadDir = path.dirname(originalPath);
    
    // إنشاء نسخ بجودات مختلفة
    await generateMultipleQualities(originalPath, uploadDir);
    
    // إنشاء صور مصغرة متعددة
    await generateMultipleThumbnails(originalPath, uploadDir);
    
    // تحسين الفيديو للويب
    await optimizeForWeb(originalPath, uploadDir);
    
    // تحديث حالة المعالجة في قاعدة البيانات
    const Video = require('../models/Video');
    await Video.findByIdAndUpdate(videoId, {
      processingStatus: 'completed',
      processedAt: new Date()
    });

    console.log(`Video processing completed for ${videoId}`);
  } catch (error) {
    console.error('Process video error:', error);
    
    // تحديث حالة الخطأ
    const Video = require('../models/Video');
    await Video.findByIdAndUpdate(videoId, {
      processingStatus: 'failed',
      processingError: error.message
    });
  }
};

// الحصول على معلومات الفيديو
const getVideoInfo = (filePath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(err);
        return;
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
      const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

      if (!videoStream) {
        reject(new Error('لا يحتوي الملف على مسار فيديو صالح'));
        return;
      }

      const stats = fs.statSync(filePath);

      resolve({
        duration: parseFloat(metadata.format.duration),
        size: stats.size,
        width: videoStream.width,
        height: videoStream.height,
        format: metadata.format.format_name,
        bitrate: parseInt(metadata.format.bit_rate),
        fps: eval(videoStream.r_frame_rate),
        hasAudio: !!audioStream,
        codec: videoStream.codec_name
      });
    });
  });
};

// التحقق من صحة الفيديو
const validateVideo = (videoInfo) => {
  if (videoInfo.duration > VIDEO_SETTINGS.maxDuration) {
    throw new Error(`مدة الفيديو تتجاوز الحد المسموح (${VIDEO_SETTINGS.maxDuration} ثانية)`);
  }

  if (videoInfo.size > VIDEO_SETTINGS.maxFileSize) {
    throw new Error('حجم الفيديو كبير جداً');
  }

  if (videoInfo.width < 240 || videoInfo.height < 240) {
    throw new Error('دقة الفيديو منخفضة جداً');
  }
};

// معالجة الصورة المصغرة
const processThumbnail = async (thumbnailFile, uploadDir) => {
  try {
    const thumbnailPath = path.join(uploadDir, 'thumbnail.jpg');
    
    await sharp(thumbnailFile.path)
      .resize(640, 360, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);

    // حذف الملف المؤقت
    fs.unlinkSync(thumbnailFile.path);

    return thumbnailPath.replace(/\\/g, '/');
  } catch (error) {
    console.error('Process thumbnail error:', error);
    throw new Error('فشل في معالجة الصورة المصغرة');
  }
};

// إنشاء صورة مصغرة من الفيديو
const generateThumbnail = (videoPath, uploadDir) => {
  return new Promise((resolve, reject) => {
    const thumbnailPath = path.join(uploadDir, 'thumbnail.jpg');
    
    ffmpeg(videoPath)
      .screenshots({
        count: 1,
        folder: uploadDir,
        filename: 'thumbnail.jpg',
        size: '640x360'
      })
      .on('end', () => {
        resolve(thumbnailPath.replace(/\\/g, '/'));
      })
      .on('error', (err) => {
        reject(new Error('فشل في إنشاء الصورة المصغرة: ' + err.message));
      });
  });
};

// إنشاء معاينة الفيديو
const generatePreview = (videoPath, uploadDir) => {
  return new Promise((resolve, reject) => {
    const previewPath = path.join(uploadDir, 'preview.mp4');
    
    ffmpeg(videoPath)
      .setStartTime(0)
      .setDuration(VIDEO_SETTINGS.previewDuration)
      .size('640x360')
      .videoBitrate('800k')
      .format('mp4')
      .output(previewPath)
      .on('end', () => {
        resolve(previewPath.replace(/\\/g, '/'));
      })
      .on('error', (err) => {
        reject(new Error('فشل في إنشاء معاينة الفيديو: ' + err.message));
      })
      .run();
  });
};

// إنشاء نسخ بجودات مختلفة
const generateMultipleQualities = async (videoPath, uploadDir) => {
  const videoInfo = await getVideoInfo(videoPath);
  const promises = [];

  for (const [quality, settings] of Object.entries(VIDEO_SETTINGS.qualities)) {
    // تخطي الجودات الأعلى من الفيديو الأصلي
    if (settings.height > videoInfo.height) continue;

    const outputPath = path.join(uploadDir, `${quality}.mp4`);
    
    const promise = new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .size(`${settings.width}x${settings.height}`)
        .videoBitrate(settings.bitrate)
        .format('mp4')
        .output(outputPath)
        .on('end', resolve)
        .on('error', reject)
        .run();
    });

    promises.push(promise);
  }

  await Promise.all(promises);
};

// إنشاء صور مصغرة متعددة
const generateMultipleThumbnails = (videoPath, uploadDir) => {
  return new Promise((resolve, reject) => {
    const thumbnailsDir = path.join(uploadDir, 'thumbnails');
    if (!fs.existsSync(thumbnailsDir)) {
      fs.mkdirSync(thumbnailsDir);
    }

    ffmpeg(videoPath)
      .screenshots({
        count: VIDEO_SETTINGS.thumbnailCount,
        folder: thumbnailsDir,
        filename: 'thumb_%i.jpg',
        size: '320x180'
      })
      .on('end', resolve)
      .on('error', reject);
  });
};

// تحسين الفيديو للويب
const optimizeForWeb = (videoPath, uploadDir) => {
  return new Promise((resolve, reject) => {
    const optimizedPath = path.join(uploadDir, 'web_optimized.mp4');
    
    ffmpeg(videoPath)
      .format('mp4')
      .videoCodec('libx264')
      .audioCodec('aac')
      .addOption('-movflags', '+faststart') // للتشغيل السريع
      .addOption('-preset', 'fast')
      .addOption('-crf', '23') // جودة متوازنة
      .output(optimizedPath)
      .on('end', resolve)
      .on('error', reject)
      .run();
  });
};

// ضغط الفيديو
const compressVideo = (inputPath, outputPath, quality = 'medium') => {
  return new Promise((resolve, reject) => {
    const qualitySettings = {
      low: { crf: 28, preset: 'fast' },
      medium: { crf: 23, preset: 'medium' },
      high: { crf: 18, preset: 'slow' }
    };

    const settings = qualitySettings[quality] || qualitySettings.medium;

    ffmpeg(inputPath)
      .videoCodec('libx264')
      .audioCodec('aac')
      .addOption('-crf', settings.crf)
      .addOption('-preset', settings.preset)
      .format('mp4')
      .output(outputPath)
      .on('end', resolve)
      .on('error', reject)
      .run();
  });
};

// تحويل تنسيق الفيديو
const convertFormat = (inputPath, outputPath, format) => {
  return new Promise((resolve, reject) => {
    ffmpeg(inputPath)
      .format(format)
      .output(outputPath)
      .on('end', resolve)
      .on('error', reject)
      .run();
  });
};

// إضافة علامة مائية
const addWatermark = (videoPath, watermarkPath, outputPath) => {
  return new Promise((resolve, reject) => {
    ffmpeg(videoPath)
      .input(watermarkPath)
      .complexFilter([
        '[1:v]scale=100:50[watermark]',
        '[0:v][watermark]overlay=W-w-10:H-h-10'
      ])
      .output(outputPath)
      .on('end', resolve)
      .on('error', reject)
      .run();
  });
};

// حذف ملفات الفيديو
const deleteVideoFiles = async (uploadDir) => {
  try {
    if (fs.existsSync(uploadDir)) {
      fs.rmSync(uploadDir, { recursive: true, force: true });
    }
  } catch (error) {
    console.error('Error deleting video files:', error);
  }
};

// الحصول على مدة الفيديو
const getVideoDuration = (filePath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(parseFloat(metadata.format.duration));
    });
  });
};

module.exports = {
  uploadVideo,
  processVideo,
  getVideoInfo,
  validateVideo,
  processThumbnail,
  generateThumbnail,
  generatePreview,
  generateMultipleQualities,
  generateMultipleThumbnails,
  optimizeForWeb,
  compressVideo,
  convertFormat,
  addWatermark,
  deleteVideoFiles,
  getVideoDuration,
  VIDEO_SETTINGS
};
