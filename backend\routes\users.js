// =====================================================
// User Routes - مسارات المستخدمين
// =====================================================

const express = require('express');
const { body, query } = require('express-validator');
const userController = require('../controllers/userController');
const auth = require('../middleware/auth');
const optionalAuth = require('../middleware/optionalAuth');
const upload = require('../middleware/upload');
const rateLimit = require('../middleware/rateLimit');

const router = express.Router();

// معدل محدود للمتابعة
const followLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 20, // 20 follow actions per minute
  message: 'تم تجاوز عدد عمليات المتابعة المسموحة في الدقيقة'
});

// الحصول على ملف المستخدم
router.get('/:identifier', optionalAuth, userController.getUserProfile);

// تحديث الملف الشخصي
router.put('/profile', auth, upload.fields([
  { name: 'avatar', maxCount: 1 },
  { name: 'cover', maxCount: 1 }
]), [
  body('fullName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون بين 2 و 100 حرف')
    .trim(),
  
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('النبذة الشخصية يجب أن تكون أقل من 500 حرف')
    .trim(),
  
  body('website')
    .optional()
    .isURL()
    .withMessage('رابط الموقع غير صحيح'),
  
  body('location')
    .optional()
    .isLength({ max: 100 })
    .withMessage('الموقع يجب أن يكون أقل من 100 حرف')
    .trim(),
  
  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('تاريخ الميلاد غير صحيح'),
  
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('الجنس يجب أن يكون male أو female أو other'),
  
  body('language')
    .optional()
    .isIn(['ar', 'en', 'fr', 'es', 'de'])
    .withMessage('اللغة غير مدعومة'),
  
  body('isPrivate')
    .optional()
    .isBoolean()
    .withMessage('حالة الخصوصية يجب أن تكون true أو false')
], userController.updateProfile);

// متابعة مستخدم
router.post('/:userId/follow', auth, followLimiter, userController.followUser);

// إلغاء متابعة مستخدم
router.delete('/:userId/follow', auth, userController.unfollowUser);

// الحصول على المتابعين
router.get('/:userId/followers', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], userController.getFollowers);

// الحصول على المتابَعين
router.get('/:userId/following', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], userController.getFollowing);

// البحث عن المستخدمين
router.get('/search/users', [
  query('q')
    .notEmpty()
    .withMessage('نص البحث مطلوب')
    .isLength({ min: 1, max: 100 })
    .withMessage('نص البحث يجب أن يكون بين 1 و 100 حرف'),
  
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], userController.searchUsers);

// اقتراحات المتابعة
router.get('/suggestions/follow', auth, [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 50')
], userController.getFollowSuggestions);

// الحصول على طلبات المتابعة المعلقة
router.get('/follow-requests/pending', auth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], userController.getPendingFollowRequests);

// قبول طلب المتابعة
router.post('/follow-requests/:followerId/accept', auth, userController.acceptFollowRequest);

// رفض طلب المتابعة
router.post('/follow-requests/:followerId/reject', auth, userController.rejectFollowRequest);

// حظر مستخدم
router.post('/:userId/block', auth, userController.blockUser);

// إلغاء حظر مستخدم
router.delete('/:userId/block', auth, userController.unblockUser);

// الحصول على المستخدمين المحظورين
router.get('/blocked/users', auth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], async (req, res) => {
  try {
    // المستخدمين المحظورين
    res.json({ message: 'المستخدمين المحظورين قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب المستخدمين المحظورين' });
  }
});

// الحصول على إحصائيات المستخدم
router.get('/:userId/stats', optionalAuth, async (req, res) => {
  try {
    // إحصائيات المستخدم
    res.json({ message: 'إحصائيات المستخدم قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب إحصائيات المستخدم' });
  }
});

// الحصول على نشاط المستخدم
router.get('/:userId/activity', auth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100'),
  
  query('type')
    .optional()
    .isIn(['like', 'comment', 'follow', 'share', 'view'])
    .withMessage('نوع النشاط غير صحيح')
], async (req, res) => {
  try {
    // نشاط المستخدم
    res.json({ message: 'نشاط المستخدم قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب نشاط المستخدم' });
  }
});

// تحديث إعدادات الخصوصية
router.put('/privacy/settings', auth, [
  body('isPrivate')
    .optional()
    .isBoolean()
    .withMessage('حالة الخصوصية يجب أن تكون true أو false'),
  
  body('allowMessages')
    .optional()
    .isBoolean()
    .withMessage('السماح بالرسائل يجب أن يكون true أو false'),
  
  body('allowComments')
    .optional()
    .isBoolean()
    .withMessage('السماح بالتعليقات يجب أن يكون true أو false'),
  
  body('allowMentions')
    .optional()
    .isBoolean()
    .withMessage('السماح بالإشارات يجب أن يكون true أو false'),
  
  body('showOnlineStatus')
    .optional()
    .isBoolean()
    .withMessage('إظهار حالة الاتصال يجب أن يكون true أو false')
], async (req, res) => {
  try {
    // تحديث إعدادات الخصوصية
    res.json({ message: 'تحديث إعدادات الخصوصية قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تحديث إعدادات الخصوصية' });
  }
});

// تحديث إعدادات الإشعارات
router.put('/notifications/settings', auth, [
  body('pushNotifications')
    .optional()
    .isBoolean()
    .withMessage('الإشعارات الفورية يجب أن تكون true أو false'),
  
  body('emailNotifications')
    .optional()
    .isBoolean()
    .withMessage('إشعارات البريد الإلكتروني يجب أن تكون true أو false'),
  
  body('smsNotifications')
    .optional()
    .isBoolean()
    .withMessage('إشعارات الرسائل النصية يجب أن تكون true أو false'),
  
  body('likeNotifications')
    .optional()
    .isBoolean()
    .withMessage('إشعارات الإعجاب يجب أن تكون true أو false'),
  
  body('commentNotifications')
    .optional()
    .isBoolean()
    .withMessage('إشعارات التعليقات يجب أن تكون true أو false'),
  
  body('followNotifications')
    .optional()
    .isBoolean()
    .withMessage('إشعارات المتابعة يجب أن تكون true أو false')
], async (req, res) => {
  try {
    // تحديث إعدادات الإشعارات
    res.json({ message: 'تحديث إعدادات الإشعارات قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تحديث إعدادات الإشعارات' });
  }
});

// الحصول على المستخدمين المقترحين
router.get('/suggestions/discover', optionalAuth, [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 50'),
  
  query('category')
    .optional()
    .isIn(['trending', 'new', 'verified', 'nearby'])
    .withMessage('فئة الاقتراحات غير صحيحة')
], async (req, res) => {
  try {
    // المستخدمين المقترحين
    res.json({ message: 'المستخدمين المقترحين قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب المستخدمين المقترحين' });
  }
});

// التحقق من توفر اسم المستخدم
router.get('/check/username/:username', async (req, res) => {
  try {
    // التحقق من توفر اسم المستخدم
    res.json({ message: 'التحقق من توفر اسم المستخدم قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في التحقق من اسم المستخدم' });
  }
});

module.exports = router;
