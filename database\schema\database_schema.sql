-- =====================================================
-- TikTok Clone - مخطط قاعدة البيانات الكامل
-- =====================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS tiktok_clone_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE tiktok_clone_db;

-- =====================================================
-- جدول الميزات الديناميكية
-- =====================================================
CREATE TABLE features (
    id INT PRIMARY KEY AUTO_INCREMENT,
    feature_key VARCHAR(100) UNIQUE NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    status TINYINT(1) DEFAULT 1 COMMENT '1=مفعل, 0=معطل',
    settings JSON COMMENT 'إعدادات الميزة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_feature_key (feature_key),
    INDEX idx_status (status)
);

-- =====================================================
-- جدول المستخدمين
-- =====================================================
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    bio TEXT,
    avatar_url VARCHAR(500),
    cover_url VARCHAR(500),
    birth_date DATE,
    gender ENUM('male', 'female', 'other'),
    country VARCHAR(100),
    city VARCHAR(100),
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50),
    is_verified TINYINT(1) DEFAULT 0,
    is_private TINYINT(1) DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    is_banned TINYINT(1) DEFAULT 0,
    followers_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    videos_count INT DEFAULT 0,
    likes_received_count INT DEFAULT 0,
    last_login_at TIMESTAMP NULL,
    email_verified_at TIMESTAMP NULL,
    phone_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_is_verified (is_verified),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- جدول الفيديوهات
-- =====================================================
CREATE TABLE videos (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    video_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    duration INT NOT NULL COMMENT 'المدة بالثواني',
    file_size BIGINT COMMENT 'حجم الملف بالبايت',
    resolution VARCHAR(20) COMMENT 'مثل 1080x1920',
    format VARCHAR(10) DEFAULT 'mp4',
    is_public TINYINT(1) DEFAULT 1,
    is_comments_enabled TINYINT(1) DEFAULT 1,
    is_duet_enabled TINYINT(1) DEFAULT 1,
    is_download_enabled TINYINT(1) DEFAULT 1,
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    downloads_count INT DEFAULT 0,
    reports_count INT DEFAULT 0,
    music_id BIGINT,
    filter_id INT,
    location VARCHAR(255),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    hashtags JSON,
    mentions JSON,
    ai_analysis JSON COMMENT 'تحليل الذكاء الاصطناعي',
    moderation_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    is_featured TINYINT(1) DEFAULT 0,
    featured_at TIMESTAMP NULL,
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_public (is_public),
    INDEX idx_published_at (published_at),
    INDEX idx_views_count (views_count),
    INDEX idx_likes_count (likes_count),
    INDEX idx_moderation_status (moderation_status),
    INDEX idx_location (latitude, longitude)
);

-- =====================================================
-- جدول المتابعة
-- =====================================================
CREATE TABLE follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_id BIGINT NOT NULL,
    following_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_follow (follower_id, following_id),
    INDEX idx_follower_id (follower_id),
    INDEX idx_following_id (following_id)
);

-- =====================================================
-- جدول الإعجابات
-- =====================================================
CREATE TABLE likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    video_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_like (user_id, video_id),
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id)
);

-- =====================================================
-- جدول التعليقات
-- =====================================================
CREATE TABLE comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    video_id BIGINT NOT NULL,
    parent_id BIGINT NULL COMMENT 'للردود على التعليقات',
    content TEXT NOT NULL,
    video_url VARCHAR(500) COMMENT 'للتعليقات بالفيديو',
    likes_count INT DEFAULT 0,
    replies_count INT DEFAULT 0,
    is_pinned TINYINT(1) DEFAULT 0,
    is_hidden TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- جدول الرسائل
-- =====================================================
CREATE TABLE messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sender_id BIGINT NOT NULL,
    receiver_id BIGINT NOT NULL,
    content TEXT,
    message_type ENUM('text', 'image', 'video', 'audio', 'file') DEFAULT 'text',
    file_url VARCHAR(500),
    is_read TINYINT(1) DEFAULT 0,
    is_deleted_by_sender TINYINT(1) DEFAULT 0,
    is_deleted_by_receiver TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read)
);

-- =====================================================
-- جدول البث المباشر
-- =====================================================
CREATE TABLE live_streams (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    stream_key VARCHAR(255) UNIQUE NOT NULL,
    stream_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    viewers_count INT DEFAULT 0,
    max_viewers_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    gifts_count INT DEFAULT 0,
    duration INT DEFAULT 0 COMMENT 'المدة بالثواني',
    status ENUM('scheduled', 'live', 'ended', 'cancelled') DEFAULT 'scheduled',
    is_private TINYINT(1) DEFAULT 0,
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
);

-- =====================================================
-- جدول الموسيقى والأصوات
-- =====================================================
CREATE TABLE music (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    artist VARCHAR(255),
    album VARCHAR(255),
    duration INT NOT NULL COMMENT 'المدة بالثواني',
    file_url VARCHAR(500) NOT NULL,
    cover_url VARCHAR(500),
    genre VARCHAR(100),
    is_original TINYINT(1) DEFAULT 0,
    usage_count INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_title (title),
    INDEX idx_artist (artist),
    INDEX idx_genre (genre),
    INDEX idx_usage_count (usage_count)
);

-- =====================================================
-- جدول الهاشتاجز
-- =====================================================
CREATE TABLE hashtags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    usage_count INT DEFAULT 0,
    trending_score DECIMAL(10, 2) DEFAULT 0,
    is_trending TINYINT(1) DEFAULT 0,
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_name (name),
    INDEX idx_usage_count (usage_count),
    INDEX idx_trending_score (trending_score),
    INDEX idx_is_trending (is_trending)
);

-- =====================================================
-- جدول التحديات
-- =====================================================
CREATE TABLE challenges (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    hashtag VARCHAR(100) NOT NULL,
    banner_url VARCHAR(500),
    music_id BIGINT,
    creator_id BIGINT,
    participants_count INT DEFAULT 0,
    videos_count INT DEFAULT 0,
    views_count INT DEFAULT 0,
    prize_amount DECIMAL(10, 2) DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'USD',
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    status ENUM('draft', 'active', 'ended', 'cancelled') DEFAULT 'draft',
    is_featured TINYINT(1) DEFAULT 0,
    rules JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (music_id) REFERENCES music(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_hashtag (hashtag),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date),
    INDEX idx_participants_count (participants_count)
);

-- =====================================================
-- جدول الهدايا الافتراضية
-- =====================================================
CREATE TABLE gifts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    icon_url VARCHAR(500) NOT NULL,
    animation_url VARCHAR(500),
    price DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'coins',
    category VARCHAR(50),
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_price (price),
    INDEX idx_category (category),
    INDEX idx_rarity (rarity)
);

-- =====================================================
-- جدول العملات الافتراضية
-- =====================================================
CREATE TABLE user_coins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    coins_balance INT DEFAULT 0,
    diamonds_balance INT DEFAULT 0,
    total_earned INT DEFAULT 0,
    total_spent INT DEFAULT 0,
    last_transaction_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_coins (user_id),
    INDEX idx_coins_balance (coins_balance),
    INDEX idx_diamonds_balance (diamonds_balance)
);

-- =====================================================
-- جدول معاملات العملات
-- =====================================================
CREATE TABLE coin_transactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    transaction_type ENUM('earn', 'spend', 'gift', 'purchase', 'refund') NOT NULL,
    amount INT NOT NULL,
    currency_type ENUM('coins', 'diamonds') DEFAULT 'coins',
    description VARCHAR(255),
    reference_id BIGINT COMMENT 'مرجع للفيديو أو الهدية أو المعاملة',
    reference_type VARCHAR(50) COMMENT 'نوع المرجع: video, gift, purchase, etc',
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at),
    INDEX idx_reference (reference_id, reference_type)
);

-- =====================================================
-- جدول الإبلاغات
-- =====================================================
CREATE TABLE reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reporter_id BIGINT NOT NULL,
    reported_user_id BIGINT,
    reported_video_id BIGINT,
    reported_comment_id BIGINT,
    report_type ENUM('spam', 'harassment', 'inappropriate', 'copyright', 'fake', 'other') NOT NULL,
    reason TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    admin_notes TEXT,
    reviewed_by BIGINT,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_video_id) REFERENCES videos(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_reporter_id (reporter_id),
    INDEX idx_status (status),
    INDEX idx_report_type (report_type),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- جدول الإشعارات
-- =====================================================
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type ENUM('like', 'comment', 'follow', 'mention', 'live', 'challenge', 'gift', 'system') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    data JSON COMMENT 'بيانات إضافية للإشعار',
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);
