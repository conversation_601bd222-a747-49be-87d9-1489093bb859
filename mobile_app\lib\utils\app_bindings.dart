// =====================================================
// ربط التبعيات
// =====================================================

import 'package:get/get.dart';
import '../services/storage_service.dart';
import '../services/api_service.dart';
import '../services/auth_service.dart';
import '../services/feature_service.dart';
import '../controllers/app_controller.dart';

class AppBindings extends Bindings {
  @override
  void dependencies() {
    // الخدمات الأساسية
    Get.lazyPut<StorageService>(() => StorageService());
    Get.lazyPut<ApiService>(() => ApiService());
    Get.lazyPut<AuthService>(() => AuthService());
    Get.lazyPut<FeatureService>(() => FeatureService());
    
    // التحكمات
    Get.lazyPut<AppController>(() => AppController());
  }
}
