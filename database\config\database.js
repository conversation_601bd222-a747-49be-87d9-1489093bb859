// =====================================================
// إعدادات قاعدة البيانات
// =====================================================

const mysql = require('mysql2/promise');
require('dotenv').config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
  development: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'tiktok_clone_db',
    charset: 'utf8mb4',
    timezone: '+00:00',
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    connectionLimit: 10,
    queueLimit: 0,
    ssl: false
  },
  
  production: {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4',
    timezone: '+00:00',
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    connectionLimit: 20,
    queueLimit: 0,
    ssl: {
      rejectUnauthorized: false
    }
  },
  
  test: {
    host: process.env.TEST_DB_HOST || 'localhost',
    port: process.env.TEST_DB_PORT || 3306,
    user: process.env.TEST_DB_USER || 'root',
    password: process.env.TEST_DB_PASSWORD || '',
    database: process.env.TEST_DB_NAME || 'tiktok_clone_test_db',
    charset: 'utf8mb4',
    timezone: '+00:00',
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    connectionLimit: 5,
    queueLimit: 0,
    ssl: false
  }
};

// الحصول على إعدادات البيئة الحالية
const environment = process.env.NODE_ENV || 'development';
const currentConfig = dbConfig[environment];

// إنشاء pool للاتصالات
let pool;

const createPool = () => {
  try {
    pool = mysql.createPool(currentConfig);
    console.log(`✅ تم إنشاء pool قاعدة البيانات للبيئة: ${environment}`);
    return pool;
  } catch (error) {
    console.error('❌ خطأ في إنشاء pool قاعدة البيانات:', error);
    throw error;
  }
};

// اختبار الاتصال بقاعدة البيانات
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    return true;
  } catch (error) {
    console.error('❌ فشل الاتصال بقاعدة البيانات:', error);
    return false;
  }
};

// تنفيذ استعلام
const query = async (sql, params = []) => {
  try {
    const [results] = await pool.execute(sql, params);
    return results;
  } catch (error) {
    console.error('❌ خطأ في تنفيذ الاستعلام:', error);
    throw error;
  }
};

// تنفيذ معاملة (transaction)
const transaction = async (queries) => {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const { sql, params } of queries) {
      const [result] = await connection.execute(sql, params);
      results.push(result);
    }
    
    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

// إغلاق pool
const closePool = async () => {
  try {
    if (pool) {
      await pool.end();
      console.log('✅ تم إغلاق pool قاعدة البيانات');
    }
  } catch (error) {
    console.error('❌ خطأ في إغلاق pool قاعدة البيانات:', error);
  }
};

// تصدير الوحدات
module.exports = {
  dbConfig,
  currentConfig,
  createPool,
  testConnection,
  query,
  transaction,
  closePool,
  getPool: () => pool
};

// إنشاء pool عند تحميل الوحدة
if (!pool) {
  createPool();
}
