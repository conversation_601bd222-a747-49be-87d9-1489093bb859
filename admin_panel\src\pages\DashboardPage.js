// =====================================================
// Dashboard Page - لوحة المعلومات الرئيسية
// =====================================================

import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Button
} from '@mui/material';
import {
  People as UsersIcon,
  VideoLibrary as VideosIcon,
  TrendingUp as TrendingIcon,
  AttachMoney as RevenueIcon,
  Visibility as ViewsIcon,
  ThumbUp as LikesIcon,
  Comment as CommentsIcon,
  Share as SharesIcon,
  MoreVert as MoreIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

const DashboardPage = () => {
  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const stats = [
    {
      title: 'إجمالي المستخدمين',
      value: '2.5M',
      change: '+12.5%',
      changeType: 'positive',
      icon: UsersIcon,
      color: '#FF0050'
    },
    {
      title: 'إجمالي الفيديوهات',
      value: '850K',
      change: '+8.2%',
      changeType: 'positive',
      icon: VideosIcon,
      color: '#25F4EE'
    },
    {
      title: 'المشاهدات اليومية',
      value: '45.2M',
      change: '+15.7%',
      changeType: 'positive',
      icon: ViewsIcon,
      color: '#00D9FF'
    },
    {
      title: 'الإيرادات الشهرية',
      value: '$125K',
      change: '+22.1%',
      changeType: 'positive',
      icon: RevenueIcon,
      color: '#FFB800'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      user: 'أحمد محمد',
      action: 'نشر فيديو جديد',
      time: 'منذ 5 دقائق',
      avatar: null
    },
    {
      id: 2,
      user: 'فاطمة علي',
      action: 'حصل على 10K إعجاب',
      time: 'منذ 15 دقيقة',
      avatar: null
    },
    {
      id: 3,
      user: 'محمد سالم',
      action: 'بدأ بث مباشر',
      time: 'منذ 30 دقيقة',
      avatar: null
    },
    {
      id: 4,
      user: 'نور الدين',
      action: 'انضم للتطبيق',
      time: 'منذ ساعة',
      avatar: null
    }
  ];

  const topVideos = [
    {
      id: 1,
      title: 'رقصة ترند جديدة',
      creator: 'سارة أحمد',
      views: '2.5M',
      likes: '450K',
      thumbnail: null
    },
    {
      id: 2,
      title: 'طبخة سريعة ولذيذة',
      creator: 'الشيف محمد',
      views: '1.8M',
      likes: '320K',
      thumbnail: null
    },
    {
      id: 3,
      title: 'تحدي الرياضة',
      creator: 'فيتنس مع علي',
      views: '1.2M',
      likes: '280K',
      thumbnail: null
    }
  ];

  const features = [
    { name: 'التوأم الإبداعي', status: 'active', usage: 85 },
    { name: 'الرادار الاجتماعي', status: 'active', usage: 72 },
    { name: 'توليد فيديو بالصوت', status: 'beta', usage: 45 },
    { name: 'وضع الفن الفوري', status: 'active', usage: 68 }
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            لوحة المعلومات
          </Typography>
          <Typography variant="body2" color="text.secondary">
            نظرة عامة على أداء التطبيق والإحصائيات الرئيسية
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => window.location.reload()}
        >
          تحديث البيانات
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                    <Avatar sx={{ bgcolor: stat.color, width: 48, height: 48 }}>
                      <Icon />
                    </Avatar>
                    <IconButton size="small">
                      <MoreIcon />
                    </IconButton>
                  </Box>
                  
                  <Typography variant="h4" fontWeight="bold" gutterBottom>
                    {stat.value}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {stat.title}
                  </Typography>
                  
                  <Chip
                    label={stat.change}
                    size="small"
                    color={stat.changeType === 'positive' ? 'success' : 'error'}
                    variant="outlined"
                  />
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Activities */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                النشاطات الأخيرة
              </Typography>
              
              <Box>
                {recentActivities.map((activity) => (
                  <Box
                    key={activity.id}
                    display="flex"
                    alignItems="center"
                    gap={2}
                    py={1.5}
                    borderBottom="1px solid"
                    borderColor="divider"
                    sx={{ '&:last-child': { borderBottom: 'none' } }}
                  >
                    <Avatar sx={{ width: 40, height: 40 }}>
                      {activity.user.charAt(0)}
                    </Avatar>
                    
                    <Box flex={1}>
                      <Typography variant="body2" fontWeight="medium">
                        {activity.user}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {activity.action}
                      </Typography>
                    </Box>
                    
                    <Typography variant="caption" color="text.secondary">
                      {activity.time}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Videos */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                أفضل الفيديوهات
              </Typography>
              
              <Box>
                {topVideos.map((video, index) => (
                  <Box
                    key={video.id}
                    display="flex"
                    alignItems="center"
                    gap={2}
                    py={1.5}
                    borderBottom="1px solid"
                    borderColor="divider"
                    sx={{ '&:last-child': { borderBottom: 'none' } }}
                  >
                    <Box
                      sx={{
                        width: 60,
                        height: 40,
                        bgcolor: 'grey.200',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <VideosIcon color="action" />
                    </Box>
                    
                    <Box flex={1}>
                      <Typography variant="body2" fontWeight="medium" noWrap>
                        {video.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {video.creator}
                      </Typography>
                    </Box>
                    
                    <Box textAlign="right">
                      <Typography variant="caption" display="block">
                        {video.views} مشاهدة
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {video.likes} إعجاب
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Features Usage */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                استخدام الميزات الثورية
              </Typography>
              
              <Grid container spacing={3}>
                {features.map((feature, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Box>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" fontWeight="medium">
                          {feature.name}
                        </Typography>
                        <Chip
                          label={feature.status === 'active' ? 'مفعل' : 'تجريبي'}
                          size="small"
                          color={feature.status === 'active' ? 'success' : 'warning'}
                          variant="outlined"
                        />
                      </Box>
                      
                      <LinearProgress
                        variant="determinate"
                        value={feature.usage}
                        sx={{ height: 8, borderRadius: 4, mb: 1 }}
                      />
                      
                      <Typography variant="caption" color="text.secondary">
                        {feature.usage}% معدل الاستخدام
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
