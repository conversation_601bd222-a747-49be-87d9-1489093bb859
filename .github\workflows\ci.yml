name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install Backend Dependencies
      run: |
        cd backend
        npm ci
    
    - name: Run Backend Tests
      run: |
        cd backend
        npm test
      env:
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/tiktok_clone_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test_secret
        JWT_REFRESH_SECRET: test_refresh_secret

  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        app: [web_app, admin_panel]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: ${{ matrix.app }}/package-lock.json
    
    - name: Install Dependencies
      run: |
        cd ${{ matrix.app }}
        npm ci
    
    - name: Run Tests
      run: |
        cd ${{ matrix.app }}
        npm test -- --coverage --watchAll=false
    
    - name: Build
      run: |
        cd ${{ matrix.app }}
        npm run build

  # Flutter Tests
  flutter-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Install Dependencies
      run: |
        cd mobile_app
        flutter pub get
    
    - name: Run Tests
      run: |
        cd mobile_app
        flutter test
    
    - name: Analyze Code
      run: |
        cd mobile_app
        flutter analyze
    
    - name: Build APK
      run: |
        cd mobile_app
        flutter build apk --debug

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    needs: [backend-test, frontend-test, flutter-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here

  # Deploy to Production
  deploy-production:
    needs: [backend-test, frontend-test, flutter-test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
