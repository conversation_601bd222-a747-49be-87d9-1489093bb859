-- =====================================================
-- Migration: Create Conversations Table
-- =====================================================

CREATE TABLE IF NOT EXISTS conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- Conversation Type
    type ENUM('direct', 'group') DEFAULT 'direct',
    
    -- Group Details (for group chats)
    name VARCHAR(255),
    description TEXT,
    avatar_url VARCHAR(500),
    
    -- Settings
    is_muted BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    
    -- Group Settings
    max_participants INT DEFAULT 100,
    join_approval_required BOOLEAN DEFAULT FALSE,
    
    -- Privacy
    is_private BOOLEAN DEFAULT TRUE,
    invite_link VARCHAR(255) UNIQUE,
    
    -- Stats
    participants_count INT DEFAULT 0,
    messages_count INT DEFAULT 0,
    
    -- Last Activity
    last_message_id BIGINT,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (last_message_id) REFERENCES messages(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_type (type),
    INDEX idx_last_activity_at (last_activity_at),
    INDEX idx_participants_count (participants_count),
    INDEX idx_invite_link (invite_link),
    INDEX idx_is_archived (is_archived)
);
