/* =====================================================
   Admin Panel Styles - أنماط لوحة التحكم الإدارية
   ===================================================== */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  font-family: 'Cairo', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  background-color: #F8F9FA;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Admin App Container */
.admin-app {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Custom Scrollbar for Admin */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #FF0050;
  border-radius: 6px;
  border: 2px solid #F8F9FA;
}

::-webkit-scrollbar-thumb:hover {
  background: #FE2C55;
}

::-webkit-scrollbar-corner {
  background: #F8F9FA;
}

/* Admin Animations */
@keyframes adminFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes adminSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes adminPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Admin Utility Classes */
.admin-fade-in {
  animation: adminFadeIn 0.6s ease-out;
}

.admin-slide-in {
  animation: adminSlideIn 0.6s ease-out;
}

.admin-pulse {
  animation: adminPulse 2s infinite;
}

/* Admin Card Hover Effects */
.admin-card-hover {
  transition: all 0.3s ease;
  cursor: pointer;
}

.admin-card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Admin Stats Cards */
.admin-stat-card {
  background: linear-gradient(135deg, #FF0050 0%, #FE2C55 100%);
  color: white;
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.admin-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

/* Admin Table Styles */
.admin-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.admin-table-header {
  background: #F8F9FA;
  font-weight: 600;
  color: #161823;
}

.admin-table-row:hover {
  background: rgba(255, 0, 80, 0.02);
}

/* Admin Form Styles */
.admin-form {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.admin-form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #E1E2E3;
}

.admin-form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

/* Admin Button Styles */
.admin-btn-primary {
  background: linear-gradient(135deg, #FF0050, #FE2C55);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.admin-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 0, 80, 0.3);
}

/* Admin Sidebar Styles */
.admin-sidebar {
  background: white;
  border-right: 1px solid #E1E2E3;
  height: 100vh;
  position: fixed;
  width: 280px;
  z-index: 1000;
}

.admin-sidebar-item {
  padding: 12px 16px;
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.admin-sidebar-item:hover {
  background: rgba(255, 0, 80, 0.05);
}

.admin-sidebar-item.active {
  background: #FF0050;
  color: white;
}

/* Admin Header Styles */
.admin-header {
  background: white;
  border-bottom: 1px solid #E1E2E3;
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  position: fixed;
  top: 0;
  right: 0;
  left: 280px;
  z-index: 999;
}

/* Admin Content Area */
.admin-content {
  margin-top: 64px;
  margin-right: 280px;
  padding: 24px;
  min-height: calc(100vh - 64px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .admin-sidebar.open {
    transform: translateX(0);
  }
  
  .admin-header {
    left: 0;
  }
  
  .admin-content {
    margin-right: 0;
    padding: 16px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #0A0A0A;
    color: #FFFFFF;
  }
  
  .admin-sidebar {
    background: #161823;
    border-color: #2F2F2F;
  }
  
  .admin-header {
    background: #161823;
    border-color: #2F2F2F;
  }
  
  .admin-form {
    background: #161823;
  }
  
  .admin-table {
    background: #161823;
  }
  
  .admin-table-header {
    background: #2F2F2F;
    color: #FFFFFF;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
  
  ::-webkit-scrollbar-thumb {
    border-color: #161823;
  }
  
  ::-webkit-scrollbar-corner {
    background: #161823;
  }
}

/* Print Styles */
@media print {
  .admin-sidebar,
  .admin-header,
  .no-print {
    display: none !important;
  }
  
  .admin-content {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  * {
    color: black !important;
    background: white !important;
    box-shadow: none !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  .admin-btn-primary {
    background: #000000 !important;
    color: #FFFFFF !important;
  }
}
