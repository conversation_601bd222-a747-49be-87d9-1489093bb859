// =====================================================
// Redux Store Configuration
// =====================================================

import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Slices
import authSlice from './slices/authSlice';
import userSlice from './slices/userSlice';
import videoSlice from './slices/videoSlice';
import uiSlice from './slices/uiSlice';
import featureSlice from './slices/featureSlice';

// Persist configuration
const persistConfig = {
  key: 'tiktok-clone-web',
  storage,
  whitelist: ['auth', 'ui'], // Only persist auth and ui state
  blacklist: ['video'], // Don't persist video state (too large)
};

// Root reducer
const rootReducer = combineReducers({
  auth: authSlice,
  user: userSlice,
  video: videoSlice,
  ui: uiSlice,
  feature: featureSlice,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Persistor
export const persistor = persistStore(store);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
