-- =====================================================
-- Seed: Music Data
-- =====================================================

INSERT INTO music (
    title, artist, album, genre, audio_url, duration, 
    usage_count, is_trending, is_featured, license_type, created_at
) VALUES 
-- Arabic Music
('أغنية شعبية', 'فنان عربي', 'ألبوم شعبي', 'شعبي', '/music/arabic_folk.mp3', 180, 15000, TRUE, TRUE, 'free', NOW()),
('موسيقى هادئة', 'موسيقار', 'موسيقى هادئة', 'كلاسيكي', '/music/calm_arabic.mp3', 240, 8900, FALSE, TRUE, 'free', NOW()),
('إيقاع راقص', 'DJ عربي', 'ريمكس عربي', 'إلكتروني', '/music/dance_beat.mp3', 200, 23000, TRUE, FALSE, 'free', NOW()),
('تراث أصيل', 'فرقة تراثية', 'تراث', 'تراثي', '/music/heritage.mp3', 300, 5600, FALSE, TRUE, 'free', NOW()),
('موسيقى رومانسية', 'مطرب رومانسي', 'حب وغرام', 'رومانسي', '/music/romantic.mp3', 220, 12000, FALSE, FALSE, 'free', NOW()),

-- International Music
('Upbeat Pop', 'Pop Artist', 'Pop Hits', 'Pop', '/music/upbeat_pop.mp3', 195, 34000, TRUE, TRUE, 'free', NOW()),
('Chill Vibes', 'Chill Master', 'Relaxation', 'Chill', '/music/chill_vibes.mp3', 210, 18000, FALSE, TRUE, 'free', NOW()),
('Electronic Dance', 'EDM Producer', 'Dance Floor', 'EDM', '/music/electronic_dance.mp3', 185, 45000, TRUE, FALSE, 'free', NOW()),
('Hip Hop Beat', 'Hip Hop Artist', 'Street Beats', 'Hip Hop', '/music/hiphop_beat.mp3', 175, 28000, TRUE, FALSE, 'free', NOW()),
('Jazz Smooth', 'Jazz Musician', 'Smooth Jazz', 'Jazz', '/music/jazz_smooth.mp3', 280, 7800, FALSE, TRUE, 'free', NOW()),

-- Trending Sounds
('فيرال ساوند 1', 'مجهول', 'ترند', 'ترند', '/music/viral_sound_1.mp3', 15, 89000, TRUE, TRUE, 'free', NOW()),
('فيرال ساوند 2', 'مجهول', 'ترند', 'ترند', '/music/viral_sound_2.mp3', 20, 67000, TRUE, TRUE, 'free', NOW()),
('تحدي الرقص', 'مجهول', 'تحدي', 'ترند', '/music/dance_challenge.mp3', 30, 156000, TRUE, TRUE, 'free', NOW()),
('كوميدي ساوند', 'مجهول', 'كوميدي', 'كوميدي', '/music/comedy_sound.mp3', 10, 234000, TRUE, FALSE, 'free', NOW()),

-- Instrumental
('بيانو هادئ', 'عازف بيانو', 'موسيقى آلية', 'آلي', '/music/calm_piano.mp3', 180, 12000, FALSE, TRUE, 'free', NOW()),
('جيتار أكوستيك', 'عازف جيتار', 'أكوستيك', 'آلي', '/music/acoustic_guitar.mp3', 200, 9800, FALSE, FALSE, 'free', NOW()),
('أوركسترا', 'فرقة موسيقية', 'كلاسيكي', 'كلاسيكي', '/music/orchestra.mp3', 350, 5600, FALSE, TRUE, 'premium', NOW()),

-- Nature Sounds
('صوت المطر', 'طبيعة', 'أصوات طبيعية', 'طبيعي', '/music/rain_sound.mp3', 300, 15000, FALSE, FALSE, 'free', NOW()),
('صوت البحر', 'طبيعة', 'أصوات طبيعية', 'طبيعي', '/music/ocean_sound.mp3', 240, 11000, FALSE, FALSE, 'free', NOW()),
('صوت الطيور', 'طبيعة', 'أصوات طبيعية', 'طبيعي', '/music/birds_sound.mp3', 180, 8900, FALSE, FALSE, 'free', NOW()),

-- Gaming Sounds
('موسيقى ألعاب', 'مطور ألعاب', 'ألعاب', 'ألعاب', '/music/gaming_music.mp3', 120, 45000, TRUE, FALSE, 'free', NOW()),
('صوت انتصار', 'مطور ألعاب', 'ألعاب', 'ألعاب', '/music/victory_sound.mp3', 5, 78000, TRUE, FALSE, 'free', NOW());
