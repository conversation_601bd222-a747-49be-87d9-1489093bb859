// =====================================================
// Redux Store Configuration - Admin Panel
// =====================================================

import { configureStore } from '@reduxjs/toolkit';

// Mock reducer for now
const initialState = {
  admin: {
    isAuthenticated: false,
    user: null,
    theme: 'light'
  }
};

const adminReducer = (state = initialState, action) => {
  switch (action.type) {
    default:
      return state;
  }
};

export const store = configureStore({
  reducer: {
    admin: adminReducer
  },
  devTools: process.env.NODE_ENV !== 'production',
});
