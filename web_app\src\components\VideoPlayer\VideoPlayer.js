// =====================================================
// Video Player Component - مكون مشغل الفيديو
// =====================================================

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  IconButton,
  Slider,
  Typography,
  Tooltip,
  CircularProgress,
  Fade,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  VolumeUp,
  VolumeOff,
  Fullscreen,
  FullscreenExit,
  Settings,
  PictureInPicture,
  Replay,
  Forward10,
  Replay10
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { updateVideoProgress, toggleVideoLike } from '../../store/slices/videoSlice';

// Styled Components
const VideoContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '100%',
  backgroundColor: '#000',
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  cursor: 'pointer',
  '&:hover .video-controls': {
    opacity: 1,
  },
}));

const VideoElement = styled('video')({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
});

const ControlsOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
  padding: theme.spacing(2),
  opacity: 0,
  transition: 'opacity 0.3s ease',
  '&.visible': {
    opacity: 1,
  },
}));

const CenterControls = styled(Box)({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  display: 'flex',
  alignItems: 'center',
  gap: '16px',
});

const LoadingOverlay = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(0,0,0,0.5)',
});

const VideoPlayer = ({
  video,
  autoPlay = false,
  muted = false,
  loop = true,
  controls = true,
  onPlay,
  onPause,
  onEnded,
  onTimeUpdate,
  onLoadStart,
  onLoadedData,
  onError,
  className,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const dispatch = useDispatch();
  const { currentUser } = useSelector(state => state.auth);
  
  // Refs
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const controlsTimeoutRef = useRef(null);

  // State
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(muted);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isBuffering, setIsBuffering] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [quality, setQuality] = useState('auto');

  // Effects
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadStart = () => {
      setIsLoading(true);
      onLoadStart?.();
    };

    const handleLoadedData = () => {
      setIsLoading(false);
      setDuration(video.duration);
      onLoadedData?.();
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
      onTimeUpdate?.(video.currentTime);
      
      // تحديث تقدم المشاهدة
      if (currentUser && video.id) {
        dispatch(updateVideoProgress({
          videoId: video.id,
          progress: video.currentTime,
          duration: video.duration
        }));
      }
    };

    const handlePlay = () => {
      setIsPlaying(true);
      onPlay?.();
    };

    const handlePause = () => {
      setIsPlaying(false);
      onPause?.();
    };

    const handleEnded = () => {
      setIsPlaying(false);
      onEnded?.();
    };

    const handleWaiting = () => {
      setIsBuffering(true);
    };

    const handleCanPlay = () => {
      setIsBuffering(false);
    };

    const handleError = (e) => {
      setIsLoading(false);
      onError?.(e);
    };

    // Event Listeners
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('waiting', handleWaiting);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);

    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('waiting', handleWaiting);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
    };
  }, [video.id, currentUser, dispatch, onPlay, onPause, onEnded, onTimeUpdate, onLoadStart, onLoadedData, onError]);

  // Auto-hide controls
  useEffect(() => {
    if (showControls && isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls, isPlaying]);

  // Handlers
  const handlePlayPause = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  }, [isPlaying]);

  const handleSeek = useCallback((event, newValue) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = newValue;
    setCurrentTime(newValue);
  }, []);

  const handleVolumeChange = useCallback((event, newValue) => {
    const video = videoRef.current;
    if (!video) return;

    video.volume = newValue;
    setVolume(newValue);
    setIsMuted(newValue === 0);
  }, []);

  const handleMuteToggle = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isMuted) {
      video.volume = volume || 0.5;
      setIsMuted(false);
    } else {
      video.volume = 0;
      setIsMuted(true);
    }
  }, [isMuted, volume]);

  const handleFullscreenToggle = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    if (!isFullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen();
      } else if (container.webkitRequestFullscreen) {
        container.webkitRequestFullscreen();
      } else if (container.mozRequestFullScreen) {
        container.mozRequestFullScreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      }
    }
  }, [isFullscreen]);

  const handlePictureInPicture = useCallback(async () => {
    const video = videoRef.current;
    if (!video) return;

    try {
      if (document.pictureInPictureElement) {
        await document.exitPictureInPicture();
      } else {
        await video.requestPictureInPicture();
      }
    } catch (error) {
      console.error('Picture-in-Picture error:', error);
    }
  }, []);

  const handleSkip = useCallback((seconds) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = Math.max(0, Math.min(duration, video.currentTime + seconds));
  }, [duration]);

  const handleMouseMove = useCallback(() => {
    setShowControls(true);
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
  }, []);

  const handleDoubleClick = useCallback((event) => {
    const rect = containerRef.current.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const centerX = rect.width / 2;

    if (Math.abs(clickX - centerX) < 100) {
      handlePlayPause();
    } else if (clickX < centerX) {
      handleSkip(-10);
    } else {
      handleSkip(10);
    }
  }, [handlePlayPause, handleSkip]);

  // Format time
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <VideoContainer
      ref={containerRef}
      className={`video-player ${className || ''}`}
      onMouseMove={handleMouseMove}
      onDoubleClick={handleDoubleClick}
      {...props}
    >
      <VideoElement
        ref={videoRef}
        src={video.url}
        poster={video.thumbnailUrl}
        autoPlay={autoPlay}
        muted={isMuted}
        loop={loop}
        playsInline
      />

      {/* Loading Overlay */}
      {(isLoading || isBuffering) && (
        <LoadingOverlay>
          <CircularProgress size={60} sx={{ color: 'white' }} />
        </LoadingOverlay>
      )}

      {/* Center Controls */}
      <CenterControls>
        <Fade in={!isPlaying && !isLoading}>
          <IconButton
            onClick={handlePlayPause}
            sx={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.3)',
              },
            }}
          >
            <PlayArrow sx={{ fontSize: 48 }} />
          </IconButton>
        </Fade>
      </CenterControls>

      {/* Controls Overlay */}
      {controls && (
        <ControlsOverlay
          className={`video-controls ${showControls ? 'visible' : ''}`}
        >
          {/* Progress Bar */}
          <Box sx={{ mb: 2 }}>
            <Slider
              value={currentTime}
              max={duration}
              onChange={handleSeek}
              sx={{
                color: theme.palette.primary.main,
                '& .MuiSlider-thumb': {
                  width: 12,
                  height: 12,
                },
                '& .MuiSlider-track': {
                  height: 4,
                },
                '& .MuiSlider-rail': {
                  height: 4,
                  backgroundColor: 'rgba(255,255,255,0.3)',
                },
              }}
            />
          </Box>

          {/* Control Buttons */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Play/Pause */}
            <IconButton onClick={handlePlayPause} sx={{ color: 'white' }}>
              {isPlaying ? <Pause /> : <PlayArrow />}
            </IconButton>

            {/* Skip Buttons */}
            <IconButton onClick={() => handleSkip(-10)} sx={{ color: 'white' }}>
              <Replay10 />
            </IconButton>
            <IconButton onClick={() => handleSkip(10)} sx={{ color: 'white' }}>
              <Forward10 />
            </IconButton>

            {/* Volume */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 100 }}>
              <IconButton onClick={handleMuteToggle} sx={{ color: 'white' }}>
                {isMuted ? <VolumeOff /> : <VolumeUp />}
              </IconButton>
              {!isMobile && (
                <Slider
                  value={isMuted ? 0 : volume}
                  max={1}
                  step={0.1}
                  onChange={handleVolumeChange}
                  sx={{
                    width: 60,
                    color: 'white',
                    '& .MuiSlider-thumb': {
                      width: 8,
                      height: 8,
                    },
                  }}
                />
              )}
            </Box>

            {/* Time Display */}
            <Typography variant="body2" sx={{ color: 'white', minWidth: 80 }}>
              {formatTime(currentTime)} / {formatTime(duration)}
            </Typography>

            <Box sx={{ flexGrow: 1 }} />

            {/* Settings */}
            <Tooltip title="الإعدادات">
              <IconButton sx={{ color: 'white' }}>
                <Settings />
              </IconButton>
            </Tooltip>

            {/* Picture in Picture */}
            <Tooltip title="صورة في صورة">
              <IconButton onClick={handlePictureInPicture} sx={{ color: 'white' }}>
                <PictureInPicture />
              </IconButton>
            </Tooltip>

            {/* Fullscreen */}
            <Tooltip title={isFullscreen ? 'خروج من ملء الشاشة' : 'ملء الشاشة'}>
              <IconButton onClick={handleFullscreenToggle} sx={{ color: 'white' }}>
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
          </Box>
        </ControlsOverlay>
      )}
    </VideoContainer>
  );
};

export default VideoPlayer;
