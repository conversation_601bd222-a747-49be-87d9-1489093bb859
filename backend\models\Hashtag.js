// =====================================================
// Hashtag Model - نموذج الهاشتاغ
// =====================================================

const mongoose = require('mongoose');

const hashtagSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    maxlength: 100,
    match: /^[a-zA-Z0-9_\u0600-\u06FF]+$/,
    index: true
  },
  displayName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  
  // Stats
  usageCount: {
    type: Number,
    default: 0,
    min: 0,
    index: true
  },
  videosCount: {
    type: Number,
    default: 0,
    min: 0
  },
  trendingScore: {
    type: Number,
    default: 0,
    min: 0,
    index: true
  },
  
  // Metadata
  description: {
    type: String,
    maxlength: 500
  },
  category: {
    type: String,
    maxlength: 50,
    enum: [
      'ترند', 'ترفيه', 'رقص', 'موسيقى', 'جمال', 'موضة', 
      'طعام', 'رياضة', 'تقنية', 'سفر', 'طبيعة', 'تعليم', 
      'مناسبات', 'مواسم', 'تطوير', 'كوميدي', 'فن', 'أخرى'
    ],
    index: true
  },
  language: {
    type: String,
    default: 'ar',
    enum: ['ar', 'en', 'fr', 'es', 'de'],
    index: true
  },
  
  // Status
  isTrending: {
    type: Boolean,
    default: false,
    index: true
  },
  isFeatured: {
    type: Boolean,
    default: false,
    index: true
  },
  isBanned: {
    type: Boolean,
    default: false,
    index: true
  },
  isSensitive: {
    type: Boolean,
    default: false
  },
  
  // Moderation
  moderationStatus: {
    type: String,
    enum: ['approved', 'pending', 'rejected'],
    default: 'approved',
    index: true
  },
  moderatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  moderatedAt: {
    type: Date
  },
  moderationNotes: {
    type: String,
    maxlength: 1000
  },
  
  // AI Analysis
  aiCategory: {
    type: String,
    maxlength: 100
  },
  aiSentiment: {
    type: String,
    enum: ['positive', 'negative', 'neutral']
  },
  aiToxicityScore: {
    type: Number,
    min: 0,
    max: 1
  },
  aiTopics: [{
    topic: String,
    confidence: {
      type: Number,
      min: 0,
      max: 1
    }
  }],
  
  // Related Hashtags
  relatedHashtags: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Hashtag'
  }],
  
  // Usage History (for trending calculation)
  dailyUsage: [{
    date: {
      type: Date,
      default: Date.now
    },
    count: {
      type: Number,
      default: 0
    }
  }]
}, {
  timestamps: true
});

// Compound Indexes
hashtagSchema.index({ name: 'text', displayName: 'text', description: 'text' });
hashtagSchema.index({ usageCount: -1, trendingScore: -1 });
hashtagSchema.index({ category: 1, language: 1 });
hashtagSchema.index({ isTrending: 1, createdAt: -1 });
hashtagSchema.index({ isFeatured: 1, usageCount: -1 });

// Pre-save middleware
hashtagSchema.pre('save', function(next) {
  // Ensure displayName starts with #
  if (!this.displayName.startsWith('#')) {
    this.displayName = '#' + this.displayName;
  }
  
  // Calculate trending score
  this.trendingScore = this.calculateTrendingScore();
  
  next();
});

// Method to increment usage
hashtagSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  
  // Update daily usage
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const todayUsage = this.dailyUsage.find(usage => 
    usage.date.getTime() === today.getTime()
  );
  
  if (todayUsage) {
    todayUsage.count += 1;
  } else {
    this.dailyUsage.push({
      date: today,
      count: 1
    });
  }
  
  // Keep only last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  this.dailyUsage = this.dailyUsage.filter(usage => usage.date >= thirtyDaysAgo);
  
  this.trendingScore = this.calculateTrendingScore();
  
  return this.save();
};

// Method to calculate trending score
hashtagSchema.methods.calculateTrendingScore = function() {
  const now = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  
  // Calculate usage in last 7 days
  const recentUsage = this.dailyUsage
    .filter(usage => usage.date >= sevenDaysAgo)
    .reduce((total, usage) => total + usage.count, 0);
  
  // Weight recent usage more heavily
  const totalUsageWeight = this.usageCount * 0.3;
  const recentUsageWeight = recentUsage * 0.7;
  
  return totalUsageWeight + recentUsageWeight;
};

// Static method to get trending hashtags
hashtagSchema.statics.getTrending = function(limit = 20, category = null) {
  const query = {
    isBanned: false,
    moderationStatus: 'approved'
  };
  
  if (category) {
    query.category = category;
  }
  
  return this.find(query)
    .sort({ 
      isTrending: -1,
      trendingScore: -1,
      usageCount: -1 
    })
    .limit(limit);
};

// Static method to get featured hashtags
hashtagSchema.statics.getFeatured = function(limit = 10) {
  return this.find({
    isFeatured: true,
    isBanned: false,
    moderationStatus: 'approved'
  })
  .sort({ usageCount: -1 })
  .limit(limit);
};

// Static method to search hashtags
hashtagSchema.statics.searchHashtags = function(query, options = {}) {
  const {
    page = 1,
    limit = 20,
    category = null,
    language = null
  } = options;
  
  const skip = (page - 1) * limit;
  
  const searchQuery = {
    $text: { $search: query },
    isBanned: false,
    moderationStatus: 'approved'
  };
  
  if (category) searchQuery.category = category;
  if (language) searchQuery.language = language;
  
  return this.find(
    searchQuery,
    { score: { $meta: 'textScore' } }
  )
  .sort({ score: { $meta: 'textScore' } })
  .skip(skip)
  .limit(limit);
};

// Static method to get hashtags by category
hashtagSchema.statics.getByCategory = function(category, options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'popular'
  } = options;
  
  const skip = (page - 1) * limit;
  
  let sortOptions = {};
  switch (sortBy) {
    case 'recent':
      sortOptions = { createdAt: -1 };
      break;
    case 'alphabetical':
      sortOptions = { displayName: 1 };
      break;
    default:
      sortOptions = { usageCount: -1 };
  }
  
  return this.find({
    category: category,
    isBanned: false,
    moderationStatus: 'approved'
  })
  .sort(sortOptions)
  .skip(skip)
  .limit(limit);
};

// Static method to find or create hashtag
hashtagSchema.statics.findOrCreate = async function(name, displayName = null) {
  const cleanName = name.toLowerCase().replace(/^#/, '');
  const cleanDisplayName = displayName || '#' + cleanName;
  
  let hashtag = await this.findOne({ name: cleanName });
  
  if (!hashtag) {
    hashtag = new this({
      name: cleanName,
      displayName: cleanDisplayName
    });
    await hashtag.save();
  }
  
  return hashtag;
};

// Static method to get related hashtags
hashtagSchema.statics.getRelated = function(hashtagId, limit = 10) {
  return this.findById(hashtagId)
    .populate('relatedHashtags')
    .then(hashtag => {
      if (!hashtag || hashtag.relatedHashtags.length === 0) {
        // If no related hashtags, find by category
        return this.find({
          _id: { $ne: hashtagId },
          category: hashtag?.category,
          isBanned: false,
          moderationStatus: 'approved'
        })
        .sort({ usageCount: -1 })
        .limit(limit);
      }
      return hashtag.relatedHashtags.slice(0, limit);
    });
};

// Static method to update trending status
hashtagSchema.statics.updateTrendingStatus = async function() {
  // Get top hashtags by trending score
  const trendingHashtags = await this.find({
    isBanned: false,
    moderationStatus: 'approved'
  })
  .sort({ trendingScore: -1 })
  .limit(50);
  
  // Reset all trending status
  await this.updateMany({}, { isTrending: false });
  
  // Set trending status for top hashtags
  const trendingIds = trendingHashtags.map(h => h._id);
  await this.updateMany(
    { _id: { $in: trendingIds } },
    { isTrending: true }
  );
  
  return trendingHashtags.length;
};

// Static method to get hashtag statistics
hashtagSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $match: { isBanned: false }
    },
    {
      $group: {
        _id: null,
        totalHashtags: { $sum: 1 },
        totalUsage: { $sum: '$usageCount' },
        avgUsage: { $avg: '$usageCount' },
        categoryBreakdown: {
          $push: '$category'
        },
        languageBreakdown: {
          $push: '$language'
        }
      }
    }
  ]);
};

// Virtual for usage growth
hashtagSchema.virtual('usageGrowth').get(function() {
  if (this.dailyUsage.length < 2) return 0;
  
  const today = this.dailyUsage[this.dailyUsage.length - 1]?.count || 0;
  const yesterday = this.dailyUsage[this.dailyUsage.length - 2]?.count || 0;
  
  if (yesterday === 0) return today > 0 ? 100 : 0;
  
  return ((today - yesterday) / yesterday) * 100;
});

module.exports = mongoose.model('Hashtag', hashtagSchema);
