// =====================================================
// تطبيق TikTok Clone الثوري
// =====================================================

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'constants/app_constants.dart';
import 'themes/app_theme.dart';
import 'services/storage_service.dart';
import 'services/api_service.dart';
import 'services/auth_service.dart';
import 'services/feature_service.dart';
import 'controllers/app_controller.dart';
import 'screens/splash/splash_screen.dart';
import 'utils/app_bindings.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Firebase
  await Firebase.initializeApp();
  
  // تهيئة Hive للتخزين المحلي
  await Hive.initFlutter();
  
  // تهيئة الخدمات
  await initServices();
  
  // تشغيل التطبيق
  runApp(TikTokCloneApp());
}

// تهيئة الخدمات الأساسية
Future<void> initServices() async {
  try {
    // تهيئة خدمة التخزين
    await Get.putAsync(() => StorageService().init());
    
    // تهيئة خدمة API
    Get.put(ApiService());
    
    // تهيئة خدمة المصادقة
    Get.put(AuthService());
    
    // تهيئة خدمة الميزات
    Get.put(FeatureService());
    
    // تهيئة تحكم التطبيق الرئيسي
    Get.put(AppController());
    
    print('✅ تم تهيئة جميع الخدمات بنجاح');
    
  } catch (e) {
    print('❌ خطأ في تهيئة الخدمات: $e');
  }
}

class TikTokCloneApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      // إعدادات التطبيق الأساسية
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      
      // إعدادات التصميم
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      
      // إعدادات اللغة
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),
      supportedLocales: const [
        Locale('ar', 'SA'),
        Locale('en', 'US'),
        Locale('fr', 'FR'),
        Locale('es', 'ES'),
        Locale('de', 'DE'),
      ],
      
      // الشاشة الأولى
      home: SplashScreen(),
      
      // ربط التبعيات
      initialBinding: AppBindings(),
      
      // إعدادات التنقل
      defaultTransition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 300),
      
      // إعدادات النظام
      builder: (context, child) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Theme.of(context).brightness == Brightness.dark 
                ? Brightness.light 
                : Brightness.dark,
            systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
            systemNavigationBarIconBrightness: Theme.of(context).brightness == Brightness.dark 
                ? Brightness.light 
                : Brightness.dark,
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              textScaleFactor: 1.0, // منع تكبير النص من إعدادات النظام
            ),
            child: child!,
          ),
        );
      },
      
      // معالج الأخطاء
      unknownRoute: GetPage(
        name: '/unknown',
        page: () => Scaffold(
          appBar: AppBar(title: Text('صفحة غير موجودة')),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'الصفحة غير موجودة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text(
                  'لا يمكن العثور على الصفحة المطلوبة',
                  style: TextStyle(color: Colors.grey),
                ),
                SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Get.offAllNamed('/'),
                  child: Text('العودة للرئيسية'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
