-- =====================================================
-- Migration: Create Live Streams Table
-- =====================================================

CREATE TABLE IF NOT EXISTS live_streams (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    
    -- Stream Details
    title VARCHAR(255) NOT NULL,
    description TEXT,
    thumbnail_url VARCHAR(500),
    
    -- Stream Configuration
    stream_key VARCHAR(255) UNIQUE NOT NULL,
    stream_url VARCHAR(500),
    rtmp_url VARCHAR(500),
    hls_url VARCHAR(500),
    
    -- Quality Settings
    resolution VARCHAR(20) DEFAULT '720p',
    bitrate INT DEFAULT 2500, -- kbps
    fps INT DEFAULT 30,
    
    -- Status
    status ENUM('scheduled', 'live', 'ended', 'cancelled') DEFAULT 'scheduled',
    is_private BOOLEAN DEFAULT FALSE,
    allow_comments BOOLEAN DEFAULT TRUE,
    allow_gifts BOOLEAN DEFAULT TRUE,
    
    -- Engagement
    viewers_count INT DEFAULT 0,
    max_viewers_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    gifts_count INT DEFAULT 0,
    
    -- Timing
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    duration INT DEFAULT 0, -- in seconds
    
    -- Moderation
    is_featured BOOLEAN DEFAULT FALSE,
    content_warning BOOLEAN DEFAULT FALSE,
    age_restriction ENUM('none', '13+', '16+', '18+') DEFAULT 'none',
    
    -- Recording
    is_recorded BOOLEAN DEFAULT TRUE,
    recording_url VARCHAR(500),
    
    -- AI Analysis
    ai_tags JSON,
    ai_content_score DECIMAL(3,2),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_started_at (started_at),
    INDEX idx_viewers_count (viewers_count),
    INDEX idx_is_featured (is_featured),
    INDEX idx_stream_key (stream_key)
);
