// =====================================================
// مسارات المستخدمين
// =====================================================

const express = require('express');
const Joi = require('joi');
const User = require('../models/User');
const { verifyToken, optionalAuth, requireFeature } = require('../middleware/auth');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');
const { query } = require('../../database/config/database');
const logger = require('../utils/logger');

const router = express.Router();

// مخططات التحقق من البيانات
const updateProfileSchema = Joi.object({
  first_name: Joi.string().min(2).max(50).optional(),
  last_name: Joi.string().min(2).max(50).optional(),
  bio: Joi.string().max(500).optional(),
  birth_date: Joi.date().optional(),
  gender: Joi.string().valid('male', 'female', 'other').optional(),
  country: Joi.string().max(100).optional(),
  city: Joi.string().max(100).optional(),
  language: Joi.string().valid('ar', 'en', 'fr', 'es', 'de').optional(),
  timezone: Joi.string().optional(),
  is_private: Joi.boolean().optional()
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string()
    .min(8)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])'))
    .required(),
  confirmPassword: Joi.string()
    .valid(Joi.ref('newPassword'))
    .required()
});

// =====================================================
// البحث عن المستخدمين
// =====================================================
router.get('/search', optionalAuth, async (req, res, next) => {
  try {
    const { q, page = 1, limit = 20 } = req.query;
    
    if (!q || q.trim().length < 2) {
      throw new ValidationError('يجب أن يكون البحث حرفين على الأقل');
    }
    
    const offset = (page - 1) * limit;
    const searchTerm = `%${q.trim()}%`;
    
    const users = await query(
      `SELECT id, username, first_name, last_name, avatar_url, is_verified, 
              followers_count, videos_count
       FROM users 
       WHERE (username LIKE ? OR first_name LIKE ? OR last_name LIKE ?) 
             AND is_active = 1 AND is_banned = 0
       ORDER BY 
         CASE WHEN username = ? THEN 1 ELSE 2 END,
         followers_count DESC,
         is_verified DESC
       LIMIT ? OFFSET ?`,
      [searchTerm, searchTerm, searchTerm, q.trim(), parseInt(limit), offset]
    );
    
    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: users.length
        }
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// الحصول على ملف مستخدم محدد
// =====================================================
router.get('/:username', optionalAuth, async (req, res, next) => {
  try {
    const { username } = req.params;
    
    const user = await User.findByUsername(username);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    let profile = user.toPublicProfile();
    
    // إضافة معلومات إضافية إذا كان المستخدم مسجل دخول
    if (req.user) {
      // التحقق من المتابعة
      const followStatus = await query(
        'SELECT id FROM follows WHERE follower_id = ? AND following_id = ?',
        [req.user.id, user.id]
      );
      
      profile.is_following = followStatus.length > 0;
      profile.is_own_profile = req.user.id === user.id;
    }
    
    res.json({
      success: true,
      data: {
        user: profile
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// تحديث الملف الشخصي
// =====================================================
router.put('/profile', verifyToken, async (req, res, next) => {
  try {
    // التحقق من صحة البيانات
    const { error, value } = updateProfileSchema.validate(req.body);
    if (error) {
      throw new ValidationError(error.details[0].message);
    }
    
    const user = await User.findById(req.user.id);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    // تحديث البيانات
    await user.update(value);
    
    res.json({
      success: true,
      message: 'تم تحديث الملف الشخصي بنجاح',
      data: {
        user: user.toJSON()
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// تغيير كلمة المرور
// =====================================================
router.put('/password', verifyToken, async (req, res, next) => {
  try {
    // التحقق من صحة البيانات
    const { error, value } = changePasswordSchema.validate(req.body);
    if (error) {
      throw new ValidationError(error.details[0].message);
    }
    
    const { currentPassword, newPassword } = value;
    
    const user = await User.findById(req.user.id);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    // تغيير كلمة المرور
    await user.changePassword(currentPassword, newPassword);
    
    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// متابعة مستخدم
// =====================================================
router.post('/:userId/follow', verifyToken, requireFeature('social_interactions'), async (req, res, next) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findById(req.user.id);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    await user.follow(parseInt(userId));
    
    res.json({
      success: true,
      message: 'تم متابعة المستخدم بنجاح'
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// إلغاء متابعة مستخدم
// =====================================================
router.delete('/:userId/follow', verifyToken, requireFeature('social_interactions'), async (req, res, next) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findById(req.user.id);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    await user.unfollow(parseInt(userId));
    
    res.json({
      success: true,
      message: 'تم إلغاء متابعة المستخدم بنجاح'
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// الحصول على قائمة المتابعين
// =====================================================
router.get('/:userId/followers', optionalAuth, async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    
    const user = await User.findById(userId);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    // التحقق من الخصوصية
    if (user.is_private && (!req.user || req.user.id !== user.id)) {
      // التحقق من المتابعة
      if (req.user) {
        const followStatus = await query(
          'SELECT id FROM follows WHERE follower_id = ? AND following_id = ?',
          [req.user.id, user.id]
        );
        
        if (followStatus.length === 0) {
          throw new ValidationError('هذا الحساب خاص');
        }
      } else {
        throw new ValidationError('هذا الحساب خاص');
      }
    }
    
    const followers = await user.getFollowers(parseInt(page), parseInt(limit));
    
    res.json({
      success: true,
      data: {
        followers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: followers.length
        }
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// الحصول على قائمة المتابَعين
// =====================================================
router.get('/:userId/following', optionalAuth, async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    
    const user = await User.findById(userId);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    // التحقق من الخصوصية
    if (user.is_private && (!req.user || req.user.id !== user.id)) {
      if (req.user) {
        const followStatus = await query(
          'SELECT id FROM follows WHERE follower_id = ? AND following_id = ?',
          [req.user.id, user.id]
        );
        
        if (followStatus.length === 0) {
          throw new ValidationError('هذا الحساب خاص');
        }
      } else {
        throw new ValidationError('هذا الحساب خاص');
      }
    }
    
    const following = await user.getFollowing(parseInt(page), parseInt(limit));
    
    res.json({
      success: true,
      data: {
        following,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: following.length
        }
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// الحصول على المستخدمين المقترحين
// =====================================================
router.get('/suggestions/for-you', verifyToken, requireFeature('ai_recommendations'), async (req, res, next) => {
  try {
    const { limit = 10 } = req.query;
    
    // خوارزمية بسيطة للاقتراحات
    const suggestions = await query(
      `SELECT u.id, u.username, u.first_name, u.last_name, u.avatar_url, 
              u.is_verified, u.followers_count, u.videos_count
       FROM users u
       WHERE u.id != ? 
             AND u.is_active = 1 
             AND u.is_banned = 0
             AND u.id NOT IN (
               SELECT following_id FROM follows WHERE follower_id = ?
             )
       ORDER BY 
         u.is_verified DESC,
         u.followers_count DESC,
         RAND()
       LIMIT ?`,
      [req.user.id, req.user.id, parseInt(limit)]
    );
    
    res.json({
      success: true,
      data: {
        suggestions
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// إحصائيات المستخدم
// =====================================================
router.get('/:userId/stats', optionalAuth, async (req, res, next) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findById(userId);
    if (!user) {
      throw new NotFoundError('المستخدم غير موجود');
    }
    
    // إحصائيات أساسية
    const stats = {
      followers_count: user.followers_count,
      following_count: user.following_count,
      videos_count: user.videos_count,
      likes_received_count: user.likes_received_count
    };
    
    // إحصائيات إضافية للمالك فقط
    if (req.user && req.user.id === user.id) {
      const additionalStats = await query(
        `SELECT 
           COUNT(DISTINCT v.id) as total_videos,
           SUM(v.views_count) as total_views,
           SUM(v.likes_count) as total_likes,
           SUM(v.comments_count) as total_comments,
           SUM(v.shares_count) as total_shares
         FROM videos v 
         WHERE v.user_id = ? AND v.is_public = 1`,
        [user.id]
      );
      
      if (additionalStats.length > 0) {
        Object.assign(stats, additionalStats[0]);
      }
    }
    
    res.json({
      success: true,
      data: {
        stats
      }
    });
    
  } catch (error) {
    next(error);
  }
});

module.exports = router;
