{"name": "tiktok-clone-revolutionary", "version": "1.0.0", "description": "مشروع TikTok Clone ثوري متكامل مع 48+ ميزة مبتكرة", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run web:dev\" \"npm run admin:dev\"", "backend:dev": "cd backend && npm run dev", "web:dev": "cd web_app && npm run dev", "admin:dev": "cd admin_panel && npm run dev", "mobile:dev": "cd mobile_app && flutter run", "build": "npm run backend:build && npm run web:build && npm run admin:build", "backend:build": "cd backend && npm run build", "web:build": "cd web_app && npm run build", "admin:build": "cd admin_panel && npm run build", "mobile:build": "cd mobile_app && flutter build apk && flutter build ios", "test": "npm run backend:test && npm run web:test && npm run admin:test", "backend:test": "cd backend && npm test", "web:test": "cd web_app && npm test", "admin:test": "cd admin_panel && npm test", "mobile:test": "cd mobile_app && flutter test", "setup": "npm run setup:backend && npm run setup:web && npm run setup:admin && npm run setup:mobile", "setup:backend": "cd backend && npm install", "setup:web": "cd web_app && npm install", "setup:admin": "cd admin_panel && npm install", "setup:mobile": "cd mobile_app && flutter pub get", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset"}, "keywords": ["tiktok", "clone", "flutter", "react", "nodejs", "ai", "revolutionary", "social-media", "video-sharing"], "author": "Augment Agent", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/tiktok-clone-revolutionary.git"}, "bugs": {"url": "https://github.com/your-username/tiktok-clone-revolutionary/issues"}, "homepage": "https://github.com/your-username/tiktok-clone-revolutionary#readme"}