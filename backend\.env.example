# =====================================================
# متغيرات البيئة لـ TikTok Clone Backend
# =====================================================

# إعدادات الخادم
NODE_ENV=development
PORT=5000
APP_NAME="TikTok Clone"
APP_URL=http://localhost:5000

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=tiktok_clone_db

# قاعدة بيانات الاختبار
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_USER=root
TEST_DB_PASSWORD=
TEST_DB_NAME=tiktok_clone_test_db

# إعدادات JWT
JWT_SECRET=your-super-secret-jwt-key-here-make-it-very-long-and-random
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-here
JWT_REFRESH_EXPIRE=30d

# إعدادات Redis (للتخزين المؤقت)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# إعدادات البريد الإلكتروني
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-app-password
MAIL_FROM=<EMAIL>

# إعدادات Twilio (للرسائل النصية)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# إعدادات Stripe (للدفع)
STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# إعدادات PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

# إعدادات Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# إعدادات AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# إعدادات CloudFlare
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
CLOUDFLARE_ZONE_ID=your-zone-id

# إعدادات الذكاء الاصطناعي
OPENAI_API_KEY=sk-your-openai-api-key
REPLICATE_API_TOKEN=r8_your-replicate-token
HUGGINGFACE_API_KEY=hf_your-huggingface-key

# إعدادات Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# إعدادات Facebook OAuth
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# إعدادات التطبيقات الأمامية
FRONTEND_URL=http://localhost:3000
WEB_URL=http://localhost:3001
ADMIN_URL=http://localhost:3002

# إعدادات الملفات
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=100MB
ALLOWED_VIDEO_FORMATS=mp4,mov,avi,mkv
ALLOWED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp
ALLOWED_AUDIO_FORMATS=mp3,wav,aac,m4a

# إعدادات الفيديو
MAX_VIDEO_DURATION=180
MAX_VIDEO_SIZE=*********
VIDEO_QUALITY=1080p
THUMBNAIL_SIZE=300x300

# إعدادات البث المباشر
LIVE_STREAM_KEY_LENGTH=32
MAX_LIVE_DURATION=7200
LIVE_QUALITY=1080p

# إعدادات الأمان
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# إعدادات التسجيل
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# إعدادات المراقبة
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# إعدادات التطوير
DEBUG=true
ENABLE_CORS=true
ENABLE_SWAGGER=true

# إعدادات الإنتاج
ENABLE_HTTPS=false
SSL_CERT_PATH=
SSL_KEY_PATH=
