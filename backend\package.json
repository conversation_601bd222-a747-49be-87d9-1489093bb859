{"name": "tiktok-clone-backend", "version": "1.0.0", "description": "Backend API لمشروع TikTok Clone الثوري", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "node ../database/migrate.js migrate", "db:seed": "node ../database/migrate.js seed", "db:reset": "node ../database/migrate.js reset", "db:rollback": "node ../database/migrate.js rollback", "build": "echo 'Backend build completed'", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["tiktok", "clone", "api", "backend", "nodejs", "express", "mysql", "socket.io", "ai"], "author": "Augment Agent", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "redis": "^4.6.10", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "stripe": "^14.9.0", "axios": "^1.6.2", "sharp": "^0.32.6", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "node-cron": "^3.0.3", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "passport-facebook": "^3.0.0", "winston": "^3.11.0", "moment": "^2.29.4", "uuid": "^9.0.1", "lodash": "^4.17.21", "crypto": "^1.0.1", "fs-extra": "^11.1.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/tiktok-clone-revolutionary.git"}}