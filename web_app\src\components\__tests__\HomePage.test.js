// =====================================================
// Home Page Tests
// =====================================================

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import HomePage from '../HomePage';

// Mock store
const mockStore = configureStore({
  reducer: {
    auth: (state = { isAuthenticated: false, user: null }, action) => state,
    theme: (state = { isDarkMode: false }, action) => state,
  },
});

// Mock theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <Provider store={mockStore}>
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('HomePage', () => {
  beforeEach(() => {
    // Clear any mocks
    jest.clearAllMocks();
  });

  it('renders homepage correctly', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check if main heading is present
    expect(screen.getByText(/مرحباً بك في TikTok Clone/i)).toBeInTheDocument();
  });

  it('displays hero section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check hero section elements
    expect(screen.getByText(/مرحباً بك في TikTok Clone/i)).toBeInTheDocument();
    expect(screen.getByText(/اكتشف عالماً جديداً/i)).toBeInTheDocument();
    expect(screen.getByText(/ابدأ الاستكشاف/i)).toBeInTheDocument();
  });

  it('displays statistics section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check statistics
    expect(screen.getByText(/المستخدمين النشطين/i)).toBeInTheDocument();
    expect(screen.getByText(/الفيديوهات المنشورة/i)).toBeInTheDocument();
    expect(screen.getByText(/المشاهدات اليومية/i)).toBeInTheDocument();
    expect(screen.getByText(/التقييم/i)).toBeInTheDocument();
  });

  it('displays features section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check features
    expect(screen.getByText(/الميزات الثورية/i)).toBeInTheDocument();
    expect(screen.getByText(/التوأم الإبداعي/i)).toBeInTheDocument();
    expect(screen.getByText(/الرادار الاجتماعي/i)).toBeInTheDocument();
    expect(screen.getByText(/توليد فيديو بالصوت/i)).toBeInTheDocument();
    expect(screen.getByText(/وضع الفن الفوري/i)).toBeInTheDocument();
  });

  it('handles start exploration button click', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    const startButton = screen.getByText(/ابدأ الاستكشاف/i);
    fireEvent.click(startButton);

    // Button should be clickable (no errors thrown)
    expect(startButton).toBeInTheDocument();
  });

  it('displays call to action section', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check CTA section
    expect(screen.getByText(/جاهز لتجربة المستقبل؟/i)).toBeInTheDocument();
    expect(screen.getByText(/انضم إلى ملايين المستخدمين/i)).toBeInTheDocument();
    expect(screen.getByText(/ابدأ الآن مجاناً/i)).toBeInTheDocument();
  });

  it('has proper responsive design classes', () => {
    const { container } = render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check if container has proper Material-UI classes
    const mainContainer = container.querySelector('.MuiContainer-root');
    expect(mainContainer).toBeInTheDocument();
  });

  it('displays feature cards with proper structure', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check if feature cards are rendered
    const featureCards = screen.getAllByText(/جديد|شائع|ثوري|مميز/i);
    expect(featureCards.length).toBeGreaterThan(0);
  });

  it('handles feature card interactions', () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Find feature cards
    const cards = document.querySelectorAll('[data-testid="feature-card"]');
    
    // If cards exist, test hover interactions
    if (cards.length > 0) {
      fireEvent.mouseEnter(cards[0]);
      fireEvent.mouseLeave(cards[0]);
    }

    // No errors should be thrown
    expect(true).toBe(true);
  });

  it('displays proper Arabic text direction', () => {
    const { container } = render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>
    );

    // Check if Arabic text is properly displayed
    const arabicText = screen.getByText(/مرحباً بك في TikTok Clone/i);
    expect(arabicText).toBeInTheDocument();
  });
});
