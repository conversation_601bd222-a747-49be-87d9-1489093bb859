<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#FF0050" />
    <meta name="description" content="لوحة التحكم الإدارية - TikTok Clone" />
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- CSS Variables -->
    <style>
      :root {
        --primary-color: #FF0050;
        --secondary-color: #25F4EE;
        --accent-color: #FE2C55;
        --success-color: #00D9FF;
        --warning-color: #FFB800;
        --error-color: #FF3040;
        --info-color: #007AFF;
        --text-primary: #161823;
        --text-secondary: #69707D;
        --text-light: #A1A2A7;
        --background-light: #FFFFFF;
        --background-dark: #000000;
        --surface-light: #F8F8F8;
        --surface-dark: #161823;
        --border-color: #E1E2E3;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --sidebar-width: 280px;
        --header-height: 64px;
      }
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Cairo', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: var(--surface-light);
        color: var(--text-primary);
        line-height: 1.6;
        overflow-x: hidden;
      }
      
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Admin Loading Spinner */
      .admin-loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .admin-loading-logo {
        width: 100px;
        height: 100px;
        background: white;
        border-radius: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 24px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        animation: adminPulse 2s infinite;
      }
      
      .admin-loading-text {
        color: white;
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
      
      .admin-loading-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 32px;
      }
      
      .admin-loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: adminSpin 1s linear infinite;
      }
      
      @keyframes adminPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      @keyframes adminSpin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Admin Scrollbar */
      ::-webkit-scrollbar {
        width: 12px;
        height: 12px;
      }
      
      ::-webkit-scrollbar-track {
        background: var(--surface-light);
        border-radius: 6px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 6px;
        border: 2px solid var(--surface-light);
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: var(--accent-color);
      }
      
      ::-webkit-scrollbar-corner {
        background: var(--surface-light);
      }
      
      /* Dark mode */
      @media (prefers-color-scheme: dark) {
        :root {
          --text-primary: #FFFFFF;
          --text-secondary: #A1A2A7;
          --background-light: #000000;
          --surface-light: #161823;
          --border-color: #2F2F2F;
        }
        
        body {
          background-color: var(--background-light);
          color: var(--text-primary);
        }
        
        ::-webkit-scrollbar-track {
          background: var(--surface-dark);
        }
        
        ::-webkit-scrollbar-thumb {
          border-color: var(--surface-dark);
        }
        
        ::-webkit-scrollbar-corner {
          background: var(--surface-dark);
        }
      }
      
      /* Responsive Design */
      @media (max-width: 768px) {
        :root {
          --sidebar-width: 260px;
          --header-height: 56px;
        }
        
        body {
          font-size: 14px;
        }
        
        .admin-loading-logo {
          width: 80px;
          height: 80px;
        }
        
        .admin-loading-text {
          font-size: 20px;
        }
        
        .admin-loading-subtitle {
          font-size: 14px;
        }
      }
      
      /* Print Styles */
      @media print {
        .no-print {
          display: none !important;
        }
        
        body {
          background: white !important;
          color: black !important;
        }
        
        * {
          box-shadow: none !important;
        }
      }
      
      /* High Contrast Mode */
      @media (prefers-contrast: high) {
        :root {
          --border-color: currentColor;
        }
        
        * {
          border-color: currentColor !important;
        }
      }
      
      /* Reduced Motion */
      @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    </style>
    
    <title>لوحة التحكم الإدارية - TikTok Clone</title>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif; background: #f5f5f5; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
        <div style="background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); max-width: 400px;">
          <h2 style="color: #FF0050; margin-bottom: 16px;">يتطلب JavaScript</h2>
          <p style="color: #666; margin-bottom: 16px;">تحتاج لوحة التحكم الإدارية إلى JavaScript ليعمل بشكل صحيح.</p>
          <p style="color: #666;">يرجى تفعيل JavaScript في متصفحك والمحاولة مرة أخرى.</p>
        </div>
      </div>
    </noscript>
    
    <!-- Admin Loading Screen -->
    <div id="admin-initial-loading" class="admin-loading-container">
      <div class="admin-loading-logo">
        <span style="font-size: 50px; color: var(--primary-color);">⚙️</span>
      </div>
      <div class="admin-loading-text">لوحة التحكم الإدارية</div>
      <div class="admin-loading-subtitle">TikTok Clone Admin Panel</div>
      <div class="admin-loading-subtitle">جاري تحميل النظام...</div>
      <div class="admin-loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <!-- Remove loading screen when React loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingElement = document.getElementById('admin-initial-loading');
          if (loadingElement) {
            loadingElement.style.opacity = '0';
            loadingElement.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingElement.remove();
            }, 500);
          }
        }, 1500);
      });
      
      // Admin Panel Security
      if (process.env.NODE_ENV === 'production') {
        // Disable right-click context menu
        document.addEventListener('contextmenu', function(e) {
          e.preventDefault();
        });
        
        // Disable F12, Ctrl+Shift+I, Ctrl+U
        document.addEventListener('keydown', function(e) {
          if (e.key === 'F12' || 
              (e.ctrlKey && e.shiftKey && e.key === 'I') ||
              (e.ctrlKey && e.key === 'u')) {
            e.preventDefault();
          }
        });
      }
    </script>
  </body>
</html>
