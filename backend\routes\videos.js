// =====================================================
// Video Routes - مسارات الفيديو
// =====================================================

const express = require('express');
const { body, query } = require('express-validator');
const videoController = require('../controllers/videoController');
const auth = require('../middleware/auth');
const optionalAuth = require('../middleware/optionalAuth');
const upload = require('../middleware/upload');
const rateLimit = require('../middleware/rateLimit');

const router = express.Router();

// معدل محدود للرفع
const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 uploads per hour
  message: 'تم تجاوز عدد مرات الرفع المسموحة في الساعة'
});

// الحصول على الخلاصة
router.get('/feed', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100'),
  
  query('category')
    .optional()
    .isString()
    .withMessage('الفئة يجب أن تكون نص'),
  
  query('following')
    .optional()
    .isBoolean()
    .withMessage('متابعة يجب أن تكون true أو false')
], videoController.getFeed);

// رفع فيديو جديد
router.post('/upload', auth, uploadLimiter, upload.fields([
  { name: 'video', maxCount: 1 },
  { name: 'thumbnail', maxCount: 1 }
]), [
  body('title')
    .optional()
    .isLength({ max: 255 })
    .withMessage('العنوان يجب أن يكون أقل من 255 حرف')
    .trim(),
  
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('الوصف يجب أن يكون أقل من 2000 حرف')
    .trim(),
  
  body('hashtags')
    .optional()
    .isString()
    .withMessage('الهاشتاغات يجب أن تكون نص'),
  
  body('musicId')
    .optional()
    .isMongoId()
    .withMessage('معرف الموسيقى غير صحيح'),
  
  body('privacy')
    .optional()
    .isIn(['public', 'friends', 'private'])
    .withMessage('الخصوصية يجب أن تكون public أو friends أو private'),
  
  body('allowComments')
    .optional()
    .isBoolean()
    .withMessage('السماح بالتعليقات يجب أن يكون true أو false'),
  
  body('allowDuet')
    .optional()
    .isBoolean()
    .withMessage('السماح بالدويت يجب أن يكون true أو false'),
  
  body('allowStitch')
    .optional()
    .isBoolean()
    .withMessage('السماح بالستيتش يجب أن يكون true أو false'),
  
  body('allowDownload')
    .optional()
    .isBoolean()
    .withMessage('السماح بالتحميل يجب أن يكون true أو false')
], videoController.uploadVideo);

// الحصول على فيديو محدد
router.get('/:id', optionalAuth, videoController.getVideo);

// الإعجاب بالفيديو
router.post('/:id/like', auth, videoController.likeVideo);

// إلغاء الإعجاب بالفيديو
router.delete('/:id/like', auth, videoController.likeVideo);

// مشاركة الفيديو
router.post('/:id/share', optionalAuth, [
  body('platform')
    .optional()
    .isIn(['internal', 'facebook', 'twitter', 'whatsapp', 'telegram'])
    .withMessage('منصة المشاركة غير صحيحة')
], videoController.shareVideo);

// حذف الفيديو
router.delete('/:id', auth, videoController.deleteVideo);

// تحديث الفيديو
router.put('/:id', auth, [
  body('title')
    .optional()
    .isLength({ max: 255 })
    .withMessage('العنوان يجب أن يكون أقل من 255 حرف')
    .trim(),
  
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('الوصف يجب أن يكون أقل من 2000 حرف')
    .trim(),
  
  body('privacy')
    .optional()
    .isIn(['public', 'friends', 'private'])
    .withMessage('الخصوصية يجب أن تكون public أو friends أو private'),
  
  body('allowComments')
    .optional()
    .isBoolean()
    .withMessage('السماح بالتعليقات يجب أن يكون true أو false')
], async (req, res) => {
  try {
    // تحديث الفيديو
    res.json({ message: 'تحديث الفيديو قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تحديث الفيديو' });
  }
});

// البحث في الفيديوهات
router.get('/search/videos', [
  query('q')
    .notEmpty()
    .withMessage('نص البحث مطلوب')
    .isLength({ min: 1, max: 100 })
    .withMessage('نص البحث يجب أن يكون بين 1 و 100 حرف'),
  
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100'),
  
  query('sortBy')
    .optional()
    .isIn(['relevance', 'recent', 'popular', 'likes'])
    .withMessage('ترتيب البحث غير صحيح')
], videoController.searchVideos);

// الحصول على الفيديوهات الرائجة
router.get('/trending/videos', [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], videoController.getTrendingVideos);

// الحصول على الفيديوهات المميزة
router.get('/featured/videos', [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], videoController.getFeaturedVideos);

// الحصول على فيديوهات مستخدم محدد
router.get('/user/:userId', optionalAuth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100'),
  
  query('privacy')
    .optional()
    .isIn(['public', 'friends', 'private', 'all'])
    .withMessage('نوع الخصوصية غير صحيح')
], videoController.getUserVideos);

// الحصول على الفيديوهات المحفوظة
router.get('/saved/videos', auth, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد العناصر يجب أن يكون بين 1 و 100')
], async (req, res) => {
  try {
    // الحصول على الفيديوهات المحفوظة
    res.json({ message: 'الفيديوهات المحفوظة قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب الفيديوهات المحفوظة' });
  }
});

// حفظ الفيديو
router.post('/:id/save', auth, async (req, res) => {
  try {
    // حفظ الفيديو
    res.json({ message: 'حفظ الفيديو قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في حفظ الفيديو' });
  }
});

// إلغاء حفظ الفيديو
router.delete('/:id/save', auth, async (req, res) => {
  try {
    // إلغاء حفظ الفيديو
    res.json({ message: 'إلغاء حفظ الفيديو قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في إلغاء حفظ الفيديو' });
  }
});

// الإبلاغ عن الفيديو
router.post('/:id/report', auth, [
  body('reason')
    .notEmpty()
    .withMessage('سبب الإبلاغ مطلوب')
    .isIn([
      'spam', 'harassment', 'hate_speech', 'violence',
      'nudity', 'copyright', 'fake_news', 'scam',
      'underage', 'drugs', 'self_harm', 'other'
    ])
    .withMessage('سبب الإبلاغ غير صحيح'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('وصف الإبلاغ يجب أن يكون أقل من 1000 حرف')
    .trim()
], async (req, res) => {
  try {
    // الإبلاغ عن الفيديو
    res.json({ message: 'الإبلاغ عن الفيديو قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الإبلاغ عن الفيديو' });
  }
});

// الحصول على إحصائيات الفيديو
router.get('/:id/stats', auth, async (req, res) => {
  try {
    // إحصائيات الفيديو
    res.json({ message: 'إحصائيات الفيديو قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في جلب إحصائيات الفيديو' });
  }
});

// تحميل الفيديو
router.get('/:id/download', auth, async (req, res) => {
  try {
    // تحميل الفيديو
    res.json({ message: 'تحميل الفيديو قيد التطوير' });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في تحميل الفيديو' });
  }
});

module.exports = router;
