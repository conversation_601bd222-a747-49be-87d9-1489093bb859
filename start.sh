#!/bin/bash

# =====================================================
# TikTok Clone - سكريبت التشغيل السريع
# =====================================================

echo "🚀 مرحباً بك في TikTok Clone!"
echo "=================================="

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة لطباعة رسائل ملونة
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# التحقق من المتطلبات
check_requirements() {
    print_info "التحقق من المتطلبات..."
    
    # التحقق من Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً"
        exit 1
    fi
    
    # التحقق من npm
    if ! command -v npm &> /dev/null; then
        print_error "npm غير مثبت"
        exit 1
    fi
    
    # التحقق من Flutter
    if ! command -v flutter &> /dev/null; then
        print_warning "Flutter غير مثبت. لن تتمكن من تشغيل التطبيق المحمول"
    fi
    
    # التحقق من Docker
    if ! command -v docker &> /dev/null; then
        print_warning "Docker غير مثبت. لن تتمكن من استخدام Docker Compose"
    fi
    
    print_status "تم التحقق من المتطلبات"
}

# تشغيل Backend
start_backend() {
    print_info "تشغيل Backend API..."
    cd backend
    
    if [ ! -d "node_modules" ]; then
        print_info "تثبيت مكتبات Backend..."
        npm install
    fi
    
    if [ ! -f ".env" ]; then
        print_info "إنشاء ملف .env..."
        cp .env.example .env
        print_warning "يرجى تحديث ملف .env بالإعدادات الصحيحة"
    fi
    
    print_status "Backend يعمل على http://localhost:5000"
    npm run dev &
    cd ..
}

# تشغيل Web App
start_web() {
    print_info "تشغيل موقع الويب..."
    cd web_app
    
    if [ ! -d "node_modules" ]; then
        print_info "تثبيت مكتبات موقع الويب..."
        npm install
    fi
    
    print_status "موقع الويب يعمل على http://localhost:3000"
    npm start &
    cd ..
}

# تشغيل Admin Panel
start_admin() {
    print_info "تشغيل لوحة التحكم الإدارية..."
    cd admin_panel
    
    if [ ! -d "node_modules" ]; then
        print_info "تثبيت مكتبات لوحة التحكم..."
        npm install
    fi
    
    print_status "لوحة التحكم تعمل على http://localhost:3001"
    npm start &
    cd ..
}

# تشغيل Flutter App
start_flutter() {
    if command -v flutter &> /dev/null; then
        print_info "تشغيل تطبيق Flutter..."
        cd mobile_app
        
        print_info "تثبيت مكتبات Flutter..."
        flutter pub get
        
        print_status "تطبيق Flutter جاهز للتشغيل"
        print_info "استخدم: flutter run"
        cd ..
    else
        print_warning "Flutter غير مثبت، تم تخطي تطبيق الهاتف"
    fi
}

# تشغيل مع Docker
start_docker() {
    if command -v docker &> /dev/null; then
        print_info "تشغيل المشروع مع Docker..."
        docker-compose up -d
        print_status "جميع الخدمات تعمل مع Docker"
        print_info "الويب: http://localhost:3000"
        print_info "الإدارة: http://localhost:3001"
        print_info "API: http://localhost:5000"
    else
        print_error "Docker غير مثبت"
        exit 1
    fi
}

# عرض القائمة
show_menu() {
    echo ""
    echo -e "${PURPLE}اختر طريقة التشغيل:${NC}"
    echo "1) تشغيل كامل (Backend + Web + Admin)"
    echo "2) تشغيل Backend فقط"
    echo "3) تشغيل موقع الويب فقط"
    echo "4) تشغيل لوحة التحكم فقط"
    echo "5) إعداد Flutter"
    echo "6) تشغيل مع Docker"
    echo "7) إيقاف جميع العمليات"
    echo "8) عرض الحالة"
    echo "0) خروج"
    echo ""
}

# إيقاف العمليات
stop_all() {
    print_info "إيقاف جميع العمليات..."
    pkill -f "npm"
    pkill -f "node"
    docker-compose down 2>/dev/null
    print_status "تم إيقاف جميع العمليات"
}

# عرض الحالة
show_status() {
    print_info "حالة الخدمات:"
    echo ""
    
    # التحقق من Backend
    if curl -s http://localhost:5000/health > /dev/null 2>&1; then
        print_status "Backend API: يعمل (http://localhost:5000)"
    else
        print_error "Backend API: متوقف"
    fi
    
    # التحقق من Web App
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_status "موقع الويب: يعمل (http://localhost:3000)"
    else
        print_error "موقع الويب: متوقف"
    fi
    
    # التحقق من Admin Panel
    if curl -s http://localhost:3001 > /dev/null 2>&1; then
        print_status "لوحة التحكم: تعمل (http://localhost:3001)"
    else
        print_error "لوحة التحكم: متوقفة"
    fi
}

# البرنامج الرئيسي
main() {
    check_requirements
    
    while true; do
        show_menu
        read -p "اختر رقماً: " choice
        
        case $choice in
            1)
                print_info "تشغيل المشروع كاملاً..."
                start_backend
                sleep 3
                start_web
                sleep 2
                start_admin
                print_status "تم تشغيل جميع المكونات!"
                print_info "الروابط:"
                echo "  - موقع الويب: http://localhost:3000"
                echo "  - لوحة التحكم: http://localhost:3001"
                echo "  - API: http://localhost:5000"
                ;;
            2)
                start_backend
                ;;
            3)
                start_web
                ;;
            4)
                start_admin
                ;;
            5)
                start_flutter
                ;;
            6)
                start_docker
                ;;
            7)
                stop_all
                ;;
            8)
                show_status
                ;;
            0)
                print_info "شكراً لاستخدام TikTok Clone!"
                exit 0
                ;;
            *)
                print_error "خيار غير صحيح"
                ;;
        esac
        
        echo ""
        read -p "اضغط Enter للمتابعة..."
    done
}

# تشغيل البرنامج
main
