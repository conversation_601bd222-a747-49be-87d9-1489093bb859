// =====================================================
// مسارات المصادقة
// =====================================================

const express = require('express');
const Joi = require('joi');
const rateLimit = require('express-rate-limit');
const User = require('../models/User');
const { generateToken, generateRefreshToken, verifyRefreshToken, verifyToken } = require('../middleware/auth');
const { ValidationError, AuthenticationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// تحديد معدل طلبات المصادقة
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // 5 محاولات كحد أقصى
  message: {
    error: 'تم تجاوز الحد المسموح من محاولات تسجيل الدخول',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// مخططات التحقق من البيانات
const registerSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط',
      'string.min': 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل',
      'string.max': 'اسم المستخدم يجب أن يكون 30 حرف كحد أقصى',
      'any.required': 'اسم المستخدم مطلوب'
    }),
  
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'البريد الإلكتروني غير صحيح',
      'any.required': 'البريد الإلكتروني مطلوب'
    }),
  
  password: Joi.string()
    .min(8)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])'))
    .required()
    .messages({
      'string.min': 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
      'string.pattern.base': 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص',
      'any.required': 'كلمة المرور مطلوبة'
    }),
  
  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': 'تأكيد كلمة المرور غير متطابق',
      'any.required': 'تأكيد كلمة المرور مطلوب'
    }),
  
  first_name: Joi.string().min(2).max(50).optional(),
  last_name: Joi.string().min(2).max(50).optional(),
  phone: Joi.string().pattern(/^[+]?[1-9]\d{1,14}$/).optional()
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required(),
  remember: Joi.boolean().optional()
});

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required()
});

// =====================================================
// تسجيل مستخدم جديد
// =====================================================
router.post('/register', authLimiter, async (req, res, next) => {
  try {
    // التحقق من صحة البيانات
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
      throw new ValidationError(error.details[0].message);
    }
    
    const { username, email, password, first_name, last_name, phone } = value;
    
    // إنشاء المستخدم
    const user = await User.create({
      username,
      email,
      password,
      first_name,
      last_name,
      phone
    });
    
    // إنشاء الرموز المميزة
    const token = generateToken({ id: user.id, email: user.email });
    const refreshToken = generateRefreshToken({ id: user.id });
    
    // تسجيل النشاط
    logger.logAuthAttempt(email, true, req.ip, req.get('User-Agent'));
    
    res.status(201).json({
      success: true,
      message: 'تم إنشاء الحساب بنجاح',
      data: {
        user: user.toPublicProfile(),
        token,
        refreshToken
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// تسجيل الدخول
// =====================================================
router.post('/login', authLimiter, async (req, res, next) => {
  try {
    // التحقق من صحة البيانات
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      throw new ValidationError(error.details[0].message);
    }
    
    const { email, password, remember } = value;
    
    // البحث عن المستخدم
    const user = await User.findByEmail(email);
    if (!user) {
      logger.logAuthAttempt(email, false, req.ip, req.get('User-Agent'));
      throw new AuthenticationError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
    
    // التحقق من كلمة المرور
    const isPasswordValid = await user.verifyPassword(password);
    if (!isPasswordValid) {
      logger.logAuthAttempt(email, false, req.ip, req.get('User-Agent'));
      throw new AuthenticationError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
    
    // التحقق من حالة الحساب
    if (!user.is_active) {
      throw new AuthenticationError('الحساب غير مفعل');
    }
    
    if (user.is_banned) {
      throw new AuthenticationError('الحساب محظور');
    }
    
    // إنشاء الرموز المميزة
    const tokenExpiry = remember ? '30d' : '7d';
    const token = generateToken({ id: user.id, email: user.email }, tokenExpiry);
    const refreshToken = generateRefreshToken({ id: user.id });
    
    // تحديث آخر تسجيل دخول
    await user.update({ last_login_at: new Date() });
    
    // تسجيل النشاط
    logger.logAuthAttempt(email, true, req.ip, req.get('User-Agent'));
    
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        user: user.toPublicProfile(),
        token,
        refreshToken
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// تحديث الرمز المميز
// =====================================================
router.post('/refresh', async (req, res, next) => {
  try {
    // التحقق من صحة البيانات
    const { error, value } = refreshTokenSchema.validate(req.body);
    if (error) {
      throw new ValidationError(error.details[0].message);
    }
    
    const { refreshToken } = value;
    
    // التحقق من صحة رمز التحديث
    const decoded = verifyRefreshToken(refreshToken);
    
    // البحث عن المستخدم
    const user = await User.findById(decoded.id);
    if (!user) {
      throw new AuthenticationError('المستخدم غير موجود');
    }
    
    // إنشاء رمز جديد
    const newToken = generateToken({ id: user.id, email: user.email });
    const newRefreshToken = generateRefreshToken({ id: user.id });
    
    res.json({
      success: true,
      message: 'تم تحديث الرمز المميز بنجاح',
      data: {
        token: newToken,
        refreshToken: newRefreshToken
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// تسجيل الخروج
// =====================================================
router.post('/logout', verifyToken, async (req, res, next) => {
  try {
    // تسجيل النشاط
    logger.logUserActivity(req.user.id, 'logout', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// الحصول على بيانات المستخدم الحالي
// =====================================================
router.get('/me', verifyToken, async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      throw new AuthenticationError('المستخدم غير موجود');
    }
    
    res.json({
      success: true,
      data: {
        user: user.toJSON()
      }
    });
    
  } catch (error) {
    next(error);
  }
});

// =====================================================
// التحقق من صحة الرمز المميز
// =====================================================
router.get('/verify', verifyToken, async (req, res, next) => {
  try {
    res.json({
      success: true,
      message: 'الرمز المميز صحيح',
      data: {
        user: req.user
      }
    });
    
  } catch (error) {
    next(error);
  }
});

module.exports = router;
