#!/bin/bash

# =====================================================
# TikTok Clone Backup Script - سكريبت النسخ الاحتياطي
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="tiktok-clone"
BACKUP_DIR="/var/backups/tiktok-clone"
RETENTION_DAYS=30
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="${PROJECT_NAME}_backup_${TIMESTAMP}"

# Database Configuration
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"27017"}
DB_NAME=${DB_NAME:-"tiktok_clone"}
DB_USER=${DB_USER:-""}
DB_PASS=${DB_PASS:-""}

# Redis Configuration
REDIS_HOST=${REDIS_HOST:-"localhost"}
REDIS_PORT=${REDIS_PORT:-"6379"}
REDIS_PASS=${REDIS_PASS:-""}

# Storage Configuration
UPLOADS_DIR=${UPLOADS_DIR:-"/var/www/tiktok-clone/uploads"}
LOGS_DIR=${LOGS_DIR:-"/var/log/tiktok-clone"}

# Cloud Storage Configuration (Optional)
AWS_S3_BUCKET=${AWS_S3_BUCKET:-""}
AWS_REGION=${AWS_REGION:-"us-east-1"}
GOOGLE_CLOUD_BUCKET=${GOOGLE_CLOUD_BUCKET:-""}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking backup dependencies..."
    
    # Check mongodump
    if ! command -v mongodump &> /dev/null; then
        log_error "mongodump is not installed"
        exit 1
    fi
    
    # Check redis-cli
    if ! command -v redis-cli &> /dev/null; then
        log_warning "redis-cli is not installed, Redis backup will be skipped"
    fi
    
    # Check tar
    if ! command -v tar &> /dev/null; then
        log_error "tar is not installed"
        exit 1
    fi
    
    # Check gzip
    if ! command -v gzip &> /dev/null; then
        log_error "gzip is not installed"
        exit 1
    fi
    
    log_success "Dependencies check completed"
}

create_backup_directory() {
    log_info "Creating backup directory..."
    
    # Create main backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Create timestamped backup directory
    CURRENT_BACKUP_DIR="$BACKUP_DIR/$BACKUP_NAME"
    mkdir -p "$CURRENT_BACKUP_DIR"
    
    # Create subdirectories
    mkdir -p "$CURRENT_BACKUP_DIR/database"
    mkdir -p "$CURRENT_BACKUP_DIR/redis"
    mkdir -p "$CURRENT_BACKUP_DIR/uploads"
    mkdir -p "$CURRENT_BACKUP_DIR/logs"
    mkdir -p "$CURRENT_BACKUP_DIR/config"
    
    log_success "Backup directory created: $CURRENT_BACKUP_DIR"
}

backup_database() {
    log_info "Backing up MongoDB database..."
    
    local db_backup_dir="$CURRENT_BACKUP_DIR/database"
    
    # Build mongodump command
    local mongodump_cmd="mongodump --host $DB_HOST:$DB_PORT --db $DB_NAME --out $db_backup_dir"
    
    # Add authentication if provided
    if [ -n "$DB_USER" ] && [ -n "$DB_PASS" ]; then
        mongodump_cmd="$mongodump_cmd --username $DB_USER --password $DB_PASS --authenticationDatabase admin"
    fi
    
    # Execute backup
    if eval "$mongodump_cmd"; then
        log_success "Database backup completed"
        
        # Compress database backup
        cd "$db_backup_dir"
        tar -czf "../database_${TIMESTAMP}.tar.gz" .
        rm -rf "$DB_NAME"
        
        log_success "Database backup compressed"
    else
        log_error "Database backup failed"
        return 1
    fi
}

backup_redis() {
    if ! command -v redis-cli &> /dev/null; then
        log_warning "Skipping Redis backup (redis-cli not available)"
        return
    fi
    
    log_info "Backing up Redis data..."
    
    local redis_backup_dir="$CURRENT_BACKUP_DIR/redis"
    
    # Build redis-cli command
    local redis_cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
    
    # Add authentication if provided
    if [ -n "$REDIS_PASS" ]; then
        redis_cmd="$redis_cmd -a $REDIS_PASS"
    fi
    
    # Save Redis data
    if $redis_cmd BGSAVE; then
        log_info "Redis background save initiated"
        
        # Wait for background save to complete
        while [ "$($redis_cmd LASTSAVE)" = "$($redis_cmd LASTSAVE)" ]; do
            sleep 1
        done
        
        # Copy RDB file
        local redis_data_dir="/var/lib/redis"
        if [ -f "$redis_data_dir/dump.rdb" ]; then
            cp "$redis_data_dir/dump.rdb" "$redis_backup_dir/dump_${TIMESTAMP}.rdb"
            gzip "$redis_backup_dir/dump_${TIMESTAMP}.rdb"
            log_success "Redis backup completed"
        else
            log_warning "Redis RDB file not found"
        fi
    else
        log_error "Redis backup failed"
        return 1
    fi
}

backup_uploads() {
    log_info "Backing up uploaded files..."
    
    if [ ! -d "$UPLOADS_DIR" ]; then
        log_warning "Uploads directory not found: $UPLOADS_DIR"
        return
    fi
    
    local uploads_backup_file="$CURRENT_BACKUP_DIR/uploads_${TIMESTAMP}.tar.gz"
    
    # Create compressed archive of uploads
    if tar -czf "$uploads_backup_file" -C "$(dirname "$UPLOADS_DIR")" "$(basename "$UPLOADS_DIR")"; then
        log_success "Uploads backup completed"
        
        # Get backup size
        local backup_size=$(du -h "$uploads_backup_file" | cut -f1)
        log_info "Uploads backup size: $backup_size"
    else
        log_error "Uploads backup failed"
        return 1
    fi
}

backup_logs() {
    log_info "Backing up application logs..."
    
    if [ ! -d "$LOGS_DIR" ]; then
        log_warning "Logs directory not found: $LOGS_DIR"
        return
    fi
    
    local logs_backup_file="$CURRENT_BACKUP_DIR/logs_${TIMESTAMP}.tar.gz"
    
    # Create compressed archive of logs (last 7 days only)
    if find "$LOGS_DIR" -name "*.log" -mtime -7 -print0 | tar -czf "$logs_backup_file" --null -T -; then
        log_success "Logs backup completed"
        
        # Get backup size
        local backup_size=$(du -h "$logs_backup_file" | cut -f1)
        log_info "Logs backup size: $backup_size"
    else
        log_warning "Logs backup failed or no recent logs found"
    fi
}

backup_configuration() {
    log_info "Backing up configuration files..."
    
    local config_backup_dir="$CURRENT_BACKUP_DIR/config"
    
    # List of configuration files to backup
    local config_files=(
        "/etc/nginx/sites-available/tiktok-clone"
        "/etc/systemd/system/tiktok-clone.service"
        "$(pwd)/.env"
        "$(pwd)/docker-compose.yml"
        "$(pwd)/docker-compose.prod.yml"
    )
    
    # Copy configuration files
    for config_file in "${config_files[@]}"; do
        if [ -f "$config_file" ]; then
            cp "$config_file" "$config_backup_dir/" 2>/dev/null || true
            log_info "Backed up: $(basename "$config_file")"
        fi
    done
    
    # Compress configuration backup
    cd "$config_backup_dir"
    if [ "$(ls -A .)" ]; then
        tar -czf "../config_${TIMESTAMP}.tar.gz" .
        cd ..
        rm -rf config
        log_success "Configuration backup completed"
    else
        log_warning "No configuration files found to backup"
    fi
}

create_backup_manifest() {
    log_info "Creating backup manifest..."
    
    local manifest_file="$CURRENT_BACKUP_DIR/manifest.txt"
    
    cat > "$manifest_file" << EOF
TikTok Clone Backup Manifest
============================
Backup Name: $BACKUP_NAME
Timestamp: $TIMESTAMP
Date: $(date)
Hostname: $(hostname)
User: $(whoami)

Components Backed Up:
EOF
    
    # List backup components
    if [ -f "$CURRENT_BACKUP_DIR/database_${TIMESTAMP}.tar.gz" ]; then
        echo "- Database (MongoDB)" >> "$manifest_file"
        echo "  Size: $(du -h "$CURRENT_BACKUP_DIR/database_${TIMESTAMP}.tar.gz" | cut -f1)" >> "$manifest_file"
    fi
    
    if [ -f "$CURRENT_BACKUP_DIR/redis/dump_${TIMESTAMP}.rdb.gz" ]; then
        echo "- Redis Cache" >> "$manifest_file"
        echo "  Size: $(du -h "$CURRENT_BACKUP_DIR/redis/dump_${TIMESTAMP}.rdb.gz" | cut -f1)" >> "$manifest_file"
    fi
    
    if [ -f "$CURRENT_BACKUP_DIR/uploads_${TIMESTAMP}.tar.gz" ]; then
        echo "- Uploaded Files" >> "$manifest_file"
        echo "  Size: $(du -h "$CURRENT_BACKUP_DIR/uploads_${TIMESTAMP}.tar.gz" | cut -f1)" >> "$manifest_file"
    fi
    
    if [ -f "$CURRENT_BACKUP_DIR/logs_${TIMESTAMP}.tar.gz" ]; then
        echo "- Application Logs" >> "$manifest_file"
        echo "  Size: $(du -h "$CURRENT_BACKUP_DIR/logs_${TIMESTAMP}.tar.gz" | cut -f1)" >> "$manifest_file"
    fi
    
    if [ -f "$CURRENT_BACKUP_DIR/config_${TIMESTAMP}.tar.gz" ]; then
        echo "- Configuration Files" >> "$manifest_file"
        echo "  Size: $(du -h "$CURRENT_BACKUP_DIR/config_${TIMESTAMP}.tar.gz" | cut -f1)" >> "$manifest_file"
    fi
    
    echo "" >> "$manifest_file"
    echo "Total Backup Size: $(du -sh "$CURRENT_BACKUP_DIR" | cut -f1)" >> "$manifest_file"
    
    log_success "Backup manifest created"
}

compress_backup() {
    log_info "Compressing final backup..."
    
    cd "$BACKUP_DIR"
    
    # Create final compressed backup
    local final_backup_file="${BACKUP_NAME}.tar.gz"
    
    if tar -czf "$final_backup_file" "$BACKUP_NAME"; then
        # Remove uncompressed directory
        rm -rf "$BACKUP_NAME"
        
        local final_size=$(du -h "$final_backup_file" | cut -f1)
        log_success "Final backup created: $final_backup_file ($final_size)"
        
        # Update current backup path
        CURRENT_BACKUP_FILE="$BACKUP_DIR/$final_backup_file"
    else
        log_error "Failed to compress final backup"
        return 1
    fi
}

upload_to_cloud() {
    if [ -z "$AWS_S3_BUCKET" ] && [ -z "$GOOGLE_CLOUD_BUCKET" ]; then
        log_info "No cloud storage configured, skipping upload"
        return
    fi
    
    # Upload to AWS S3
    if [ -n "$AWS_S3_BUCKET" ] && command -v aws &> /dev/null; then
        log_info "Uploading backup to AWS S3..."
        
        if aws s3 cp "$CURRENT_BACKUP_FILE" "s3://$AWS_S3_BUCKET/backups/" --region "$AWS_REGION"; then
            log_success "Backup uploaded to S3 successfully"
        else
            log_error "Failed to upload backup to S3"
        fi
    fi
    
    # Upload to Google Cloud Storage
    if [ -n "$GOOGLE_CLOUD_BUCKET" ] && command -v gsutil &> /dev/null; then
        log_info "Uploading backup to Google Cloud Storage..."
        
        if gsutil cp "$CURRENT_BACKUP_FILE" "gs://$GOOGLE_CLOUD_BUCKET/backups/"; then
            log_success "Backup uploaded to Google Cloud Storage successfully"
        else
            log_error "Failed to upload backup to Google Cloud Storage"
        fi
    fi
}

cleanup_old_backups() {
    log_info "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    # Remove old local backups
    find "$BACKUP_DIR" -name "${PROJECT_NAME}_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
    
    local removed_count=$(find "$BACKUP_DIR" -name "${PROJECT_NAME}_backup_*.tar.gz" -mtime +$RETENTION_DAYS | wc -l)
    
    if [ "$removed_count" -gt 0 ]; then
        log_success "Removed $removed_count old backup(s)"
    else
        log_info "No old backups to remove"
    fi
    
    # Cleanup cloud storage (if configured)
    if [ -n "$AWS_S3_BUCKET" ] && command -v aws &> /dev/null; then
        log_info "Cleaning up old S3 backups..."
        # This would require more complex AWS CLI commands to list and delete old objects
        log_warning "S3 cleanup not implemented - please configure lifecycle policies"
    fi
}

verify_backup() {
    log_info "Verifying backup integrity..."
    
    if [ ! -f "$CURRENT_BACKUP_FILE" ]; then
        log_error "Backup file not found: $CURRENT_BACKUP_FILE"
        return 1
    fi
    
    # Test archive integrity
    if tar -tzf "$CURRENT_BACKUP_FILE" > /dev/null 2>&1; then
        log_success "Backup archive integrity verified"
    else
        log_error "Backup archive is corrupted"
        return 1
    fi
    
    # Check file size (should be > 0)
    local file_size=$(stat -f%z "$CURRENT_BACKUP_FILE" 2>/dev/null || stat -c%s "$CURRENT_BACKUP_FILE" 2>/dev/null)
    if [ "$file_size" -gt 0 ]; then
        log_success "Backup file size is valid: $(du -h "$CURRENT_BACKUP_FILE" | cut -f1)"
    else
        log_error "Backup file is empty"
        return 1
    fi
}

send_notification() {
    local status=$1
    local message=$2
    
    # Email notification (if configured)
    if [ -n "$NOTIFICATION_EMAIL" ] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "TikTok Clone Backup $status" "$NOTIFICATION_EMAIL"
    fi
    
    # Slack notification (if configured)
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        local color="good"
        if [ "$status" = "FAILED" ]; then
            color="danger"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"title\":\"TikTok Clone Backup $status\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK_URL" > /dev/null 2>&1
    fi
}

# Main backup process
main() {
    local start_time=$(date +%s)
    
    log_info "Starting TikTok Clone backup process..."
    log_info "Backup name: $BACKUP_NAME"
    
    # Check dependencies
    check_dependencies
    
    # Create backup directory
    create_backup_directory
    
    # Perform backups
    backup_database
    backup_redis
    backup_uploads
    backup_logs
    backup_configuration
    
    # Create manifest
    create_backup_manifest
    
    # Compress backup
    compress_backup
    
    # Verify backup
    verify_backup
    
    # Upload to cloud storage
    upload_to_cloud
    
    # Cleanup old backups
    cleanup_old_backups
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "🎉 Backup completed successfully!"
    log_info "Backup duration: ${duration} seconds"
    log_info "Backup location: $CURRENT_BACKUP_FILE"
    
    # Send success notification
    send_notification "SUCCESS" "Backup completed successfully in ${duration} seconds. Location: $CURRENT_BACKUP_FILE"
}

# Handle script arguments
case "${1:-backup}" in
    "backup")
        main
        ;;
    "verify")
        if [ -n "$2" ]; then
            CURRENT_BACKUP_FILE="$2"
            verify_backup
        else
            log_error "Please provide backup file path for verification"
            exit 1
        fi
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    *)
        echo "Usage: $0 [backup|verify|cleanup]"
        echo "  backup  - Perform full backup (default)"
        echo "  verify  - Verify backup integrity"
        echo "  cleanup - Remove old backups"
        echo ""
        echo "Examples:"
        echo "  $0 backup"
        echo "  $0 verify /var/backups/tiktok-clone/backup.tar.gz"
        echo "  $0 cleanup"
        exit 1
        ;;
esac
