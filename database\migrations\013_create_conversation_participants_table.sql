-- =====================================================
-- Migration: Create Conversation Participants Table
-- =====================================================

CREATE TABLE IF NOT EXISTS conversation_participants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    conversation_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    
    -- Role in conversation
    role ENUM('member', 'admin', 'owner') DEFAULT 'member',
    
    -- Permissions
    can_send_messages BOOLEAN DEFAULT TRUE,
    can_add_participants BOOLEAN DEFAULT FALSE,
    can_remove_participants BOOLEAN DEFAULT FALSE,
    can_edit_info BOOLEAN DEFAULT FALSE,
    
    -- Status
    status ENUM('active', 'left', 'removed', 'banned') DEFAULT 'active',
    
    -- Notifications
    notifications_enabled BOOLEAN DEFAULT TRUE,
    
    -- Read Status
    last_read_message_id BIGINT,
    last_read_at TIMESTAMP NULL,
    unread_count INT DEFAULT 0,
    
    -- Timestamps
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (last_read_message_id) REFERENCES messages(id) ON DELETE SET NULL,
    
    -- Constraints
    UNIQUE KEY unique_participant (conversation_id, user_id),
    
    -- Indexes
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_joined_at (joined_at),
    INDEX idx_unread_count (unread_count)
);
