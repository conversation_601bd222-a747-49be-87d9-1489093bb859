-- =====================================================
-- Migration: Create Reports Table
-- =====================================================

CREATE TABLE IF NOT EXISTS reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reporter_id BIGINT NOT NULL,
    
    -- Reported Entity
    reported_type ENUM('user', 'video', 'comment', 'hashtag') NOT NULL,
    reported_id BIGINT NOT NULL,
    reported_user_id BIGINT, -- user being reported
    
    -- Report Details
    reason ENUM(
        'spam', 'harassment', 'hate_speech', 'violence',
        'nudity', 'copyright', 'fake_news', 'scam',
        'underage', 'drugs', 'self_harm', 'other'
    ) NOT NULL,
    description TEXT,
    
    -- Evidence
    evidence_urls JSON, -- screenshots, links, etc.
    
    -- Status
    status ENUM('pending', 'reviewing', 'resolved', 'dismissed') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    
    -- Moderation
    moderator_id BIGINT,
    moderator_notes TEXT,
    action_taken ENUM(
        'none', 'warning', 'content_removed', 'account_suspended',
        'account_banned', 'content_age_restricted', 'other'
    ),
    
    -- AI Analysis
    ai_severity_score DECIMAL(3,2),
    ai_category VARCHAR(100),
    ai_confidence DECIMAL(3,2),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (moderator_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_reporter_id (reporter_id),
    INDEX idx_reported_type_id (reported_type, reported_id),
    INDEX idx_reported_user_id (reported_user_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_reason (reason),
    INDEX idx_created_at (created_at),
    INDEX idx_moderator_id (moderator_id)
);
