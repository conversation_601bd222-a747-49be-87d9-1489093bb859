// =====================================================
// نقطة البداية - TikTok Clone Web
// =====================================================

import React from 'react';
import { createRoot } from 'react-dom/client';
import { StrictMode } from 'react';

// App Component
import App from './App';

// Service Worker
import * as serviceWorkerRegistration from './serviceWorkerRegistration';

// Performance monitoring
import reportWebVitals from './reportWebVitals';

// Get root element
const container = document.getElementById('root');
const root = createRoot(container);

// Render app
root.render(
  <StrictMode>
    <App />
  </StrictMode>
);

// Register service worker for PWA functionality
serviceWorkerRegistration.register({
  onSuccess: (registration) => {
    console.log('✅ Service Worker registered successfully:', registration);
  },
  onUpdate: (registration) => {
    console.log('🔄 New content available, please refresh.');
    // You can show a notification to user here
    if (window.confirm('تحديث جديد متاح. هل تريد إعادة تحميل الصفحة؟')) {
      window.location.reload();
    }
  },
});

// Performance monitoring
reportWebVitals((metric) => {
  // Log performance metrics
  console.log('📊 Performance metric:', metric);
  
  // You can send to analytics service here
  // Example: gtag('event', metric.name, { value: metric.value });
});

// Global error handler
window.addEventListener('error', (event) => {
  console.error('❌ Global error:', event.error);
  // You can send error to monitoring service here
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ Unhandled promise rejection:', event.reason);
  // You can send error to monitoring service here
});

// Development helpers
if (process.env.NODE_ENV === 'development') {
  // Enable React DevTools
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('🔧 React DevTools detected');
  }
  
  // Log app info
  console.log(`
🚀 TikTok Clone Web App
📦 Version: ${process.env.REACT_APP_VERSION || '1.0.0'}
🌍 Environment: ${process.env.NODE_ENV}
🔗 API URL: ${process.env.REACT_APP_API_URL || 'http://localhost:5000'}
  `);
}
