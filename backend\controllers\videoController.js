// =====================================================
// Video Controller - متحكم الفيديو
// =====================================================

const Video = require('../models/Video');
const User = require('../models/User');
const Like = require('../models/Like');
const Hashtag = require('../models/Hashtag');
const { validationResult } = require('express-validator');
const { createResponse, createErrorResponse } = require('../utils/response');
const { uploadVideo, processVideo } = require('../utils/videoProcessor');
const { extractHashtags } = require('../utils/textProcessor');

class VideoController {
  // الحصول على الخلاصة
  async getFeed(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        category = null,
        following = false
      } = req.query;

      const skip = (page - 1) * limit;
      const userId = req.user?._id;

      let query = {
        privacy: 'public',
        isApproved: true,
        publishedAt: { $exists: true }
      };

      // إذا كان المستخدم يريد رؤية متابعيه فقط
      if (following && userId) {
        const user = await User.findById(userId);
        const followingUsers = await mongoose.model('Follow').find({
          follower: userId,
          status: 'accepted'
        }).select('following');
        
        const followingIds = followingUsers.map(f => f.following);
        query.user = { $in: followingIds };
      }

      if (category) {
        query.hashtags = { $in: [category] };
      }

      const videos = await Video.find(query)
        .sort({ 
          isFeatured: -1,
          isTrending: -1,
          createdAt: -1 
        })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('user', 'username fullName avatarUrl isVerified')
        .populate('music', 'title artist audioUrl')
        .lean();

      // إضافة معلومات الإعجاب للمستخدم الحالي
      if (userId) {
        for (let video of videos) {
          const isLiked = await Like.isLikedByUser(userId, video._id, 'Video');
          video.isLiked = !!isLiked;
        }
      }

      const total = await Video.countDocuments(query);

      res.json(
        createResponse('تم جلب الفيديوهات بنجاح', {
          videos,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Get feed error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // رفع فيديو جديد
  async uploadVideo(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          createErrorResponse('بيانات غير صحيحة', errors.array())
        );
      }

      const {
        title,
        description,
        hashtags,
        musicId,
        privacy = 'public',
        allowComments = true,
        allowDuet = true,
        allowStitch = true,
        allowDownload = false
      } = req.body;

      const userId = req.user._id;

      // التحقق من وجود الملف
      if (!req.files || !req.files.video) {
        return res.status(400).json(
          createErrorResponse('ملف الفيديو مطلوب')
        );
      }

      const videoFile = req.files.video;
      const thumbnailFile = req.files.thumbnail;

      // رفع ومعالجة الفيديو
      const uploadResult = await uploadVideo(videoFile, thumbnailFile);

      // استخراج الهاشتاغات
      const extractedHashtags = extractHashtags(description || '');
      const allHashtags = [
        ...extractedHashtags,
        ...(hashtags ? hashtags.split(',').map(tag => tag.trim()) : [])
      ];

      // إنشاء الفيديو
      const video = new Video({
        user: userId,
        title,
        description,
        videoUrl: uploadResult.videoUrl,
        thumbnailUrl: uploadResult.thumbnailUrl,
        duration: uploadResult.duration,
        fileSize: uploadResult.fileSize,
        resolution: uploadResult.resolution,
        format: uploadResult.format,
        hashtags: allHashtags,
        music: musicId || null,
        privacy,
        allowComments,
        allowDuet,
        allowStitch,
        allowDownload,
        processingStatus: 'processing'
      });

      await video.save();

      // معالجة الفيديو في الخلفية
      processVideo(video._id, uploadResult.originalPath);

      // تحديث عدد الفيديوهات للمستخدم
      await User.findByIdAndUpdate(userId, {
        $inc: { videosCount: 1 }
      });

      // إنشاء أو تحديث الهاشتاغات
      for (const hashtagName of allHashtags) {
        await Hashtag.findOrCreate(hashtagName);
      }

      res.status(201).json(
        createResponse('تم رفع الفيديو بنجاح', {
          video: await video.populate('user', 'username fullName avatarUrl isVerified')
        })
      );
    } catch (error) {
      console.error('Upload video error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في رفع الفيديو')
      );
    }
  }

  // الحصول على فيديو محدد
  async getVideo(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?._id;

      const video = await Video.findById(id)
        .populate('user', 'username fullName avatarUrl isVerified followersCount')
        .populate('music', 'title artist audioUrl');

      if (!video) {
        return res.status(404).json(
          createErrorResponse('الفيديو غير موجود')
        );
      }

      // التحقق من الخصوصية
      if (video.privacy === 'private' && (!userId || !video.user._id.equals(userId))) {
        return res.status(403).json(
          createErrorResponse('غير مسموح بالوصول لهذا الفيديو')
        );
      }

      // زيادة عدد المشاهدات
      await video.incrementViews();

      // التحقق من الإعجاب
      let isLiked = false;
      if (userId) {
        isLiked = !!(await Like.isLikedByUser(userId, video._id, 'Video'));
      }

      const videoData = video.toJSON();
      videoData.isLiked = isLiked;

      res.json(
        createResponse('تم جلب الفيديو بنجاح', {
          video: videoData
        })
      );
    } catch (error) {
      console.error('Get video error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // الإعجاب بالفيديو
  async likeVideo(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const video = await Video.findById(id);
      if (!video) {
        return res.status(404).json(
          createErrorResponse('الفيديو غير موجود')
        );
      }

      const result = await Like.toggleLike(userId, id, 'Video');

      res.json(
        createResponse(
          result.action === 'liked' ? 'تم الإعجاب بالفيديو' : 'تم إلغاء الإعجاب',
          {
            action: result.action,
            likesCount: video.likesCount + (result.action === 'liked' ? 1 : -1)
          }
        )
      );
    } catch (error) {
      console.error('Like video error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // مشاركة الفيديو
  async shareVideo(req, res) {
    try {
      const { id } = req.params;
      const { platform = 'internal' } = req.body;

      const video = await Video.findById(id);
      if (!video) {
        return res.status(404).json(
          createErrorResponse('الفيديو غير موجود')
        );
      }

      // زيادة عدد المشاركات
      await video.incrementShares();

      res.json(
        createResponse('تم مشاركة الفيديو بنجاح', {
          sharesCount: video.sharesCount + 1,
          shareUrl: `${process.env.FRONTEND_URL}/video/${id}`
        })
      );
    } catch (error) {
      console.error('Share video error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // حذف الفيديو
  async deleteVideo(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const video = await Video.findById(id);
      if (!video) {
        return res.status(404).json(
          createErrorResponse('الفيديو غير موجود')
        );
      }

      // التحقق من الملكية
      if (!video.user.equals(userId)) {
        return res.status(403).json(
          createErrorResponse('غير مسموح بحذف هذا الفيديو')
        );
      }

      await video.remove();

      // تحديث عدد الفيديوهات للمستخدم
      await User.findByIdAndUpdate(userId, {
        $inc: { videosCount: -1 }
      });

      res.json(
        createResponse('تم حذف الفيديو بنجاح')
      );
    } catch (error) {
      console.error('Delete video error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // البحث في الفيديوهات
  async searchVideos(req, res) {
    try {
      const {
        q: query,
        page = 1,
        limit = 20,
        sortBy = 'relevance'
      } = req.query;

      if (!query) {
        return res.status(400).json(
          createErrorResponse('نص البحث مطلوب')
        );
      }

      const videos = await Video.searchVideos(query, {
        page: parseInt(page),
        limit: parseInt(limit),
        sortBy
      });

      const total = await Video.countDocuments({
        $text: { $search: query },
        privacy: 'public',
        isApproved: true,
        publishedAt: { $exists: true }
      });

      res.json(
        createResponse('تم البحث بنجاح', {
          videos,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Search videos error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في البحث')
      );
    }
  }

  // الحصول على الفيديوهات الرائجة
  async getTrendingVideos(req, res) {
    try {
      const { limit = 20 } = req.query;

      const videos = await Video.getTrending(parseInt(limit));

      res.json(
        createResponse('تم جلب الفيديوهات الرائجة بنجاح', {
          videos
        })
      );
    } catch (error) {
      console.error('Get trending videos error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // الحصول على الفيديوهات المميزة
  async getFeaturedVideos(req, res) {
    try {
      const { limit = 10 } = req.query;

      const videos = await Video.getFeatured(parseInt(limit));

      res.json(
        createResponse('تم جلب الفيديوهات المميزة بنجاح', {
          videos
        })
      );
    } catch (error) {
      console.error('Get featured videos error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // الحصول على فيديوهات المستخدم
  async getUserVideos(req, res) {
    try {
      const { userId } = req.params;
      const {
        page = 1,
        limit = 20,
        privacy = 'public'
      } = req.query;

      const currentUserId = req.user?._id;
      const skip = (page - 1) * limit;

      let query = {
        user: userId,
        publishedAt: { $exists: true }
      };

      // إذا لم يكن المستخدم الحالي هو صاحب الفيديوهات، أظهر العامة فقط
      if (!currentUserId || !currentUserId.equals(userId)) {
        query.privacy = 'public';
        query.isApproved = true;
      } else if (privacy !== 'all') {
        query.privacy = privacy;
      }

      const videos = await Video.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('music', 'title artist');

      const total = await Video.countDocuments(query);

      res.json(
        createResponse('تم جلب فيديوهات المستخدم بنجاح', {
          videos,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Get user videos error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }
}

module.exports = new VideoController();
