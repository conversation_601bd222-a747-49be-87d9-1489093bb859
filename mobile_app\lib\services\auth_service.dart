// =====================================================
// خدمة المصادقة
// =====================================================

import 'package:get/get.dart';
import '../constants/app_constants.dart';
import 'storage_service.dart';
import 'api_service.dart';

class AuthService extends GetxService {
  static AuthService get instance => Get.find<AuthService>();
  
  final StorageService _storage = StorageService.instance;
  final ApiService _api = ApiService.instance;
  
  final RxBool isLoggedIn = false.obs;
  final RxBool isLoading = false.obs;
  final Rxn<Map<String, dynamic>> currentUser = Rxn<Map<String, dynamic>>();

  @override
  void onInit() {
    super.onInit();
    checkAuthStatus();
  }

  // التحقق من حالة المصادقة
  Future<void> checkAuthStatus() async {
    try {
      final token = _storage.getSecure<String>(AppConstants.userTokenKey);
      final userData = _storage.getObject(AppConstants.userDataKey);
      
      if (token != null && userData != null) {
        currentUser.value = userData;
        isLoggedIn.value = true;
        print('المستخدم مسجل دخول');
      } else {
        isLoggedIn.value = false;
        print('المستخدم غير مسجل دخول');
      }
    } catch (e) {
      print('خطأ في التحقق من حالة المصادقة: $e');
      isLoggedIn.value = false;
    }
  }

  // تسجيل الدخول
  Future<bool> login(String email, String password) async {
    try {
      isLoading.value = true;
      
      final response = await _api.post('/auth/login', data: {
        'email': email,
        'password': password,
      });
      
      if (response.statusCode == 200) {
        final data = response.data['data'];
        final token = data['token'];
        final refreshToken = data['refreshToken'];
        final user = data['user'];
        
        // حفظ البيانات
        await _storage.setSecure(AppConstants.userTokenKey, token);
        await _storage.setSecure(AppConstants.refreshTokenKey, refreshToken);
        await _storage.setObject(AppConstants.userDataKey, user);
        
        currentUser.value = user;
        isLoggedIn.value = true;
        
        return true;
      }
      
      return false;
    } catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    try {
      await _storage.clearUserData();
      currentUser.value = null;
      isLoggedIn.value = false;
      print('تم تسجيل الخروج');
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
    }
  }
}
