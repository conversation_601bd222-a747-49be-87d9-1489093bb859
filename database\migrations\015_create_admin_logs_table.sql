-- =====================================================
-- Migration: Create Admin Logs Table
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    admin_id BIGINT NOT NULL,
    
    -- Action Details
    action VARCHAR(100) NOT NULL, -- create, update, delete, approve, ban, etc.
    entity_type VARCHAR(50) NOT NULL, -- user, video, comment, etc.
    entity_id BIGINT,
    
    -- Description
    description TEXT,
    
    -- Changes Made
    old_values JSON, -- previous values
    new_values JSON, -- new values
    
    -- Request Details
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    -- Severity Level
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_entity_type_id (entity_type, entity_id),
    INDEX idx_created_at (created_at),
    INDEX idx_severity (severity),
    INDEX idx_ip_address (ip_address)
);
