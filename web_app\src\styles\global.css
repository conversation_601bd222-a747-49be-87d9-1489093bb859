/* =====================================================
   Global Styles - الأنماط العامة
   ===================================================== */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  font-family: 'Cairo', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Typography */
.gradient-text {
  background: linear-gradient(45deg, #FF0050, #25F4EE);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Utility Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #FF0050;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #FE2C55;
}

/* Selection */
::selection {
  background: #FF0050;
  color: white;
}

::-moz-selection {
  background: #FF0050;
  color: white;
}

/* Focus Styles */
*:focus {
  outline: 2px solid #FF0050;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    color: black !important;
    background: white !important;
  }
}

/* Responsive Utilities */
@media (max-width: 600px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 601px) {
  .show-mobile {
    display: none !important;
  }
}

/* Loading States */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .slide-in-left {
  animation: slideInRight 0.6s ease-out;
}

[dir="rtl"] .slide-in-right {
  animation: slideInLeft 0.6s ease-out;
}
