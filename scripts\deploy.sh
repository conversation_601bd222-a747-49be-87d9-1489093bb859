#!/bin/bash

# =====================================================
# TikTok Clone - Deployment Script
# =====================================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
ENVIRONMENT=${1:-staging}
VERSION=$(date +%Y%m%d_%H%M%S)

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Main deployment function
main() {
    print_info "🚀 بدء نشر TikTok Clone - البيئة: $ENVIRONMENT"
    
    # Validate environment
    validate_environment
    
    # Pre-deployment checks
    pre_deployment_checks
    
    # Build applications
    build_applications
    
    # Deploy based on environment
    case $ENVIRONMENT in
        "staging")
            deploy_staging
            ;;
        "production")
            deploy_production
            ;;
        "docker")
            deploy_docker
            ;;
        *)
            print_error "بيئة غير مدعومة: $ENVIRONMENT"
            print_info "البيئات المدعومة: staging, production, docker"
            exit 1
            ;;
    esac
    
    # Post-deployment tasks
    post_deployment_tasks
    
    print_status "🎉 تم النشر بنجاح!"
}

# Validate environment
validate_environment() {
    print_info "التحقق من البيئة..."
    
    # Check required tools
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js غير مثبت"
        exit 1
    fi
    
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm غير مثبت"
        exit 1
    fi
    
    print_status "البيئة صالحة"
}

# Pre-deployment checks
pre_deployment_checks() {
    print_info "فحوصات ما قبل النشر..."
    
    # Check if all tests pass
    print_info "تشغيل الاختبارات..."
    
    # Backend tests
    cd backend
    npm test
    cd ..
    
    # Frontend tests
    cd web_app
    npm test -- --watchAll=false
    cd ..
    
    cd admin_panel
    npm test -- --watchAll=false
    cd ..
    
    # Flutter tests (if available)
    if command -v flutter >/dev/null 2>&1; then
        cd mobile_app
        flutter test
        cd ..
    fi
    
    print_status "جميع الاختبارات نجحت"
}

# Build applications
build_applications() {
    print_info "بناء التطبيقات..."
    
    # Build web app
    print_info "بناء Web App..."
    cd web_app
    npm run build
    cd ..
    
    # Build admin panel
    print_info "بناء Admin Panel..."
    cd admin_panel
    npm run build
    cd ..
    
    # Build Flutter app (if available)
    if command -v flutter >/dev/null 2>&1; then
        print_info "بناء Flutter App..."
        cd mobile_app
        flutter build apk --release
        flutter build ios --release || print_warning "iOS build failed (requires macOS)"
        cd ..
    fi
    
    print_status "تم بناء جميع التطبيقات"
}

# Deploy to staging
deploy_staging() {
    print_info "نشر على بيئة التجريب..."
    
    # Create staging directory
    STAGING_DIR="/var/www/tiktok-clone-staging"
    sudo mkdir -p $STAGING_DIR
    
    # Deploy backend
    print_info "نشر Backend..."
    sudo rsync -av --delete backend/ $STAGING_DIR/backend/
    
    # Deploy web app
    print_info "نشر Web App..."
    sudo rsync -av --delete web_app/build/ $STAGING_DIR/web/
    
    # Deploy admin panel
    print_info "نشر Admin Panel..."
    sudo rsync -av --delete admin_panel/build/ $STAGING_DIR/admin/
    
    # Restart services
    sudo systemctl restart tiktok-backend-staging || print_warning "Failed to restart backend service"
    sudo systemctl restart nginx || print_warning "Failed to restart nginx"
    
    print_status "تم النشر على بيئة التجريب"
}

# Deploy to production
deploy_production() {
    print_info "نشر على بيئة الإنتاج..."
    
    # Confirmation prompt
    read -p "هل أنت متأكد من النشر على الإنتاج؟ (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "تم إلغاء النشر"
        exit 0
    fi
    
    # Create backup
    print_info "إنشاء نسخة احتياطية..."
    BACKUP_DIR="/var/backups/tiktok-clone-$(date +%Y%m%d_%H%M%S)"
    sudo mkdir -p $BACKUP_DIR
    sudo cp -r /var/www/tiktok-clone $BACKUP_DIR/ || print_warning "Backup failed"
    
    # Deploy to production
    PROD_DIR="/var/www/tiktok-clone"
    
    # Deploy backend
    print_info "نشر Backend..."
    sudo rsync -av --delete backend/ $PROD_DIR/backend/
    
    # Deploy web app
    print_info "نشر Web App..."
    sudo rsync -av --delete web_app/build/ $PROD_DIR/web/
    
    # Deploy admin panel
    print_info "نشر Admin Panel..."
    sudo rsync -av --delete admin_panel/build/ $PROD_DIR/admin/
    
    # Restart services
    sudo systemctl restart tiktok-backend
    sudo systemctl restart nginx
    
    print_status "تم النشر على بيئة الإنتاج"
}

# Deploy with Docker
deploy_docker() {
    print_info "نشر باستخدام Docker..."
    
    # Build Docker images
    print_info "بناء Docker images..."
    docker-compose build
    
    # Deploy with Docker Compose
    print_info "تشغيل الخدمات..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_info "انتظار جاهزية الخدمات..."
    sleep 30
    
    # Health check
    if curl -f http://localhost:5000/health >/dev/null 2>&1; then
        print_status "Backend service is healthy"
    else
        print_warning "Backend service health check failed"
    fi
    
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        print_status "Web app is accessible"
    else
        print_warning "Web app health check failed"
    fi
    
    print_status "تم النشر باستخدام Docker"
}

# Post-deployment tasks
post_deployment_tasks() {
    print_info "مهام ما بعد النشر..."
    
    # Clear caches
    print_info "مسح الذاكرة المؤقتة..."
    
    # Database migrations (if any)
    print_info "تشغيل migrations..."
    
    # Warm up caches
    print_info "تسخين الذاكرة المؤقتة..."
    
    # Send deployment notification
    print_info "إرسال إشعار النشر..."
    
    print_status "تم إكمال مهام ما بعد النشر"
}

# Show usage
usage() {
    echo "الاستخدام: $0 [staging|production|docker]"
    echo ""
    echo "البيئات:"
    echo "  staging     - نشر على بيئة التجريب"
    echo "  production  - نشر على بيئة الإنتاج"
    echo "  docker      - نشر باستخدام Docker"
    echo ""
    echo "مثال:"
    echo "  $0 staging"
    echo "  $0 production"
    echo "  $0 docker"
}

# Check arguments
if [ $# -eq 0 ]; then
    usage
    exit 1
fi

# Run main function
main "$@"
