// =====================================================
// بذور البيانات الأولية للميزات
// =====================================================

const fs = require('fs').promises;
const path = require('path');
const { query } = require('../config/database');

const seeder = {
  name: '001_features_seeder',
  description: 'إدخال البيانات الأولية لجدول الميزات',
  
  // تنفيذ البذور
  run: async () => {
    try {
      console.log('🌱 بدء إدخال البيانات الأولية للميزات...');
      
      // قراءة ملف SQL الخاص بالميزات
      const seedPath = path.join(__dirname, '../seeds/features_seed.sql');
      const seedSql = await fs.readFile(seedPath, 'utf8');
      
      // تنفيذ استعلام الإدخال
      await query(seedSql);
      
      // التحقق من عدد الميزات المدخلة
      const [result] = await query('SELECT COUNT(*) as count FROM features');
      const featuresCount = result.count;
      
      console.log(`✅ تم إدخال ${featuresCount} ميزة بنجاح`);
      
      // عرض قائمة بالميزات المفعلة
      const activeFeatures = await query(
        'SELECT feature_key, name_ar, status FROM features WHERE status = 1 ORDER BY id'
      );
      
      console.log('\n📋 الميزات المفعلة:');
      activeFeatures.forEach((feature, index) => {
        console.log(`${index + 1}. ${feature.name_ar} (${feature.feature_key})`);
      });
      
      return true;
      
    } catch (error) {
      console.error('❌ خطأ في إدخال البيانات الأولية للميزات:', error);
      throw error;
    }
  },
  
  // حذف البيانات
  rollback: async () => {
    try {
      console.log('🔄 بدء حذف البيانات الأولية للميزات...');
      
      await query('DELETE FROM features');
      await query('ALTER TABLE features AUTO_INCREMENT = 1');
      
      console.log('✅ تم حذف جميع الميزات بنجاح');
      return true;
      
    } catch (error) {
      console.error('❌ خطأ في حذف البيانات الأولية للميزات:', error);
      throw error;
    }
  }
};

module.exports = seeder;
