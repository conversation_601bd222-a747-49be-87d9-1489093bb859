// =====================================================
// شاشة البداية
// =====================================================

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../constants/app_constants.dart';
import '../../themes/app_theme.dart';
import '../../services/auth_service.dart';
import '../../services/feature_service.dart';
import '../auth/login_screen.dart';
import '../home/<USER>';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;
  late Animation<Offset> _slideAnimation;

  final AuthService _authService = Get.find<AuthService>();
  final FeatureService _featureService = Get.find<FeatureService>();

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initializeApp();
  }

  void _initAnimations() {
    // تحكم الشعار
    _logoController = AnimationController(
      duration: AppConstants.longAnimationDuration,
      vsync: this,
    );

    // تحكم النص
    _textController = AnimationController(
      duration: AppConstants.mediumAnimationDuration,
      vsync: this,
    );

    // رسوم متحركة للشعار
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // رسوم متحركة للنص
    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    // رسوم متحركة للانزلاق
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // بدء الرسوم المتحركة
    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _textController.forward();
    });
  }

  Future<void> _initializeApp() async {
    try {
      // تحميل الميزات
      await _featureService.loadFeatures();
      
      // التحقق من حالة المصادقة
      await _authService.checkAuthStatus();
      
      // انتظار انتهاء الرسوم المتحركة
      await Future.delayed(const Duration(seconds: 3));
      
      // التنقل للشاشة المناسبة
      _navigateToNextScreen();
      
    } catch (e) {
      print('خطأ في تهيئة التطبيق: $e');
      // في حالة الخطأ، انتقل لشاشة تسجيل الدخول
      await Future.delayed(const Duration(seconds: 2));
      Get.offAll(() => LoginScreen());
    }
  }

  void _navigateToNextScreen() {
    if (_authService.isLoggedIn.value) {
      // المستخدم مسجل دخول، انتقل للرئيسية
      Get.offAll(() => HomeScreen());
    } else {
      // المستخدم غير مسجل دخول، انتقل لتسجيل الدخول
      Get.offAll(() => LoginScreen());
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // المساحة العلوية
              const Spacer(flex: 2),
              
              // الشعار والعنوان
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // الشعار المتحرك
                    AnimatedBuilder(
                      animation: _logoAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.play_arrow_rounded,
                              size: 60,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // عنوان التطبيق
                    SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _textAnimation,
                        child: Column(
                          children: [
                            Text(
                              AppConstants.appName,
                              style: const TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 1.2,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'تطبيق ثوري مع 48+ ميزة مبتكرة',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white.withOpacity(0.9),
                                fontWeight: FontWeight.w300,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // مؤشر التحميل والنسخة
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // مؤشر التحميل
                    const SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 3,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // نص التحميل
                    Text(
                      'جاري تحميل الميزات الثورية...',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                      ),
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // معلومات النسخة
                    Text(
                      'الإصدار ${AppConstants.appVersion}',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.6),
                        fontSize: 12,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // حقوق الطبع
                    Text(
                      'تم تطويره بواسطة Augment Agent',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.6),
                        fontSize: 10,
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
