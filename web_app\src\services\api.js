// =====================================================
// API Service - خدمة API
// =====================================================

import axios from 'axios';
import { store } from '../store/store';
import { logout } from '../store/slices/authSlice';

// Base configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const state = store.getState();
    const token = state.auth.token;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    console.log('🚀 API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log('✅ API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('❌ Response Error:', error.response?.status, error.config?.url);
    
    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      store.dispatch(logout());
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// ==================== Auth API ====================
export const authAPI = {
  // تسجيل الدخول
  login: (credentials) => api.post('/auth/login', credentials),
  
  // تسجيل حساب جديد
  register: (userData) => api.post('/auth/register', userData),
  
  // تسجيل الخروج
  logout: () => api.post('/auth/logout'),
  
  // تحديث الرمز المميز
  refreshToken: () => api.post('/auth/refresh'),
  
  // إعادة تعيين كلمة المرور
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  
  // تأكيد إعادة تعيين كلمة المرور
  resetPassword: (token, password) => api.post('/auth/reset-password', { token, password }),
  
  // تأكيد البريد الإلكتروني
  verifyEmail: (token) => api.post('/auth/verify-email', { token }),
  
  // إرسال رمز التحقق
  sendVerificationCode: () => api.post('/auth/send-verification'),
  
  // تفعيل المصادقة الثنائية
  enable2FA: () => api.post('/auth/2fa/enable'),
  
  // تعطيل المصادقة الثنائية
  disable2FA: (code) => api.post('/auth/2fa/disable', { code }),
  
  // التحقق من رمز المصادقة الثنائية
  verify2FA: (code) => api.post('/auth/2fa/verify', { code }),
};

// ==================== Video API ====================
export const videoAPI = {
  // الحصول على الفيديوهات
  getVideos: (params = {}) => api.get('/videos', { params }),
  
  // الحصول على فيديو محدد
  getVideoById: (id) => api.get(`/videos/${id}`),
  
  // رفع فيديو جديد
  uploadVideo: (formData) => {
    return api.post('/videos/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        store.dispatch({ type: 'video/setUploadProgress', payload: progress });
      },
    });
  },
  
  // تحديث فيديو
  updateVideo: (id, data) => api.put(`/videos/${id}`, data),
  
  // حذف فيديو
  deleteVideo: (id) => api.delete(`/videos/${id}`),
  
  // الإعجاب بالفيديو
  toggleLike: (id) => api.post(`/videos/${id}/like`),
  
  // مشاركة الفيديو
  shareVideo: (id) => api.post(`/videos/${id}/share`),
  
  // الإبلاغ عن الفيديو
  reportVideo: (id, data) => api.post(`/videos/${id}/report`, data),
  
  // إضافة للمفضلة
  addToFavorites: (id) => api.post(`/videos/${id}/favorite`),
  
  // إزالة من المفضلة
  removeFromFavorites: (id) => api.delete(`/videos/${id}/favorite`),
  
  // تحديث تقدم المشاهدة
  updateProgress: (id, data) => api.post(`/videos/${id}/progress`, data),
  
  // البحث في الفيديوهات
  searchVideos: (query, params = {}) => api.get('/videos/search', { 
    params: { q: query, ...params } 
  }),
  
  // الحصول على الفيديوهات الشائعة
  getTrendingVideos: (params = {}) => api.get('/videos/trending', { params }),
  
  // الحصول على الفيديوهات المقترحة
  getRecommendedVideos: (params = {}) => api.get('/videos/recommended', { params }),
  
  // الحصول على فيديوهات المستخدم
  getUserVideos: (userId, params = {}) => api.get(`/videos/user/${userId}`, { params }),
  
  // الحصول على الفيديوهات المعجب بها
  getLikedVideos: (params = {}) => api.get('/videos/liked', { params }),
  
  // الحصول على سجل المشاهدة
  getWatchHistory: (params = {}) => api.get('/videos/history', { params }),
};

// ==================== Comment API ====================
export const commentAPI = {
  // الحصول على التعليقات
  getComments: (videoId, params = {}) => api.get(`/comments/video/${videoId}`, { params }),
  
  // إضافة تعليق
  addComment: (videoId, data) => api.post(`/comments/video/${videoId}`, data),
  
  // تحديث تعليق
  updateComment: (id, data) => api.put(`/comments/${id}`, data),
  
  // حذف تعليق
  deleteComment: (id) => api.delete(`/comments/${id}`),
  
  // الإعجاب بالتعليق
  toggleCommentLike: (id) => api.post(`/comments/${id}/like`),
  
  // الحصول على ردود التعليق
  getCommentReplies: (id, params = {}) => api.get(`/comments/${id}/replies`, { params }),
  
  // تثبيت التعليق
  pinComment: (id) => api.post(`/comments/${id}/pin`),
  
  // إخفاء التعليق
  hideComment: (id) => api.post(`/comments/${id}/hide`),
  
  // الإبلاغ عن التعليق
  reportComment: (id, data) => api.post(`/comments/${id}/report`, data),
  
  // البحث في التعليقات
  searchComments: (query, params = {}) => api.get('/comments/search', { 
    params: { q: query, ...params } 
  }),
};

// ==================== User API ====================
export const userAPI = {
  // الحصول على ملف المستخدم
  getUserProfile: (identifier) => api.get(`/users/${identifier}`),
  
  // تحديث الملف الشخصي
  updateProfile: (data) => {
    const formData = new FormData();
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key]);
      }
    });
    
    return api.put('/users/profile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // متابعة مستخدم
  followUser: (id) => api.post(`/users/${id}/follow`),
  
  // إلغاء متابعة مستخدم
  unfollowUser: (id) => api.delete(`/users/${id}/follow`),
  
  // الحصول على المتابعين
  getFollowers: (id, params = {}) => api.get(`/users/${id}/followers`, { params }),
  
  // الحصول على المتابَعين
  getFollowing: (id, params = {}) => api.get(`/users/${id}/following`, { params }),
  
  // البحث عن المستخدمين
  searchUsers: (query, params = {}) => api.get('/users/search/users', { 
    params: { q: query, ...params } 
  }),
  
  // اقتراحات المتابعة
  getFollowSuggestions: (params = {}) => api.get('/users/suggestions/follow', { params }),
  
  // طلبات المتابعة المعلقة
  getPendingFollowRequests: (params = {}) => api.get('/users/follow-requests/pending', { params }),
  
  // قبول طلب المتابعة
  acceptFollowRequest: (id) => api.post(`/users/follow-requests/${id}/accept`),
  
  // رفض طلب المتابعة
  rejectFollowRequest: (id) => api.post(`/users/follow-requests/${id}/reject`),
  
  // حظر مستخدم
  blockUser: (id) => api.post(`/users/${id}/block`),
  
  // إلغاء حظر مستخدم
  unblockUser: (id) => api.delete(`/users/${id}/block`),
  
  // الحصول على المستخدمين المحظورين
  getBlockedUsers: (params = {}) => api.get('/users/blocked/users', { params }),
  
  // تحديث إعدادات الخصوصية
  updatePrivacySettings: (data) => api.put('/users/privacy/settings', data),
  
  // تحديث إعدادات الإشعارات
  updateNotificationSettings: (data) => api.put('/users/notifications/settings', data),
  
  // الحصول على إحصائيات المستخدم
  getUserStats: (id) => api.get(`/users/${id}/stats`),
  
  // الحصول على نشاط المستخدم
  getUserActivity: (id, params = {}) => api.get(`/users/${id}/activity`, { params }),
};

// ==================== Notification API ====================
export const notificationAPI = {
  // الحصول على الإشعارات
  getNotifications: (params = {}) => api.get('/notifications', { params }),
  
  // تحديد إشعار كمقروء
  markAsRead: (id) => api.put(`/notifications/${id}/read`),
  
  // تحديد جميع الإشعارات كمقروءة
  markAllAsRead: () => api.put('/notifications/read-all'),
  
  // حذف إشعار
  deleteNotification: (id) => api.delete(`/notifications/${id}`),
  
  // مسح جميع الإشعارات
  clearAllNotifications: () => api.delete('/notifications'),
  
  // الحصول على عدد الإشعارات غير المقروءة
  getUnreadCount: () => api.get('/notifications/unread-count'),
  
  // تحديث رمز FCM
  updateFCMToken: (token) => api.post('/notifications/fcm-token', { token }),
  
  // الاشتراك في موضوع
  subscribeToTopic: (topic) => api.post('/notifications/subscribe', { topic }),
  
  // إلغاء الاشتراك في موضوع
  unsubscribeFromTopic: (topic) => api.post('/notifications/unsubscribe', { topic }),
};

// ==================== Music API ====================
export const musicAPI = {
  // الحصول على الموسيقى
  getMusic: (params = {}) => api.get('/music', { params }),
  
  // البحث في الموسيقى
  searchMusic: (query, params = {}) => api.get('/music/search', { 
    params: { q: query, ...params } 
  }),
  
  // الحصول على الموسيقى الشائعة
  getTrendingMusic: (params = {}) => api.get('/music/trending', { params }),
  
  // رفع موسيقى جديدة
  uploadMusic: (formData) => api.post('/music/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  
  // الحصول على تفاصيل الموسيقى
  getMusicById: (id) => api.get(`/music/${id}`),
};

// ==================== Analytics API ====================
export const analyticsAPI = {
  // إحصائيات عامة
  getGeneralStats: () => api.get('/analytics/general'),
  
  // إحصائيات الفيديوهات
  getVideoStats: (id) => api.get(`/analytics/videos/${id}`),
  
  // إحصائيات المستخدم
  getUserAnalytics: (params = {}) => api.get('/analytics/user', { params }),
  
  // تتبع الأحداث
  trackEvent: (data) => api.post('/analytics/events', data),
  
  // إحصائيات الأداء
  getPerformanceStats: (params = {}) => api.get('/analytics/performance', { params }),
};

// ==================== Admin API ====================
export const adminAPI = {
  // إحصائيات لوحة التحكم
  getDashboardStats: () => api.get('/admin/dashboard/stats'),
  
  // إدارة المستخدمين
  getUsers: (params = {}) => api.get('/admin/users', { params }),
  updateUser: (id, data) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),
  banUser: (id, data) => api.post(`/admin/users/${id}/ban`, data),
  unbanUser: (id) => api.post(`/admin/users/${id}/unban`),
  
  // إدارة الفيديوهات
  getVideosForReview: (params = {}) => api.get('/admin/videos', { params }),
  approveVideo: (id) => api.post(`/admin/videos/${id}/approve`),
  rejectVideo: (id, data) => api.post(`/admin/videos/${id}/reject`, data),
  
  // إدارة التقارير
  getReports: (params = {}) => api.get('/admin/reports', { params }),
  resolveReport: (id, data) => api.post(`/admin/reports/${id}/resolve`, data),
  
  // إدارة الميزات
  getFeatures: () => api.get('/admin/features'),
  updateFeature: (id, data) => api.put(`/admin/features/${id}`, data),
  
  // إعدادات النظام
  getSystemSettings: () => api.get('/admin/settings'),
  updateSystemSettings: (data) => api.put('/admin/settings', data),
};

// Export default api instance
export default api;
