<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#FF0050" />
    <meta name="description" content="TikTok Clone - تطبيق ثوري مع 48+ ميزة مبتكرة" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="TikTok Clone - تطبيق ثوري" />
    <meta property="og:description" content="اكتشف عالم الفيديوهات القصيرة مع ميزات ثورية مبتكرة" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.jpg" />
    <meta property="og:url" content="https://tiktokclone.com" />
    <meta property="og:type" content="website" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="TikTok Clone - تطبيق ثوري" />
    <meta name="twitter:description" content="اكتشف عالم الفيديوهات القصيرة مع ميزات ثورية مبتكرة" />
    <meta name="twitter:image" content="%PUBLIC_URL%/twitter-image.jpg" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- CSS Variables -->
    <style>
      :root {
        --primary-color: #FF0050;
        --secondary-color: #25F4EE;
        --accent-color: #FE2C55;
        --text-primary: #161823;
        --text-secondary: #69707D;
        --text-light: #A1A2A7;
        --background-light: #FFFFFF;
        --background-dark: #000000;
        --surface-light: #F8F8F8;
        --surface-dark: #161823;
        --border-color: #E1E2E3;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --success-color: #00D9FF;
        --warning-color: #FFB800;
        --error-color: #FF3040;
        --info-color: #007AFF;
      }
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Cairo', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: var(--background-light);
        color: var(--text-primary);
        line-height: 1.6;
        overflow-x: hidden;
      }
      
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading Spinner */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        background: white;
        border-radius: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Scrollbar Styling */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: var(--surface-light);
      }
      
      ::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: var(--accent-color);
      }
      
      /* Dark mode */
      @media (prefers-color-scheme: dark) {
        :root {
          --text-primary: #FFFFFF;
          --text-secondary: #A1A2A7;
          --background-light: #000000;
          --surface-light: #161823;
          --border-color: #2F2F2F;
        }
        
        body {
          background-color: var(--background-dark);
          color: var(--text-primary);
        }
      }
      
      /* Responsive Design */
      @media (max-width: 768px) {
        body {
          font-size: 14px;
        }
      }
      
      /* Print Styles */
      @media print {
        .no-print {
          display: none !important;
        }
      }
    </style>
    
    <title>TikTok Clone - تطبيق ثوري</title>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h2>يتطلب JavaScript</h2>
        <p>يحتاج هذا التطبيق إلى JavaScript ليعمل بشكل صحيح.</p>
        <p>يرجى تفعيل JavaScript في متصفحك والمحاولة مرة أخرى.</p>
      </div>
    </noscript>
    
    <!-- Loading Screen -->
    <div id="initial-loading" class="loading-container">
      <div class="loading-logo">
        <span style="font-size: 40px; color: var(--primary-color);">▶</span>
      </div>
      <div class="loading-text">TikTok Clone</div>
      <div class="loading-text" style="font-size: 14px; opacity: 0.8;">جاري تحميل التطبيق...</div>
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <!-- Remove loading screen when React loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingElement = document.getElementById('initial-loading');
          if (loadingElement) {
            loadingElement.style.opacity = '0';
            loadingElement.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingElement.remove();
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
