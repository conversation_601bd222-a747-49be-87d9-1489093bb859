// =====================================================
// خادم TikTok Clone الرئيسي
// =====================================================

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// استيراد الوحدات المحلية
const { createPool, testConnection } = require('../database/config/database');
const logger = require('./src/utils/logger');
const errorHandler = require('./src/middleware/errorHandler');
const authMiddleware = require('./src/middleware/auth');

// إنشاء التطبيق
const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// إعدادات البيئة
const PORT = process.env.PORT || 5000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// =====================================================
// Middleware العام
// =====================================================

// الأمان
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "https:"],
      frameSrc: ["'none'"],
    },
  },
}));

// CORS
app.use(cors({
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001', 
      'http://localhost:3002',
      process.env.FRONTEND_URL,
      process.env.ADMIN_URL,
      process.env.WEB_URL
    ].filter(Boolean);
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('غير مسموح بواسطة CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// ضغط الاستجابات
app.use(compression());

// تسجيل الطلبات
if (NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
}

// تحليل البيانات
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cookieParser());

// تحديد معدل الطلبات
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: NODE_ENV === 'development' ? 1000 : 100, // عدد الطلبات المسموحة
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// ملفات ثابتة
app.use('/uploads', express.static('uploads'));
app.use('/public', express.static('public'));

// =====================================================
// المسارات (Routes)
// =====================================================

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في TikTok Clone API 🚀',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: NODE_ENV,
    features: {
      authentication: true,
      fileUpload: true,
      realTime: true,
      ai: true,
      payments: true
    }
  });
});

// فحص صحة الخادم
app.get('/health', async (req, res) => {
  try {
    const dbStatus = await testConnection();
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbStatus ? 'connected' : 'disconnected',
      memory: process.memoryUsage(),
      version: process.version
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});

// مسارات API
app.use('/api/auth', require('./src/routes/auth'));
app.use('/api/users', require('./src/routes/users'));
app.use('/api/videos', require('./src/routes/videos'));
app.use('/api/features', require('./src/routes/features'));
app.use('/api/admin', require('./src/routes/admin'));
app.use('/api/live', require('./src/routes/live'));
app.use('/api/messages', require('./src/routes/messages'));
app.use('/api/payments', require('./src/routes/payments'));
app.use('/api/ai', require('./src/routes/ai'));

// مسار غير موجود
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'المسار غير موجود',
    message: `المسار ${req.originalUrl} غير موجود`,
    availableEndpoints: [
      '/api/auth',
      '/api/users', 
      '/api/videos',
      '/api/features',
      '/api/admin',
      '/api/live',
      '/api/messages',
      '/api/payments',
      '/api/ai'
    ]
  });
});

// معالج الأخطاء
app.use(errorHandler);

// =====================================================
// Socket.IO للتفاعل المباشر
// =====================================================

io.on('connection', (socket) => {
  console.log(`👤 مستخدم متصل: ${socket.id}`);
  
  // انضمام لغرفة المستخدم
  socket.on('join_user_room', (userId) => {
    socket.join(`user_${userId}`);
    console.log(`👤 المستخدم ${userId} انضم لغرفته`);
  });
  
  // انضمام لغرفة البث المباشر
  socket.on('join_live_room', (liveId) => {
    socket.join(`live_${liveId}`);
    socket.to(`live_${liveId}`).emit('user_joined', socket.id);
    console.log(`📺 مستخدم انضم للبث المباشر: ${liveId}`);
  });
  
  // مغادرة غرفة البث المباشر
  socket.on('leave_live_room', (liveId) => {
    socket.leave(`live_${liveId}`);
    socket.to(`live_${liveId}`).emit('user_left', socket.id);
    console.log(`📺 مستخدم غادر البث المباشر: ${liveId}`);
  });
  
  // رسالة في البث المباشر
  socket.on('live_message', (data) => {
    socket.to(`live_${data.liveId}`).emit('live_message', data);
  });
  
  // إعجاب في البث المباشر
  socket.on('live_like', (data) => {
    socket.to(`live_${data.liveId}`).emit('live_like', data);
  });
  
  // قطع الاتصال
  socket.on('disconnect', () => {
    console.log(`👤 مستخدم قطع الاتصال: ${socket.id}`);
  });
});

// =====================================================
// بدء تشغيل الخادم
// =====================================================

async function startServer() {
  try {
    // إنشاء pool قاعدة البيانات
    createPool();
    
    // اختبار الاتصال بقاعدة البيانات
    const dbConnected = await testConnection();
    if (!dbConnected) {
      throw new Error('فشل الاتصال بقاعدة البيانات');
    }
    
    // بدء تشغيل الخادم
    server.listen(PORT, () => {
      console.log(`
🚀 خادم TikTok Clone يعمل الآن!
📍 المنفذ: ${PORT}
🌍 البيئة: ${NODE_ENV}
🔗 الرابط: http://localhost:${PORT}
📊 لوحة المراقبة: http://localhost:${PORT}/health
📚 التوثيق: http://localhost:${PORT}/api/docs
      `);
      
      logger.info(`خادم TikTok Clone بدأ على المنفذ ${PORT}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في بدء تشغيل الخادم:', error);
    logger.error('خطأ في بدء تشغيل الخادم', error);
    process.exit(1);
  }
}

// معالجة الإغلاق الآمن
process.on('SIGTERM', () => {
  console.log('🛑 تم استلام إشارة SIGTERM، إغلاق الخادم...');
  server.close(() => {
    console.log('✅ تم إغلاق الخادم بنجاح');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 تم استلام إشارة SIGINT، إغلاق الخادم...');
  server.close(() => {
    console.log('✅ تم إغلاق الخادم بنجاح');
    process.exit(0);
  });
});

// بدء الخادم
startServer();

module.exports = { app, server, io };
