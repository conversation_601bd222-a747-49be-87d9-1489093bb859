// =====================================================
// User Slice - إدارة حالة المستخدمين
// =====================================================

import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  users: [],
  currentProfile: null,
  followers: [],
  following: [],
  suggestions: [],
  isLoading: false,
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUsers: (state, action) => {
      state.users = action.payload;
    },
    addUser: (state, action) => {
      state.users.push(action.payload);
    },
    updateUser: (state, action) => {
      const index = state.users.findIndex(user => user.id === action.payload.id);
      if (index !== -1) {
        state.users[index] = { ...state.users[index], ...action.payload };
      }
    },
    setCurrentProfile: (state, action) => {
      state.currentProfile = action.payload;
    },
    setFollowers: (state, action) => {
      state.followers = action.payload;
    },
    setFollowing: (state, action) => {
      state.following = action.payload;
    },
    setSuggestions: (state, action) => {
      state.suggestions = action.payload;
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setUsers,
  addUser,
  updateUser,
  setCurrentProfile,
  setFollowers,
  setFollowing,
  setSuggestions,
  setLoading,
  setError,
  clearError,
} = userSlice.actions;

export default userSlice.reducer;
