-- =====================================================
-- Migration: Create Follows Table
-- =====================================================

CREATE TABLE IF NOT EXISTS follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_id BIGINT NOT NULL,
    following_id BIGINT NOT NULL,
    
    -- Follow Status
    status ENUM('pending', 'accepted', 'blocked') DEFAULT 'accepted',
    
    -- Notifications
    notifications_enabled BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Constraints
    UNIQUE KEY unique_follow (follower_id, following_id),
    CHECK (follower_id != following_id),
    
    -- Indexes
    INDEX idx_follower_id (follower_id),
    INDEX idx_following_id (following_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
