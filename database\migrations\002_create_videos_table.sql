-- =====================================================
-- Migration: Create Videos Table
-- =====================================================

CREATE TABLE IF NOT EXISTS videos (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    video_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    duration INT NOT NULL, -- in seconds
    file_size BIGINT, -- in bytes
    resolution VARCHAR(20), -- e.g., "1080x1920"
    format VARCHAR(10) DEFAULT 'mp4',
    
    -- Content Details
    hashtags JSON,
    mentions JSON,
    location VARCHAR(100),
    music_id BIGINT,
    
    -- Privacy & Visibility
    privacy ENUM('public', 'friends', 'private') DEFAULT 'public',
    allow_comments BOOLEAN DEFAULT TRUE,
    allow_duet BOOLEAN DEFAULT TRUE,
    allow_stitch BOOLEAN DEFAULT TRUE,
    allow_download BOOLEAN DEFAULT FALSE,
    
    -- Engagement Stats
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    downloads_count INT DEFAULT 0,
    
    -- Content Moderation
    is_approved BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    content_warning BOOLEAN DEFAULT FALSE,
    age_restriction ENUM('none', '13+', '16+', '18+') DEFAULT 'none',
    
    -- AI Analysis
    ai_tags JSON,
    ai_sentiment VARCHAR(20),
    ai_content_score DECIMAL(3,2),
    ai_quality_score DECIMAL(3,2),
    
    -- Processing Status
    processing_status ENUM('uploading', 'processing', 'ready', 'failed') DEFAULT 'uploading',
    processing_progress INT DEFAULT 0,
    
    -- Timestamps
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (music_id) REFERENCES music(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_published_at (published_at),
    INDEX idx_privacy (privacy),
    INDEX idx_views_count (views_count),
    INDEX idx_likes_count (likes_count),
    INDEX idx_is_trending (is_trending),
    INDEX idx_is_featured (is_featured),
    INDEX idx_processing_status (processing_status),
    INDEX idx_hashtags ((CAST(hashtags AS CHAR(255) ARRAY))),
    FULLTEXT INDEX idx_title_description (title, description)
);
