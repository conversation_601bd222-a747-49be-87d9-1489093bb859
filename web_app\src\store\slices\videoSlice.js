// =====================================================
// Video Slice - إدارة حالة الفيديوهات
// =====================================================

import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  videos: [],
  currentVideo: null,
  trending: [],
  forYou: [],
  following: [],
  isLoading: false,
  error: null,
  hasMore: true,
  page: 1,
};

const videoSlice = createSlice({
  name: 'video',
  initialState,
  reducers: {
    setVideos: (state, action) => {
      state.videos = action.payload;
    },
    addVideos: (state, action) => {
      state.videos = [...state.videos, ...action.payload];
    },
    addVideo: (state, action) => {
      state.videos.unshift(action.payload);
    },
    updateVideo: (state, action) => {
      const index = state.videos.findIndex(video => video.id === action.payload.id);
      if (index !== -1) {
        state.videos[index] = { ...state.videos[index], ...action.payload };
      }
    },
    removeVideo: (state, action) => {
      state.videos = state.videos.filter(video => video.id !== action.payload);
    },
    setCurrentVideo: (state, action) => {
      state.currentVideo = action.payload;
    },
    setTrending: (state, action) => {
      state.trending = action.payload;
    },
    setForYou: (state, action) => {
      state.forYou = action.payload;
    },
    setFollowing: (state, action) => {
      state.following = action.payload;
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setHasMore: (state, action) => {
      state.hasMore = action.payload;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
    incrementPage: (state) => {
      state.page += 1;
    },
    resetPagination: (state) => {
      state.page = 1;
      state.hasMore = true;
    },
  },
});

export const {
  setVideos,
  addVideos,
  addVideo,
  updateVideo,
  removeVideo,
  setCurrentVideo,
  setTrending,
  setForYou,
  setFollowing,
  setLoading,
  setError,
  clearError,
  setHasMore,
  setPage,
  incrementPage,
  resetPagination,
} = videoSlice.actions;

export default videoSlice.reducer;
