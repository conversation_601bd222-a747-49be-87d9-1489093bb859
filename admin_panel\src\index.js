// =====================================================
// نقطة البداية - لوحة التحكم الإدارية
// =====================================================

import React from 'react';
import { createRoot } from 'react-dom/client';
import { StrictMode } from 'react';

// App Component
import App from './App';

// Performance monitoring
import reportWebVitals from './reportWebVitals';

// Get root element
const container = document.getElementById('root');
const root = createRoot(container);

// Render admin app
root.render(
  <StrictMode>
    <App />
  </StrictMode>
);

// Performance monitoring for admin panel
reportWebVitals((metric) => {
  // Log performance metrics
  console.log('📊 Admin Panel Performance:', metric);
  
  // Send to analytics service if needed
  // Example: gtag('event', metric.name, { value: metric.value });
});

// Global error handler for admin panel
window.addEventListener('error', (event) => {
  console.error('❌ Admin Panel Error:', event.error);
  // Send error to monitoring service
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ Admin Panel Unhandled Rejection:', event.reason);
  // Send error to monitoring service
});

// Development helpers
if (process.env.NODE_ENV === 'development') {
  console.log(`
⚙️ TikTok Clone Admin Panel
📦 Version: ${process.env.REACT_APP_VERSION || '1.0.0'}
🌍 Environment: ${process.env.NODE_ENV}
🔗 API URL: ${process.env.REACT_APP_API_URL || 'http://localhost:5000'}
🔐 Admin Mode: Enabled
  `);
}
