// =====================================================
// Error Boundary Component
// =====================================================

import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { ErrorOutline, Refresh } from '@mui/icons-material';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo);
    }
    
    // You can also log the error to an error reporting service here
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  render() {
    if (this.state.hasError) {
      return (
        <Container maxWidth="sm">
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            minHeight="100vh"
            textAlign="center"
            gap={3}
          >
            <ErrorOutline 
              sx={{ 
                fontSize: 80, 
                color: 'error.main',
                opacity: 0.7 
              }} 
            />
            
            <Typography variant="h4" component="h1" gutterBottom>
              عذراً، حدث خطأ!
            </Typography>
            
            <Typography variant="body1" color="text.secondary" paragraph>
              حدث خطأ غير متوقع في التطبيق. يرجى المحاولة مرة أخرى.
            </Typography>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <Box
                component="details"
                sx={{
                  width: '100%',
                  p: 2,
                  bgcolor: 'grey.100',
                  borderRadius: 1,
                  fontSize: '0.875rem',
                  fontFamily: 'monospace',
                  textAlign: 'left',
                  overflow: 'auto',
                  maxHeight: 200,
                }}
              >
                <summary style={{ cursor: 'pointer', marginBottom: 8 }}>
                  تفاصيل الخطأ (للمطورين)
                </summary>
                <pre>{this.state.error && this.state.error.toString()}</pre>
                <pre>{this.state.errorInfo.componentStack}</pre>
              </Box>
            )}
            
            <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
              <Button
                variant="contained"
                startIcon={<Refresh />}
                onClick={this.handleReload}
                size="large"
              >
                إعادة تحميل الصفحة
              </Button>
              
              <Button
                variant="outlined"
                onClick={this.handleReset}
                size="large"
              >
                المحاولة مرة أخرى
              </Button>
            </Box>
            
            <Typography variant="caption" color="text.secondary">
              إذا استمر الخطأ، يرجى التواصل مع الدعم الفني
            </Typography>
          </Box>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
