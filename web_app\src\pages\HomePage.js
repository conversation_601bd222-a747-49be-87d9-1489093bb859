// =====================================================
// Home Page - الصفحة الرئيسية
// =====================================================

import React from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Card, 
  CardContent,
  Grid,
  Chip,
  Button
} from '@mui/material';
import { 
  PlayArrow as PlayIcon,
  TrendingUp as TrendingIcon,
  People as PeopleIcon,
  Star as StarIcon
} from '@mui/icons-material';

const HomePage = () => {
  const features = [
    {
      title: 'التوأم الإبداعي',
      description: 'نسخة ذكية تتعلم من أسلوبك وتساعدك في الإبداع',
      icon: '🤖',
      status: 'جديد'
    },
    {
      title: 'الرادار الاجتماعي',
      description: 'اكتشف الأشخاص والمحتوى القريب منك جغرافياً',
      icon: '📡',
      status: 'شائع'
    },
    {
      title: 'توليد فيديو بالصوت',
      description: 'أنشئ فيديوهات مذهلة من خلال الأوامر الصوتية',
      icon: '🎤',
      status: 'ثوري'
    },
    {
      title: 'وضع الفن الفوري',
      description: 'حول فيديوهاتك إلى أعمال فنية رائعة',
      icon: '🎨',
      status: 'مميز'
    }
  ];

  const stats = [
    { label: 'المستخدمين النشطين', value: '10M+', icon: PeopleIcon },
    { label: 'الفيديوهات المنشورة', value: '500M+', icon: PlayIcon },
    { label: 'المشاهدات اليومية', value: '2B+', icon: TrendingIcon },
    { label: 'التقييم', value: '4.9★', icon: StarIcon },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Hero Section */}
      <Box textAlign="center" mb={6}>
        <Typography 
          variant="h2" 
          component="h1" 
          gutterBottom
          sx={{ 
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #FF0050, #25F4EE)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2
          }}
        >
          مرحباً بك في TikTok Clone
        </Typography>
        
        <Typography 
          variant="h5" 
          color="text.secondary" 
          paragraph
          sx={{ maxWidth: 600, mx: 'auto' }}
        >
          اكتشف عالماً جديداً من الفيديوهات القصيرة مع 48+ ميزة ثورية مبتكرة
        </Typography>

        <Button
          variant="contained"
          size="large"
          startIcon={<PlayIcon />}
          sx={{ 
            mt: 2,
            px: 4,
            py: 1.5,
            borderRadius: 3,
            fontSize: '1.1rem'
          }}
        >
          ابدأ الاستكشاف
        </Button>
      </Box>

      {/* Stats Section */}
      <Grid container spacing={3} mb={6}>
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Grid item xs={6} md={3} key={index}>
              <Card 
                sx={{ 
                  textAlign: 'center',
                  p: 2,
                  height: '100%',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)'
                  }
                }}
              >
                <Icon 
                  sx={{ 
                    fontSize: 40, 
                    color: 'primary.main',
                    mb: 1
                  }} 
                />
                <Typography variant="h4" fontWeight="bold" color="primary">
                  {stat.value}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {stat.label}
                </Typography>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Features Section */}
      <Box mb={6}>
        <Typography 
          variant="h4" 
          component="h2" 
          textAlign="center" 
          gutterBottom
          fontWeight="bold"
        >
          الميزات الثورية
        </Typography>
        
        <Typography 
          variant="body1" 
          color="text.secondary" 
          textAlign="center"
          paragraph
          sx={{ maxWidth: 600, mx: 'auto', mb: 4 }}
        >
          اكتشف مجموعة من الميزات المبتكرة التي تجعل تجربتك فريدة ومميزة
        </Typography>

        <Grid container spacing={3}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  transition: 'all 0.3s',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: 4
                  }
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box display="flex" alignItems="flex-start" gap={2}>
                    <Box
                      sx={{
                        fontSize: 40,
                        lineHeight: 1,
                        mt: 0.5
                      }}
                    >
                      {feature.icon}
                    </Box>
                    
                    <Box flex={1}>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Typography variant="h6" fontWeight="bold">
                          {feature.title}
                        </Typography>
                        <Chip 
                          label={feature.status}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </Box>
                      
                      <Typography 
                        variant="body2" 
                        color="text.secondary"
                        lineHeight={1.6}
                      >
                        {feature.description}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Call to Action */}
      <Card 
        sx={{ 
          background: 'linear-gradient(135deg, #FF0050, #25F4EE)',
          color: 'white',
          textAlign: 'center',
          p: 4
        }}
      >
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          جاهز لتجربة المستقبل؟
        </Typography>
        
        <Typography variant="h6" paragraph sx={{ opacity: 0.9 }}>
          انضم إلى ملايين المستخدمين واكتشف عالماً جديداً من الإبداع
        </Typography>
        
        <Button
          variant="contained"
          size="large"
          sx={{ 
            bgcolor: 'white',
            color: 'primary.main',
            '&:hover': {
              bgcolor: 'grey.100'
            },
            px: 4,
            py: 1.5,
            borderRadius: 3,
            fontSize: '1.1rem',
            fontWeight: 'bold'
          }}
        >
          ابدأ الآن مجاناً
        </Button>
      </Card>
    </Container>
  );
};

export default HomePage;
