// =====================================================
// Auth Controller - متحكم المصادقة
// =====================================================

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const User = require('../models/User');
const { validationResult } = require('express-validator');
const { sendEmail } = require('../utils/email');
const { generateTokens, verifyRefreshToken } = require('../utils/jwt');
const { createResponse, createErrorResponse } = require('../utils/response');

class AuthController {
  // تسجيل مستخدم جديد
  async register(req, res) {
    try {
      // التحقق من صحة البيانات
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          createErrorResponse('بيانات غير صحيحة', errors.array())
        );
      }

      const { username, email, password, fullName, phone } = req.body;

      // التحقق من وجود المستخدم
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });

      if (existingUser) {
        return res.status(400).json(
          createErrorResponse('المستخدم موجود بالفعل')
        );
      }

      // إنشاء مستخدم جديد
      const user = new User({
        username,
        email,
        password,
        fullName,
        phone
      });

      await user.save();

      // إنشاء الرموز المميزة
      const { accessToken, refreshToken } = generateTokens(user._id);

      // حفظ رمز التحديث
      await user.addRefreshToken(
        refreshToken,
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        req.get('User-Agent')
      );

      // إرسال بريد إلكتروني للتحقق
      await this.sendVerificationEmail(user);

      res.status(201).json(
        createResponse('تم إنشاء الحساب بنجاح', {
          token: accessToken,
          refreshToken,
          user: user.toJSON()
        })
      );
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // تسجيل الدخول
  async login(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          createErrorResponse('بيانات غير صحيحة', errors.array())
        );
      }

      const { email, password, remember = false } = req.body;

      // البحث عن المستخدم
      const user = await User.findByEmailOrUsername(email);
      if (!user) {
        return res.status(401).json(
          createErrorResponse('بيانات الدخول غير صحيحة')
        );
      }

      // التحقق من كلمة المرور
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json(
          createErrorResponse('بيانات الدخول غير صحيحة')
        );
      }

      // التحقق من حالة الحساب
      if (!user.isActive) {
        return res.status(401).json(
          createErrorResponse('الحساب معطل')
        );
      }

      if (user.isBanned) {
        return res.status(401).json(
          createErrorResponse('الحساب محظور')
        );
      }

      // تحديث معلومات آخر دخول
      user.lastLoginAt = new Date();
      user.lastLoginIP = req.ip;
      await user.save();

      // إنشاء الرموز المميزة
      const expiresIn = remember ? '30d' : '7d';
      const { accessToken, refreshToken } = generateTokens(user._id, expiresIn);

      // حفظ رمز التحديث
      const refreshExpiry = remember 
        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      await user.addRefreshToken(
        refreshToken,
        refreshExpiry,
        req.get('User-Agent')
      );

      res.json(
        createResponse('تم تسجيل الدخول بنجاح', {
          token: accessToken,
          refreshToken,
          user: user.toJSON()
        })
      );
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // تحديث الرمز المميز
  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(401).json(
          createErrorResponse('رمز التحديث مطلوب')
        );
      }

      // التحقق من صحة رمز التحديث
      const decoded = verifyRefreshToken(refreshToken);
      if (!decoded) {
        return res.status(401).json(
          createErrorResponse('رمز التحديث غير صحيح')
        );
      }

      // البحث عن المستخدم والتحقق من رمز التحديث
      const user = await User.findById(decoded.userId);
      if (!user) {
        return res.status(401).json(
          createErrorResponse('المستخدم غير موجود')
        );
      }

      const tokenExists = user.refreshTokens.some(
        rt => rt.token === refreshToken && rt.expiresAt > new Date()
      );

      if (!tokenExists) {
        return res.status(401).json(
          createErrorResponse('رمز التحديث منتهي الصلاحية')
        );
      }

      // إنشاء رموز جديدة
      const { accessToken, refreshToken: newRefreshToken } = generateTokens(user._id);

      // إزالة الرمز القديم وإضافة الجديد
      await user.removeRefreshToken(refreshToken);
      await user.addRefreshToken(
        newRefreshToken,
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        req.get('User-Agent')
      );

      res.json(
        createResponse('تم تحديث الرمز بنجاح', {
          token: accessToken,
          refreshToken: newRefreshToken
        })
      );
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // تسجيل الخروج
  async logout(req, res) {
    try {
      const { refreshToken } = req.body;
      const user = req.user;

      if (refreshToken) {
        await user.removeRefreshToken(refreshToken);
      }

      res.json(
        createResponse('تم تسجيل الخروج بنجاح')
      );
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // تسجيل الخروج من جميع الأجهزة
  async logoutAll(req, res) {
    try {
      const user = req.user;
      user.refreshTokens = [];
      await user.save();

      res.json(
        createResponse('تم تسجيل الخروج من جميع الأجهزة')
      );
    } catch (error) {
      console.error('Logout all error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // إرسال رمز إعادة تعيين كلمة المرور
  async forgotPassword(req, res) {
    try {
      const { email } = req.body;

      const user = await User.findOne({ email });
      if (!user) {
        return res.status(404).json(
          createErrorResponse('المستخدم غير موجود')
        );
      }

      // إنشاء رمز إعادة التعيين
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // حفظ الرمز في قاعدة البيانات (يجب إضافة هذه الحقول للنموذج)
      user.passwordResetToken = resetToken;
      user.passwordResetExpiry = resetTokenExpiry;
      await user.save();

      // إرسال البريد الإلكتروني
      await sendEmail({
        to: user.email,
        subject: 'إعادة تعيين كلمة المرور',
        template: 'reset-password',
        data: {
          name: user.fullName,
          resetUrl: `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`
        }
      });

      res.json(
        createResponse('تم إرسال رابط إعادة التعيين إلى بريدك الإلكتروني')
      );
    } catch (error) {
      console.error('Forgot password error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // إعادة تعيين كلمة المرور
  async resetPassword(req, res) {
    try {
      const { token, password } = req.body;

      const user = await User.findOne({
        passwordResetToken: token,
        passwordResetExpiry: { $gt: new Date() }
      });

      if (!user) {
        return res.status(400).json(
          createErrorResponse('رمز إعادة التعيين غير صحيح أو منتهي الصلاحية')
        );
      }

      // تحديث كلمة المرور
      user.password = password;
      user.passwordResetToken = undefined;
      user.passwordResetExpiry = undefined;
      user.refreshTokens = []; // إلغاء جميع الجلسات
      await user.save();

      res.json(
        createResponse('تم تغيير كلمة المرور بنجاح')
      );
    } catch (error) {
      console.error('Reset password error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // تغيير كلمة المرور
  async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body;
      const user = req.user;

      // التحقق من كلمة المرور الحالية
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json(
          createErrorResponse('كلمة المرور الحالية غير صحيحة')
        );
      }

      // تحديث كلمة المرور
      user.password = newPassword;
      await user.save();

      res.json(
        createResponse('تم تغيير كلمة المرور بنجاح')
      );
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // التحقق من البريد الإلكتروني
  async verifyEmail(req, res) {
    try {
      const { token } = req.params;

      // هنا يجب التحقق من رمز التحقق
      // للبساطة، سنفترض أن الرمز صحيح
      const user = await User.findOne({ emailVerificationToken: token });
      
      if (!user) {
        return res.status(400).json(
          createErrorResponse('رمز التحقق غير صحيح')
        );
      }

      user.emailVerifiedAt = new Date();
      user.emailVerificationToken = undefined;
      await user.save();

      res.json(
        createResponse('تم التحقق من البريد الإلكتروني بنجاح')
      );
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // إرسال بريد التحقق
  async sendVerificationEmail(user) {
    try {
      const verificationToken = crypto.randomBytes(32).toString('hex');
      
      // حفظ الرمز (يجب إضافة هذا الحقل للنموذج)
      user.emailVerificationToken = verificationToken;
      await user.save();

      await sendEmail({
        to: user.email,
        subject: 'تأكيد البريد الإلكتروني',
        template: 'verify-email',
        data: {
          name: user.fullName,
          verificationUrl: `${process.env.FRONTEND_URL}/verify-email/${verificationToken}`
        }
      });
    } catch (error) {
      console.error('Send verification email error:', error);
    }
  }

  // الحصول على المستخدم الحالي
  async getMe(req, res) {
    try {
      const user = req.user;
      res.json(
        createResponse('تم جلب بيانات المستخدم بنجاح', {
          user: user.toJSON()
        })
      );
    } catch (error) {
      console.error('Get me error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }
}

module.exports = new AuthController();
