// =====================================================
// Image Processor Utilities - أدوات معالجة الصور
// =====================================================

const sharp = require('sharp');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// إعدادات معالجة الصور
const IMAGE_SETTINGS = {
  avatar: {
    sizes: [
      { name: 'small', width: 50, height: 50 },
      { name: 'medium', width: 150, height: 150 },
      { name: 'large', width: 300, height: 300 }
    ],
    quality: 85,
    format: 'jpeg'
  },
  cover: {
    sizes: [
      { name: 'small', width: 640, height: 360 },
      { name: 'medium', width: 1280, height: 720 },
      { name: 'large', width: 1920, height: 1080 }
    ],
    quality: 90,
    format: 'jpeg'
  },
  thumbnail: {
    sizes: [
      { name: 'small', width: 320, height: 180 },
      { name: 'medium', width: 640, height: 360 },
      { name: 'large', width: 1280, height: 720 }
    ],
    quality: 80,
    format: 'jpeg'
  },
  general: {
    maxWidth: 2048,
    maxHeight: 2048,
    quality: 85,
    format: 'jpeg'
  }
};

// رفع ومعالجة الصورة
const uploadImage = async (imageFile, type = 'general') => {
  try {
    const imageId = crypto.randomUUID();
    const uploadDir = path.join('uploads', 'images', type, imageId);
    
    // إنشاء مجلد الصورة
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // الحصول على معلومات الصورة
    const imageInfo = await getImageInfo(imageFile.path);
    
    // التحقق من صحة الصورة
    validateImage(imageInfo);

    // معالجة الصورة حسب النوع
    const processedImages = await processImageByType(imageFile.path, uploadDir, type);

    return {
      imageId,
      url: processedImages.main,
      sizes: processedImages.sizes,
      originalInfo: imageInfo,
      uploadDir
    };
  } catch (error) {
    console.error('Upload image error:', error);
    throw new Error('فشل في رفع الصورة: ' + error.message);
  }
};

// الحصول على معلومات الصورة
const getImageInfo = async (filePath) => {
  try {
    const metadata = await sharp(filePath).metadata();
    const stats = fs.statSync(filePath);

    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: stats.size,
      channels: metadata.channels,
      hasAlpha: metadata.hasAlpha,
      density: metadata.density
    };
  } catch (error) {
    throw new Error('فشل في قراءة معلومات الصورة');
  }
};

// التحقق من صحة الصورة
const validateImage = (imageInfo) => {
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const supportedFormats = ['jpeg', 'jpg', 'png', 'webp', 'gif'];

  if (imageInfo.size > maxFileSize) {
    throw new Error('حجم الصورة كبير جداً');
  }

  if (!supportedFormats.includes(imageInfo.format.toLowerCase())) {
    throw new Error('تنسيق الصورة غير مدعوم');
  }

  if (imageInfo.width < 50 || imageInfo.height < 50) {
    throw new Error('دقة الصورة منخفضة جداً');
  }
};

// معالجة الصورة حسب النوع
const processImageByType = async (inputPath, outputDir, type) => {
  const settings = IMAGE_SETTINGS[type] || IMAGE_SETTINGS.general;
  const results = { sizes: {} };

  if (settings.sizes) {
    // إنشاء أحجام متعددة
    for (const size of settings.sizes) {
      const outputPath = path.join(outputDir, `${size.name}.${settings.format}`);
      
      await sharp(inputPath)
        .resize(size.width, size.height, {
          fit: type === 'avatar' ? 'cover' : 'inside',
          position: 'center'
        })
        .jpeg({ quality: settings.quality })
        .toFile(outputPath);

      results.sizes[size.name] = outputPath.replace(/\\/g, '/');
    }

    // الحجم الرئيسي هو المتوسط
    results.main = results.sizes.medium || results.sizes.large || results.sizes.small;
  } else {
    // معالجة عامة
    const outputPath = path.join(outputDir, `processed.${settings.format}`);
    
    await sharp(inputPath)
      .resize(settings.maxWidth, settings.maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality: settings.quality })
      .toFile(outputPath);

    results.main = outputPath.replace(/\\/g, '/');
  }

  return results;
};

// تغيير حجم الصورة
const resizeImage = async (inputPath, outputPath, width, height, options = {}) => {
  try {
    const {
      fit = 'inside',
      position = 'center',
      quality = 85,
      format = 'jpeg'
    } = options;

    await sharp(inputPath)
      .resize(width, height, { fit, position })
      .jpeg({ quality })
      .toFile(outputPath);

    return outputPath;
  } catch (error) {
    throw new Error('فشل في تغيير حجم الصورة: ' + error.message);
  }
};

// قص الصورة
const cropImage = async (inputPath, outputPath, x, y, width, height) => {
  try {
    await sharp(inputPath)
      .extract({ left: x, top: y, width, height })
      .toFile(outputPath);

    return outputPath;
  } catch (error) {
    throw new Error('فشل في قص الصورة: ' + error.message);
  }
};

// تطبيق فلتر على الصورة
const applyFilter = async (inputPath, outputPath, filterType) => {
  try {
    let pipeline = sharp(inputPath);

    switch (filterType) {
      case 'grayscale':
        pipeline = pipeline.grayscale();
        break;
      case 'blur':
        pipeline = pipeline.blur(3);
        break;
      case 'sharpen':
        pipeline = pipeline.sharpen();
        break;
      case 'sepia':
        pipeline = pipeline.tint({ r: 255, g: 240, b: 196 });
        break;
      case 'vintage':
        pipeline = pipeline
          .modulate({ brightness: 0.9, saturation: 0.8 })
          .tint({ r: 255, g: 240, b: 200 });
        break;
      case 'bright':
        pipeline = pipeline.modulate({ brightness: 1.2 });
        break;
      case 'dark':
        pipeline = pipeline.modulate({ brightness: 0.8 });
        break;
      default:
        throw new Error('نوع الفلتر غير مدعوم');
    }

    await pipeline.toFile(outputPath);
    return outputPath;
  } catch (error) {
    throw new Error('فشل في تطبيق الفلتر: ' + error.message);
  }
};

// ضغط الصورة
const compressImage = async (inputPath, outputPath, quality = 80) => {
  try {
    const metadata = await sharp(inputPath).metadata();
    
    let pipeline = sharp(inputPath);
    
    if (metadata.format === 'png') {
      pipeline = pipeline.png({ quality, compressionLevel: 9 });
    } else {
      pipeline = pipeline.jpeg({ quality });
    }

    await pipeline.toFile(outputPath);
    return outputPath;
  } catch (error) {
    throw new Error('فشل في ضغط الصورة: ' + error.message);
  }
};

// تحويل تنسيق الصورة
const convertFormat = async (inputPath, outputPath, format) => {
  try {
    let pipeline = sharp(inputPath);

    switch (format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        pipeline = pipeline.jpeg({ quality: 85 });
        break;
      case 'png':
        pipeline = pipeline.png({ quality: 85 });
        break;
      case 'webp':
        pipeline = pipeline.webp({ quality: 85 });
        break;
      case 'avif':
        pipeline = pipeline.avif({ quality: 85 });
        break;
      default:
        throw new Error('تنسيق غير مدعوم');
    }

    await pipeline.toFile(outputPath);
    return outputPath;
  } catch (error) {
    throw new Error('فشل في تحويل التنسيق: ' + error.message);
  }
};

// إضافة علامة مائية نصية
const addTextWatermark = async (inputPath, outputPath, text, options = {}) => {
  try {
    const {
      fontSize = 24,
      color = 'white',
      position = 'bottom-right',
      opacity = 0.7
    } = options;

    const metadata = await sharp(inputPath).metadata();
    
    // إنشاء SVG للنص
    const svgText = `
      <svg width="${metadata.width}" height="${metadata.height}">
        <text x="${getTextPosition(position, metadata.width, text.length * fontSize * 0.6).x}" 
              y="${getTextPosition(position, metadata.height, fontSize).y}" 
              font-family="Arial" 
              font-size="${fontSize}" 
              fill="${color}" 
              opacity="${opacity}">
          ${text}
        </text>
      </svg>
    `;

    const textBuffer = Buffer.from(svgText);

    await sharp(inputPath)
      .composite([{ input: textBuffer, blend: 'over' }])
      .toFile(outputPath);

    return outputPath;
  } catch (error) {
    throw new Error('فشل في إضافة العلامة المائية: ' + error.message);
  }
};

// إضافة علامة مائية صورة
const addImageWatermark = async (inputPath, watermarkPath, outputPath, options = {}) => {
  try {
    const {
      position = 'bottom-right',
      opacity = 0.7,
      scale = 0.1
    } = options;

    const mainImage = await sharp(inputPath).metadata();
    
    // تغيير حجم العلامة المائية
    const watermarkSize = Math.min(mainImage.width, mainImage.height) * scale;
    const watermarkBuffer = await sharp(watermarkPath)
      .resize(watermarkSize, watermarkSize, { fit: 'inside' })
      .png()
      .toBuffer();

    const position_coords = getImagePosition(position, mainImage.width, mainImage.height, watermarkSize);

    await sharp(inputPath)
      .composite([{
        input: watermarkBuffer,
        left: position_coords.x,
        top: position_coords.y,
        blend: 'over'
      }])
      .toFile(outputPath);

    return outputPath;
  } catch (error) {
    throw new Error('فشل في إضافة العلامة المائية: ' + error.message);
  }
};

// دمج صور متعددة
const mergeImages = async (imagePaths, outputPath, layout = 'horizontal') => {
  try {
    if (imagePaths.length < 2) {
      throw new Error('يجب توفير صورتين على الأقل للدمج');
    }

    const images = await Promise.all(
      imagePaths.map(path => sharp(path).metadata())
    );

    let totalWidth, totalHeight;
    
    if (layout === 'horizontal') {
      totalWidth = images.reduce((sum, img) => sum + img.width, 0);
      totalHeight = Math.max(...images.map(img => img.height));
    } else {
      totalWidth = Math.max(...images.map(img => img.width));
      totalHeight = images.reduce((sum, img) => sum + img.height, 0);
    }

    // إنشاء canvas فارغ
    let canvas = sharp({
      create: {
        width: totalWidth,
        height: totalHeight,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      }
    });

    // إضافة الصور
    const composite = [];
    let currentX = 0, currentY = 0;

    for (let i = 0; i < imagePaths.length; i++) {
      composite.push({
        input: imagePaths[i],
        left: currentX,
        top: currentY
      });

      if (layout === 'horizontal') {
        currentX += images[i].width;
      } else {
        currentY += images[i].height;
      }
    }

    await canvas.composite(composite).jpeg().toFile(outputPath);
    return outputPath;
  } catch (error) {
    throw new Error('فشل في دمج الصور: ' + error.message);
  }
};

// حذف ملفات الصورة
const deleteImageFiles = async (uploadDir) => {
  try {
    if (fs.existsSync(uploadDir)) {
      fs.rmSync(uploadDir, { recursive: true, force: true });
    }
  } catch (error) {
    console.error('Error deleting image files:', error);
  }
};

// دوال مساعدة
const getTextPosition = (position, width, textWidth) => {
  const margin = 20;
  
  switch (position) {
    case 'top-left':
      return { x: margin, y: margin + 20 };
    case 'top-right':
      return { x: width - textWidth - margin, y: margin + 20 };
    case 'bottom-left':
      return { x: margin, y: width - margin };
    case 'bottom-right':
      return { x: width - textWidth - margin, y: width - margin };
    case 'center':
      return { x: (width - textWidth) / 2, y: width / 2 };
    default:
      return { x: width - textWidth - margin, y: width - margin };
  }
};

const getImagePosition = (position, mainWidth, mainHeight, watermarkSize) => {
  const margin = 20;
  
  switch (position) {
    case 'top-left':
      return { x: margin, y: margin };
    case 'top-right':
      return { x: mainWidth - watermarkSize - margin, y: margin };
    case 'bottom-left':
      return { x: margin, y: mainHeight - watermarkSize - margin };
    case 'bottom-right':
      return { x: mainWidth - watermarkSize - margin, y: mainHeight - watermarkSize - margin };
    case 'center':
      return { x: (mainWidth - watermarkSize) / 2, y: (mainHeight - watermarkSize) / 2 };
    default:
      return { x: mainWidth - watermarkSize - margin, y: mainHeight - watermarkSize - margin };
  }
};

module.exports = {
  uploadImage,
  getImageInfo,
  validateImage,
  processImageByType,
  resizeImage,
  cropImage,
  applyFilter,
  compressImage,
  convertFormat,
  addTextWatermark,
  addImageWatermark,
  mergeImages,
  deleteImageFiles,
  IMAGE_SETTINGS
};
