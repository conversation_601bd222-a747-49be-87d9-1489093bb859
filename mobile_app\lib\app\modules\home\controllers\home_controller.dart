// =====================================================
// Home Controller - متحكم الرئيسية
// =====================================================

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tiktok_clone/app/data/models/video_model.dart';
import 'package:tiktok_clone/app/data/models/user_model.dart';
import 'package:tiktok_clone/app/data/services/video_service.dart';
import 'package:tiktok_clone/app/data/services/user_service.dart';
import 'package:tiktok_clone/app/data/services/analytics_service.dart';
import 'package:tiktok_clone/app/utils/constants.dart';

class HomeController extends GetxController {
  // Services
  final VideoService _videoService = Get.find<VideoService>();
  final UserService _userService = Get.find<UserService>();
  final AnalyticsService _analyticsService = Get.find<AnalyticsService>();

  // Page Controller
  late PageController pageController;

  // Observable Variables
  final RxList<VideoModel> videos = <VideoModel>[].obs;
  final RxInt currentIndex = 0.obs;
  final RxInt currentBottomIndex = 0.obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isFollowingTab = false.obs;
  final RxInt currentPage = 1.obs;
  final RxBool hasMoreVideos = true.obs;

  // Video States
  final RxMap<String, bool> videoLikeStates = <String, bool>{}.obs;
  final RxMap<String, bool> videoSaveStates = <String, bool>{}.obs;

  @override
  void onInit() {
    super.onInit();
    pageController = PageController();
    loadVideos();
    _trackScreenView();
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  // Load Videos
  Future<void> loadVideos({bool refresh = false}) async {
    try {
      if (refresh) {
        currentPage.value = 1;
        hasMoreVideos.value = true;
        videos.clear();
      }

      if (!hasMoreVideos.value) return;

      isLoading.value = videos.isEmpty;
      isLoadingMore.value = videos.isNotEmpty;
      hasError.value = false;

      final response = await _videoService.getFeed(
        page: currentPage.value,
        limit: Constants.defaultPageSize,
        following: isFollowingTab.value,
      );

      if (response.success) {
        final newVideos = response.data as List<VideoModel>;
        
        if (refresh) {
          videos.assignAll(newVideos);
        } else {
          videos.addAll(newVideos);
        }

        // Update like and save states
        for (final video in newVideos) {
          videoLikeStates[video.id] = video.isLiked;
          videoSaveStates[video.id] = video.isSaved;
        }

        currentPage.value++;
        hasMoreVideos.value = newVideos.length >= Constants.defaultPageSize;
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      Get.snackbar(
        'خطأ',
        'فشل في تحميل الفيديوهات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  // Switch to Following Tab
  void switchToFollowing() {
    if (!isFollowingTab.value) {
      isFollowingTab.value = true;
      loadVideos(refresh: true);
      _analyticsService.trackEvent('tab_switch', {'tab': 'following'});
    }
  }

  // Switch to For You Tab
  void switchToForYou() {
    if (isFollowingTab.value) {
      isFollowingTab.value = false;
      loadVideos(refresh: true);
      _analyticsService.trackEvent('tab_switch', {'tab': 'for_you'});
    }
  }

  // Page Changed
  void onPageChanged(int index) {
    currentIndex.value = index;
    
    // Load more videos when near the end
    if (index >= videos.length - 3 && !isLoadingMore.value) {
      loadVideos();
    }

    // Track video view
    if (index < videos.length) {
      final video = videos[index];
      _trackVideoView(video);
    }
  }

  // Toggle Play/Pause
  void togglePlayPause() {
    // This will be handled by the VideoPlayerWidget
    _analyticsService.trackEvent('video_interaction', {
      'action': 'play_pause',
      'video_id': videos[currentIndex.value].id,
    });
  }

  // Toggle Like
  Future<void> toggleLike(VideoModel video) async {
    try {
      final currentLikeState = videoLikeStates[video.id] ?? false;
      
      // Optimistic update
      videoLikeStates[video.id] = !currentLikeState;
      video.likesCount += currentLikeState ? -1 : 1;
      video.isLiked = !currentLikeState;

      final response = await _videoService.toggleLike(video.id);
      
      if (!response.success) {
        // Revert on failure
        videoLikeStates[video.id] = currentLikeState;
        video.likesCount += currentLikeState ? 1 : -1;
        video.isLiked = currentLikeState;
        
        Get.snackbar(
          'خطأ',
          'فشل في تحديث الإعجاب',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        _analyticsService.trackEvent('video_like', {
          'video_id': video.id,
          'action': currentLikeState ? 'unlike' : 'like',
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الإعجاب',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Share Video
  Future<void> shareVideo(VideoModel video) async {
    try {
      final response = await _videoService.shareVideo(video.id);
      
      if (response.success) {
        video.sharesCount++;
        
        // Show share options
        Get.bottomSheet(
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'مشاركة الفيديو',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildShareOption(Icons.copy, 'نسخ الرابط', () {
                      // Copy link logic
                      Get.back();
                    }),
                    _buildShareOption(Icons.message, 'واتساب', () {
                      // WhatsApp share logic
                      Get.back();
                    }),
                    _buildShareOption(Icons.share, 'تطبيقات أخرى', () {
                      // Other apps share logic
                      Get.back();
                    }),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );

        _analyticsService.trackEvent('video_share', {
          'video_id': video.id,
          'platform': 'internal',
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في مشاركة الفيديو',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Save Video
  Future<void> saveVideo(VideoModel video) async {
    try {
      final currentSaveState = videoSaveStates[video.id] ?? false;
      
      // Optimistic update
      videoSaveStates[video.id] = !currentSaveState;
      video.isSaved = !currentSaveState;

      final response = await _videoService.toggleSave(video.id);
      
      if (!response.success) {
        // Revert on failure
        videoSaveStates[video.id] = currentSaveState;
        video.isSaved = currentSaveState;
        
        Get.snackbar(
          'خطأ',
          'فشل في حفظ الفيديو',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar(
          'نجح',
          currentSaveState ? 'تم إلغاء حفظ الفيديو' : 'تم حفظ الفيديو',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        _analyticsService.trackEvent('video_save', {
          'video_id': video.id,
          'action': currentSaveState ? 'unsave' : 'save',
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ الفيديو',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Download Video
  Future<void> downloadVideo(VideoModel video) async {
    try {
      Get.snackbar(
        'جاري التحميل',
        'بدء تحميل الفيديو...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );

      final response = await _videoService.downloadVideo(video.id);
      
      if (response.success) {
        Get.snackbar(
          'تم التحميل',
          'تم تحميل الفيديو بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        _analyticsService.trackEvent('video_download', {
          'video_id': video.id,
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحميل الفيديو',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Report Video
  Future<void> reportVideo(VideoModel video) async {
    Get.toNamed('/report', arguments: {
      'type': 'video',
      'id': video.id,
      'title': video.title,
    });
  }

  // Block User
  Future<void> blockUser(UserModel user) async {
    try {
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('حظر المستخدم'),
          content: Text('هل أنت متأكد من حظر ${user.fullName}؟'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('حظر'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final response = await _userService.blockUser(user.id);
        
        if (response.success) {
          // Remove videos from this user
          videos.removeWhere((video) => video.user.id == user.id);
          
          Get.snackbar(
            'تم الحظر',
            'تم حظر المستخدم بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          _analyticsService.trackEvent('user_block', {
            'blocked_user_id': user.id,
          });
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حظر المستخدم',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Bottom Navigation
  void onBottomNavTap(int index) {
    currentBottomIndex.value = index;
    
    switch (index) {
      case 0:
        // Already on home
        break;
      case 1:
        Get.toNamed('/discover');
        break;
      case 2:
        Get.toNamed('/create');
        break;
      case 3:
        Get.toNamed('/messages');
        break;
      case 4:
        Get.toNamed('/profile');
        break;
    }

    _analyticsService.trackEvent('bottom_nav_tap', {'index': index});
  }

  // Helper Methods
  Widget _buildShareOption(IconData icon, String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 24),
          ),
          const SizedBox(height: 8),
          Text(label, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  // Analytics
  void _trackScreenView() {
    _analyticsService.trackScreenView('home');
  }

  void _trackVideoView(VideoModel video) {
    _analyticsService.trackEvent('video_view', {
      'video_id': video.id,
      'user_id': video.user.id,
      'duration': video.duration,
    });
  }

  // Refresh
  Future<void> onRefresh() async {
    await loadVideos(refresh: true);
  }
}
