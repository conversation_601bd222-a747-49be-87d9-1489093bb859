// =====================================================
// Layout Component - التخطيط الرئيسي
// =====================================================

import React from 'react';
import { Box, AppBar, Toolbar, Typography, IconButton, Avatar, Badge } from '@mui/material';
import { 
  Menu as MenuIcon, 
  Search as SearchIcon, 
  Notifications as NotificationsIcon,
  Add as AddIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  Message as MessageIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useTheme } from '../../hooks/useTheme';

const Layout = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();

  const navigationItems = [
    { icon: HomeIcon, label: 'الرئيسية', path: '/' },
    { icon: SearchIcon, label: 'البحث', path: '/search' },
    { icon: AddIcon, label: 'إنشاء', path: '/create' },
    { icon: MessageIcon, label: 'الرسائل', path: '/messages' },
    { icon: PersonIcon, label: 'الملف الشخصي', path: `/profile/${user?.username}` },
  ];

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Header */}
      <AppBar 
        position="sticky" 
        elevation={0}
        sx={{ 
          bgcolor: 'background.paper',
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          {/* Logo */}
          <Box display="flex" alignItems="center" gap={1}>
            <IconButton 
              edge="start" 
              color="primary"
              onClick={() => navigate('/')}
            >
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: 'primary.main',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: 18,
                  fontWeight: 'bold'
                }}
              >
                ▶
              </Box>
            </IconButton>
            <Typography 
              variant="h6" 
              component="div" 
              sx={{ 
                fontWeight: 'bold',
                color: 'text.primary',
                display: { xs: 'none', sm: 'block' }
              }}
            >
              TikTok Clone
            </Typography>
          </Box>

          {/* Navigation - Desktop */}
          <Box 
            sx={{ 
              display: { xs: 'none', md: 'flex' },
              gap: 1
            }}
          >
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <IconButton
                  key={item.path}
                  onClick={() => navigate(item.path)}
                  sx={{
                    color: isActive ? 'primary.main' : 'text.secondary',
                    '&:hover': {
                      color: 'primary.main',
                      bgcolor: 'primary.main',
                      opacity: 0.1
                    }
                  }}
                >
                  <Icon />
                </IconButton>
              );
            })}
          </Box>

          {/* Right Side */}
          <Box display="flex" alignItems="center" gap={1}>
            {/* Notifications */}
            <IconButton color="inherit">
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            {/* User Avatar */}
            <IconButton 
              onClick={() => navigate(`/profile/${user?.username}`)}
              sx={{ p: 0 }}
            >
              <Avatar 
                src={user?.avatar_url} 
                alt={user?.username}
                sx={{ width: 32, height: 32 }}
              >
                {user?.username?.charAt(0)?.toUpperCase()}
              </Avatar>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box 
        component="main" 
        sx={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {children}
      </Box>

      {/* Bottom Navigation - Mobile */}
      <Box
        sx={{
          display: { xs: 'flex', md: 'none' },
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          bgcolor: 'background.paper',
          borderTop: 1,
          borderColor: 'divider',
          py: 1,
          px: 2,
          justifyContent: 'space-around',
          zIndex: 1000
        }}
      >
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <IconButton
              key={item.path}
              onClick={() => navigate(item.path)}
              sx={{
                flexDirection: 'column',
                gap: 0.5,
                color: isActive ? 'primary.main' : 'text.secondary',
                '&:hover': {
                  color: 'primary.main'
                }
              }}
            >
              <Icon fontSize="small" />
              <Typography variant="caption" fontSize={10}>
                {item.label}
              </Typography>
            </IconButton>
          );
        })}
      </Box>

      {/* Bottom Padding for Mobile Navigation */}
      <Box sx={{ display: { xs: 'block', md: 'none' }, height: 80 }} />
    </Box>
  );
};

export default Layout;
