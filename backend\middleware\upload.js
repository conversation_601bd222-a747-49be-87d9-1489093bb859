// =====================================================
// Upload Middleware - وسطاء رفع الملفات
// =====================================================

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const { createErrorResponse } = require('../utils/response');

// إنشاء مجلدات التخزين
const createUploadDirs = () => {
  const dirs = [
    'uploads',
    'uploads/videos',
    'uploads/images',
    'uploads/avatars',
    'uploads/covers',
    'uploads/thumbnails',
    'uploads/music',
    'uploads/temp'
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

// إنشاء المجلدات عند بدء التشغيل
createUploadDirs();

// تكوين التخزين
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads/';
    
    switch (file.fieldname) {
      case 'video':
        uploadPath += 'videos/';
        break;
      case 'thumbnail':
        uploadPath += 'thumbnails/';
        break;
      case 'avatar':
        uploadPath += 'avatars/';
        break;
      case 'cover':
        uploadPath += 'covers/';
        break;
      case 'music':
        uploadPath += 'music/';
        break;
      case 'image':
        uploadPath += 'images/';
        break;
      default:
        uploadPath += 'temp/';
    }
    
    cb(null, uploadPath);
  },
  
  filename: (req, file, cb) => {
    // إنشاء اسم ملف فريد
    const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(6).toString('hex');
    const ext = path.extname(file.originalname);
    const name = file.fieldname + '-' + uniqueSuffix + ext;
    cb(null, name);
  }
});

// فلترة أنواع الملفات
const fileFilter = (req, file, cb) => {
  const allowedTypes = {
    video: ['video/mp4', 'video/avi', 'video/mov', 'video/mkv', 'video/webm'],
    thumbnail: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    avatar: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    cover: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    image: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
    music: ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/aac', 'audio/ogg']
  };

  const fieldAllowedTypes = allowedTypes[file.fieldname] || [];
  
  if (fieldAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`نوع الملف غير مدعوم للحقل ${file.fieldname}. الأنواع المدعومة: ${fieldAllowedTypes.join(', ')}`), false);
  }
};

// حدود الملفات
const limits = {
  fileSize: {
    video: 500 * 1024 * 1024, // 500MB
    thumbnail: 5 * 1024 * 1024, // 5MB
    avatar: 5 * 1024 * 1024, // 5MB
    cover: 10 * 1024 * 1024, // 10MB
    image: 10 * 1024 * 1024, // 10MB
    music: 50 * 1024 * 1024 // 50MB
  }
};

// إنشاء multer instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB max
    files: 10 // 10 files max
  }
});

// وسطاء معالجة الأخطاء
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json(
          createErrorResponse('حجم الملف كبير جداً')
        );
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json(
          createErrorResponse('عدد الملفات يتجاوز الحد المسموح')
        );
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json(
          createErrorResponse('حقل ملف غير متوقع')
        );
      default:
        return res.status(400).json(
          createErrorResponse('خطأ في رفع الملف')
        );
    }
  }
  
  if (error.message) {
    return res.status(400).json(
      createErrorResponse(error.message)
    );
  }
  
  next(error);
};

// وسطاء التحقق من حجم الملف حسب النوع
const checkFileSize = (req, res, next) => {
  if (!req.files) {
    return next();
  }

  const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
  
  for (const file of files) {
    const maxSize = limits.fileSize[file.fieldname];
    if (maxSize && file.size > maxSize) {
      // حذف الملف المرفوع
      fs.unlink(file.path, () => {});
      
      return res.status(400).json(
        createErrorResponse(`حجم ${file.fieldname} كبير جداً. الحد الأقصى: ${Math.round(maxSize / (1024 * 1024))}MB`)
      );
    }
  }
  
  next();
};

// وسطاء التحقق من صحة الملف
const validateFile = (req, res, next) => {
  if (!req.files) {
    return next();
  }

  const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
  
  for (const file of files) {
    // التحقق من وجود الملف
    if (!fs.existsSync(file.path)) {
      return res.status(400).json(
        createErrorResponse('فشل في رفع الملف')
      );
    }
    
    // التحقق من حجم الملف الفعلي
    const stats = fs.statSync(file.path);
    if (stats.size === 0) {
      fs.unlink(file.path, () => {});
      return res.status(400).json(
        createErrorResponse('الملف فارغ')
      );
    }
    
    // إضافة معلومات إضافية للملف
    file.uploadedAt = new Date();
    file.originalSize = stats.size;
  }
  
  next();
};

// وسطاء تنظيف الملفات المؤقتة
const cleanupTempFiles = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // تنظيف الملفات في حالة الخطأ
    if (res.statusCode >= 400 && req.files) {
      const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
      files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlink(file.path, () => {});
        }
      });
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

// وسطاء إنشاء URL للملفات
const generateFileUrls = (req, res, next) => {
  if (!req.files) {
    return next();
  }

  const baseUrl = `${req.protocol}://${req.get('host')}`;
  const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
  
  files.forEach(file => {
    file.url = `${baseUrl}/${file.path.replace(/\\/g, '/')}`;
  });
  
  next();
};

// دالة حذف الملف
const deleteFile = (filePath) => {
  return new Promise((resolve, reject) => {
    if (!filePath || !fs.existsSync(filePath)) {
      return resolve();
    }
    
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error('Error deleting file:', err);
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

// دالة نقل الملف
const moveFile = (oldPath, newPath) => {
  return new Promise((resolve, reject) => {
    // إنشاء المجلد إذا لم يكن موجوداً
    const dir = path.dirname(newPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.rename(oldPath, newPath, (err) => {
      if (err) {
        console.error('Error moving file:', err);
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

// دالة الحصول على معلومات الملف
const getFileInfo = (filePath) => {
  if (!fs.existsSync(filePath)) {
    return null;
  }
  
  const stats = fs.statSync(filePath);
  const ext = path.extname(filePath);
  const name = path.basename(filePath, ext);
  
  return {
    path: filePath,
    name: name,
    extension: ext,
    size: stats.size,
    createdAt: stats.birthtime,
    modifiedAt: stats.mtime
  };
};

// تصدير الوسطاء والدوال
module.exports = {
  upload,
  handleUploadError,
  checkFileSize,
  validateFile,
  cleanupTempFiles,
  generateFileUrls,
  deleteFile,
  moveFile,
  getFileInfo,
  
  // وسطاء مخصصة لأنواع مختلفة من الملفات
  single: (fieldName) => [
    upload.single(fieldName),
    handleUploadError,
    checkFileSize,
    validateFile,
    cleanupTempFiles,
    generateFileUrls
  ],
  
  array: (fieldName, maxCount = 10) => [
    upload.array(fieldName, maxCount),
    handleUploadError,
    checkFileSize,
    validateFile,
    cleanupTempFiles,
    generateFileUrls
  ],
  
  fields: (fields) => [
    upload.fields(fields),
    handleUploadError,
    checkFileSize,
    validateFile,
    cleanupTempFiles,
    generateFileUrls
  ],
  
  any: () => [
    upload.any(),
    handleUploadError,
    checkFileSize,
    validateFile,
    cleanupTempFiles,
    generateFileUrls
  ]
};
