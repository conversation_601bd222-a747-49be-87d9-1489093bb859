// =====================================================
// Not Found Page - صفحة غير موجودة
// =====================================================

import React from 'react';
import { Box, Container, Typography, Button } from '@mui/material';
import { Home as HomeIcon, ArrowBack as BackIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const NotFoundPage = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="sm" sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center' }}>
      <Box textAlign="center" width="100%">
        <Typography 
          variant="h1" 
          sx={{ 
            fontSize: '8rem',
            fontWeight: 'bold',
            color: 'primary.main',
            opacity: 0.7,
            mb: 2
          }}
        >
          404
        </Typography>
        
        <Typography variant="h4" gutterBottom fontWeight="bold">
          الصفحة غير موجودة
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          عذراً، لا يمكن العثور على الصفحة التي تبحث عنها.
        </Typography>
        
        <Box display="flex" gap={2} justifyContent="center" flexWrap="wrap">
          <Button
            variant="contained"
            startIcon={<HomeIcon />}
            onClick={() => navigate('/')}
            size="large"
          >
            العودة للرئيسية
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate(-1)}
            size="large"
          >
            العودة للخلف
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default NotFoundPage;
