-- =====================================================
-- Migration: Create Analytics Table
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- Entity Information
    entity_type ENUM('user', 'video', 'hashtag', 'music', 'app') NOT NULL,
    entity_id BIGINT,
    
    -- Event Information
    event_type VARCHAR(100) NOT NULL, -- view, like, share, download, etc.
    event_category VARCHAR(50), -- engagement, content, user_action
    
    -- User Information
    user_id BIGINT,
    session_id VARCHAR(255),
    
    -- Device & Location
    device_type ENUM('mobile', 'tablet', 'desktop', 'tv', 'other'),
    platform ENUM('ios', 'android', 'web', 'other'),
    app_version VARCHAR(20),
    os_version VARCHAR(50),
    
    -- Geographic Data
    country VARCHAR(2), -- ISO country code
    region VARCHAR(100),
    city VARCHAR(100),
    timezone VARCHAR(50),
    
    -- Technical Details
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(500),
    
    -- Event Data
    properties JSON, -- custom event properties
    value DECIMAL(10,2), -- numeric value for the event
    duration INT, -- duration in seconds (for time-based events)
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    event_date DATE GENERATED ALWAYS AS (DATE(created_at)) STORED,
    event_hour TINYINT GENERATED ALWAYS AS (HOUR(created_at)) STORED,
    
    -- Foreign Keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_entity_type_id (entity_type, entity_id),
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_event_date (event_date),
    INDEX idx_event_hour (event_hour),
    INDEX idx_country (country),
    INDEX idx_platform (platform),
    INDEX idx_device_type (device_type),
    INDEX idx_session_id (session_id)
);
