// =====================================================
// Comment Controller - متحكم التعليقات
// =====================================================

const Comment = require('../models/Comment');
const Video = require('../models/Video');
const Like = require('../models/Like');
const Notification = require('../models/Notification');
const { validationResult } = require('express-validator');
const { createResponse, createErrorResponse } = require('../utils/response');

class CommentController {
  // الحصول على تعليقات الفيديو
  async getVideoComments(req, res) {
    try {
      const { videoId } = req.params;
      const {
        page = 1,
        limit = 20,
        sortBy = 'recent',
        includeReplies = false
      } = req.query;

      const video = await Video.findById(videoId);
      if (!video) {
        return res.status(404).json(
          createErrorResponse('الفيديو غير موجود')
        );
      }

      const comments = await Comment.getVideoComments(videoId, {
        page: parseInt(page),
        limit: parseInt(limit),
        sortBy,
        includeReplies: includeReplies === 'true'
      });

      const total = await Comment.countDocuments({
        video: videoId,
        isHidden: false,
        parent: includeReplies ? undefined : null
      });

      res.json(
        createResponse('تم جلب التعليقات بنجاح', {
          comments,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Get video comments error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // إضافة تعليق جديد
  async addComment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          createErrorResponse('بيانات غير صحيحة', errors.array())
        );
      }

      const { videoId } = req.params;
      const { content, parentId } = req.body;
      const userId = req.user._id;

      const video = await Video.findById(videoId);
      if (!video) {
        return res.status(404).json(
          createErrorResponse('الفيديو غير موجود')
        );
      }

      if (!video.allowComments) {
        return res.status(403).json(
          createErrorResponse('التعليقات غير مسموحة على هذا الفيديو')
        );
      }

      // التحقق من التعليق الأب إذا كان موجوداً
      if (parentId) {
        const parentComment = await Comment.findById(parentId);
        if (!parentComment || !parentComment.video.equals(videoId)) {
          return res.status(400).json(
            createErrorResponse('التعليق الأب غير صحيح')
          );
        }
      }

      const comment = new Comment({
        user: userId,
        video: videoId,
        parent: parentId || null,
        content
      });

      await comment.save();
      await comment.populate('user', 'username fullName avatarUrl isVerified');

      // إنشاء إشعار للمستخدم صاحب الفيديو
      if (!video.user.equals(userId)) {
        await Notification.createCommentNotification(videoId, userId, content);
      }

      // إنشاء إشعار لصاحب التعليق الأب (في حالة الرد)
      if (parentId) {
        const parentComment = await Comment.findById(parentId).populate('user');
        if (parentComment && !parentComment.user._id.equals(userId)) {
          await Notification.createNotification({
            userId: parentComment.user._id,
            senderId: userId,
            type: 'comment_reply',
            title: `رد على تعليقك`,
            message: `رد ${req.user.fullName} على تعليقك: "${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`,
            videoId: videoId,
            commentId: comment._id
          });
        }
      }

      res.status(201).json(
        createResponse('تم إضافة التعليق بنجاح', {
          comment
        })
      );
    } catch (error) {
      console.error('Add comment error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في إضافة التعليق')
      );
    }
  }

  // الحصول على ردود التعليق
  async getCommentReplies(req, res) {
    try {
      const { commentId } = req.params;
      const {
        page = 1,
        limit = 10,
        sortBy = 'oldest'
      } = req.query;

      const comment = await Comment.findById(commentId);
      if (!comment) {
        return res.status(404).json(
          createErrorResponse('التعليق غير موجود')
        );
      }

      const replies = await Comment.getCommentReplies(commentId, {
        page: parseInt(page),
        limit: parseInt(limit),
        sortBy
      });

      const total = await Comment.countDocuments({
        parent: commentId,
        isHidden: false
      });

      res.json(
        createResponse('تم جلب الردود بنجاح', {
          replies,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Get comment replies error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // الإعجاب بالتعليق
  async likeComment(req, res) {
    try {
      const { commentId } = req.params;
      const userId = req.user._id;

      const comment = await Comment.findById(commentId);
      if (!comment) {
        return res.status(404).json(
          createErrorResponse('التعليق غير موجود')
        );
      }

      const result = await Like.toggleLike(userId, commentId, 'Comment');

      res.json(
        createResponse(
          result.action === 'liked' ? 'تم الإعجاب بالتعليق' : 'تم إلغاء الإعجاب',
          {
            action: result.action,
            likesCount: comment.likesCount + (result.action === 'liked' ? 1 : -1)
          }
        )
      );
    } catch (error) {
      console.error('Like comment error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في الخادم')
      );
    }
  }

  // تحديث التعليق
  async updateComment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(
          createErrorResponse('بيانات غير صحيحة', errors.array())
        );
      }

      const { commentId } = req.params;
      const { content } = req.body;
      const userId = req.user._id;

      const comment = await Comment.findById(commentId);
      if (!comment) {
        return res.status(404).json(
          createErrorResponse('التعليق غير موجود')
        );
      }

      // التحقق من الملكية
      if (!comment.user.equals(userId)) {
        return res.status(403).json(
          createErrorResponse('غير مسموح بتعديل هذا التعليق')
        );
      }

      comment.content = content;
      comment.markAsEdited();
      await comment.save();

      res.json(
        createResponse('تم تحديث التعليق بنجاح', {
          comment
        })
      );
    } catch (error) {
      console.error('Update comment error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في تحديث التعليق')
      );
    }
  }

  // حذف التعليق
  async deleteComment(req, res) {
    try {
      const { commentId } = req.params;
      const userId = req.user._id;

      const comment = await Comment.findById(commentId);
      if (!comment) {
        return res.status(404).json(
          createErrorResponse('التعليق غير موجود')
        );
      }

      // التحقق من الملكية أو الصلاحيات الإدارية
      const isOwner = comment.user.equals(userId);
      const isAdmin = req.user.roles.includes('admin') || req.user.roles.includes('moderator');

      if (!isOwner && !isAdmin) {
        return res.status(403).json(
          createErrorResponse('غير مسموح بحذف هذا التعليق')
        );
      }

      await comment.remove();

      res.json(
        createResponse('تم حذف التعليق بنجاح')
      );
    } catch (error) {
      console.error('Delete comment error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في حذف التعليق')
      );
    }
  }

  // تثبيت التعليق
  async pinComment(req, res) {
    try {
      const { commentId } = req.params;
      const userId = req.user._id;

      const comment = await Comment.findById(commentId).populate('video');
      if (!comment) {
        return res.status(404).json(
          createErrorResponse('التعليق غير موجود')
        );
      }

      // التحقق من ملكية الفيديو
      if (!comment.video.user.equals(userId)) {
        return res.status(403).json(
          createErrorResponse('يمكن فقط لصاحب الفيديو تثبيت التعليقات')
        );
      }

      // إلغاء تثبيت التعليقات الأخرى
      await Comment.updateMany(
        { video: comment.video._id, isPinned: true },
        { isPinned: false }
      );

      comment.isPinned = !comment.isPinned;
      await comment.save();

      res.json(
        createResponse(
          comment.isPinned ? 'تم تثبيت التعليق' : 'تم إلغاء تثبيت التعليق',
          { comment }
        )
      );
    } catch (error) {
      console.error('Pin comment error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في تثبيت التعليق')
      );
    }
  }

  // إخفاء التعليق
  async hideComment(req, res) {
    try {
      const { commentId } = req.params;
      const userId = req.user._id;

      const comment = await Comment.findById(commentId).populate('video');
      if (!comment) {
        return res.status(404).json(
          createErrorResponse('التعليق غير موجود')
        );
      }

      // التحقق من الصلاحيات
      const isVideoOwner = comment.video.user.equals(userId);
      const isAdmin = req.user.roles.includes('admin') || req.user.roles.includes('moderator');

      if (!isVideoOwner && !isAdmin) {
        return res.status(403).json(
          createErrorResponse('غير مسموح بإخفاء هذا التعليق')
        );
      }

      comment.isHidden = !comment.isHidden;
      await comment.save();

      res.json(
        createResponse(
          comment.isHidden ? 'تم إخفاء التعليق' : 'تم إظهار التعليق',
          { comment }
        )
      );
    } catch (error) {
      console.error('Hide comment error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في إخفاء التعليق')
      );
    }
  }

  // البحث في التعليقات
  async searchComments(req, res) {
    try {
      const {
        q: query,
        page = 1,
        limit = 20,
        videoId = null
      } = req.query;

      if (!query) {
        return res.status(400).json(
          createErrorResponse('نص البحث مطلوب')
        );
      }

      const comments = await Comment.searchComments(query, {
        page: parseInt(page),
        limit: parseInt(limit),
        videoId
      });

      const searchQuery = {
        $text: { $search: query },
        isHidden: false,
        isSpam: false
      };

      if (videoId) {
        searchQuery.video = videoId;
      }

      const total = await Comment.countDocuments(searchQuery);

      res.json(
        createResponse('تم البحث بنجاح', {
          comments,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        })
      );
    } catch (error) {
      console.error('Search comments error:', error);
      res.status(500).json(
        createErrorResponse('خطأ في البحث')
      );
    }
  }
}

module.exports = new CommentController();
