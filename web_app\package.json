{"name": "tiktok-clone-web", "version": "1.0.0", "description": "موقع TikTok Clone الثوري", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "react-router-dom": "^6.18.0", "react-query": "^3.39.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "framer-motion": "^10.16.5", "react-spring": "^9.7.3", "react-player": "^2.13.0", "react-webcam": "^7.1.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.47.0", "yup": "^1.3.3", "@hookform/resolvers": "^3.3.2", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "react-infinite-scroll-component": "^6.1.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "workbox-background-sync": "^6.6.0", "workbox-broadcast-update": "^6.6.0", "workbox-cacheable-response": "^6.6.0", "workbox-core": "^6.6.0", "workbox-expiration": "^6.6.0", "workbox-google-analytics": "^6.6.0", "workbox-navigation-preload": "^6.6.0", "workbox-precaching": "^6.6.0", "workbox-range-requests": "^6.6.0", "workbox-routing": "^6.6.0", "workbox-strategies": "^6.6.0", "workbox-streams": "^6.6.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.14.201", "@types/uuid": "^9.0.7", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0"}, "proxy": "http://localhost:5000"}