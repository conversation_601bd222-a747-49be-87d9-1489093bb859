// =====================================================
// Auth Service - خدمة المصادقة
// =====================================================

import { apiService } from './apiService';

export const authService = {
  // تسجيل الدخول
  login: async (email, password, remember = false) => {
    const response = await apiService.post('/auth/login', {
      email,
      password,
      remember,
    });
    return response.data;
  },

  // تسجيل حساب جديد
  register: async (userData) => {
    const response = await apiService.post('/auth/register', userData);
    return response.data;
  },

  // تسجيل الخروج
  logout: async () => {
    const response = await apiService.post('/auth/logout');
    return response.data;
  },

  // تحديث الرمز المميز
  refreshToken: async (refreshToken) => {
    const response = await apiService.post('/auth/refresh', {
      refreshToken,
    });
    return response.data;
  },

  // الحصول على بيانات المستخدم الحالي
  getCurrentUser: async () => {
    const response = await apiService.get('/auth/me');
    return response.data;
  },

  // التحقق من صحة الرمز المميز
  verifyToken: async () => {
    const response = await apiService.get('/auth/verify');
    return response.data;
  },

  // إرسال رمز إعادة تعيين كلمة المرور
  forgotPassword: async (email) => {
    const response = await apiService.post('/auth/forgot-password', {
      email,
    });
    return response.data;
  },

  // إعادة تعيين كلمة المرور
  resetPassword: async (token, password, confirmPassword) => {
    const response = await apiService.post('/auth/reset-password', {
      token,
      password,
      confirmPassword,
    });
    return response.data;
  },

  // تغيير كلمة المرور
  changePassword: async (currentPassword, newPassword, confirmPassword) => {
    const response = await apiService.put('/auth/change-password', {
      currentPassword,
      newPassword,
      confirmPassword,
    });
    return response.data;
  },

  // تأكيد البريد الإلكتروني
  verifyEmail: async (token) => {
    const response = await apiService.post('/auth/verify-email', {
      token,
    });
    return response.data;
  },

  // إعادة إرسال رمز تأكيد البريد الإلكتروني
  resendEmailVerification: async () => {
    const response = await apiService.post('/auth/resend-verification');
    return response.data;
  },
};
