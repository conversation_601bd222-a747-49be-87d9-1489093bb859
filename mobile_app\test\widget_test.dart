// =====================================================
// Flutter Widget Tests
// =====================================================

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:tiktok_clone/main.dart';
import 'package:tiktok_clone/app/modules/splash/views/splash_view.dart';
import 'package:tiktok_clone/app/modules/auth/views/login_view.dart';
import 'package:tiktok_clone/app/modules/home/<USER>/home_view.dart';

void main() {
  group('TikTok Clone App Tests', () {
    testWidgets('App should start with splash screen', (WidgetTester tester) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(MyApp());

      // Verify that splash screen is displayed
      expect(find.byType(SplashView), findsOneWidget);
      
      // Verify app logo or title is present
      expect(find.text('TikTok Clone'), findsOneWidget);
    });

    testWidgets('Splash screen should navigate to login', (WidgetTester tester) async {
      await tester.pumpWidget(MyApp());

      // Wait for splash screen animation
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Should navigate to login screen
      expect(find.byType(LoginView), findsOneWidget);
    });
  });

  group('Splash Screen Tests', () => {
    testWidgets('Splash screen displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: SplashView(),
        ),
      );

      // Check if splash screen elements are present
      expect(find.text('TikTok Clone'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Splash screen has proper animations', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: SplashView(),
        ),
      );

      // Check for animated widgets
      expect(find.byType(AnimatedContainer), findsWidgets);
      
      // Pump frames to test animations
      await tester.pump(Duration(milliseconds: 500));
      await tester.pump(Duration(milliseconds: 500));
    });
  });

  group('Login Screen Tests', () => {
    testWidgets('Login screen displays all required fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: LoginView(),
        ),
      );

      // Check for login form elements
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password
      expect(find.text('تسجيل الدخول'), findsOneWidget);
      expect(find.text('البريد الإلكتروني'), findsOneWidget);
      expect(find.text('كلمة المرور'), findsOneWidget);
    });

    testWidgets('Login form validation works', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: LoginView(),
        ),
      );

      // Find login button and tap it without filling fields
      final loginButton = find.text('تسجيل الدخول');
      await tester.tap(loginButton);
      await tester.pump();

      // Should show validation errors
      expect(find.text('هذا الحقل مطلوب'), findsWidgets);
    });

    testWidgets('Email field accepts valid input', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: LoginView(),
        ),
      );

      // Find email field and enter text
      final emailField = find.byKey(Key('email_field'));
      await tester.enterText(emailField, '<EMAIL>');
      await tester.pump();

      // Verify text was entered
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('Password field hides text by default', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: LoginView(),
        ),
      );

      // Find password field
      final passwordField = find.byKey(Key('password_field'));
      final textField = tester.widget<TextFormField>(passwordField);
      
      // Should be obscured by default
      expect(textField.obscureText, isTrue);
    });

    testWidgets('Password visibility toggle works', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: LoginView(),
        ),
      );

      // Find and tap password visibility toggle
      final visibilityToggle = find.byIcon(Icons.visibility);
      await tester.tap(visibilityToggle);
      await tester.pump();

      // Icon should change to visibility_off
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });
  });

  group('Home Screen Tests', () => {
    testWidgets('Home screen displays navigation', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: HomeView(),
        ),
      );

      // Check for bottom navigation
      expect(find.byType(BottomNavigationBar), findsOneWidget);
      
      // Check navigation items
      expect(find.text('الرئيسية'), findsOneWidget);
      expect(find.text('اكتشف'), findsOneWidget);
      expect(find.text('إنشاء'), findsOneWidget);
      expect(find.text('الرسائل'), findsOneWidget);
      expect(find.text('الملف الشخصي'), findsOneWidget);
    });

    testWidgets('Home screen displays video feed', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: HomeView(),
        ),
      );

      // Check for video feed container
      expect(find.byType(PageView), findsOneWidget);
    });

    testWidgets('Navigation between tabs works', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: HomeView(),
        ),
      );

      // Tap on discover tab
      await tester.tap(find.text('اكتشف'));
      await tester.pump();

      // Should navigate to discover page
      // Add specific checks based on your discover page implementation
    });
  });

  group('Widget Interaction Tests', () => {
    testWidgets('Buttons respond to taps', (WidgetTester tester) async {
      bool buttonPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ElevatedButton(
              onPressed: () {
                buttonPressed = true;
              },
              child: Text('Test Button'),
            ),
          ),
        ),
      );

      // Tap the button
      await tester.tap(find.text('Test Button'));
      await tester.pump();

      // Verify button was pressed
      expect(buttonPressed, isTrue);
    });

    testWidgets('Text fields accept input', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextField(
              controller: controller,
            ),
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byType(TextField), 'Test input');
      await tester.pump();

      // Verify text was entered
      expect(controller.text, 'Test input');
    });
  });

  group('Performance Tests', () => {
    testWidgets('App renders without performance issues', (WidgetTester tester) async {
      // Measure rendering time
      final stopwatch = Stopwatch()..start();
      
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // Should render within reasonable time (adjust threshold as needed)
      expect(stopwatch.elapsedMilliseconds, lessThan(5000));
    });

    testWidgets('Scrolling performance is smooth', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ListView.builder(
              itemCount: 100,
              itemBuilder: (context, index) => ListTile(
                title: Text('Item $index'),
              ),
            ),
          ),
        ),
      );

      // Perform scroll gestures
      await tester.fling(find.byType(ListView), Offset(0, -500), 1000);
      await tester.pumpAndSettle();

      // Should complete without errors
      expect(tester.takeException(), isNull);
    });
  });
}
