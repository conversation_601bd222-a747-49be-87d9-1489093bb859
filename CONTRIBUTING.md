# 🤝 دليل المساهمة - TikTok Clone

مرحباً بك في مشروع TikTok Clone! نحن نرحب بمساهماتك ونقدر وقتك وجهدك.

## 📋 كيفية المساهمة

### 1. إعداد البيئة المحلية

```bash
# استنساخ المشروع
git clone <repository-url>
cd tiktok_clone

# تثبيت المتطلبات
npm install

# إعداد قاعدة البيانات
# اتبع دليل GETTING_STARTED.md
```

### 2. إنشاء فرع جديد

```bash
# إنشاء فرع للميزة الجديدة
git checkout -b feature/اسم-الميزة

# أو لإصلاح خطأ
git checkout -b fix/وصف-الإصلاح
```

### 3. تطوير التغييرات

- اكتب كود نظيف ومفهوم
- اتبع معايير الكود المحددة
- أضف تعليقات باللغة العربية أو الإنجليزية
- اختبر تغييراتك جيداً

### 4. إرسال Pull Request

```bash
# إضافة التغييرات
git add .

# إنشاء commit
git commit -m "وصف واضح للتغييرات"

# رفع الفرع
git push origin feature/اسم-الميزة
```

## 🎯 أنواع المساهمات المرحب بها

### 🐛 إصلاح الأخطاء
- إصلاح bugs في الكود
- تحسين الأداء
- إصلاح مشاكل الأمان

### ✨ ميزات جديدة
- إضافة ميزات ثورية جديدة
- تحسين الواجهات
- تطوير APIs جديدة

### 📚 التوثيق
- تحسين التوثيق الموجود
- إضافة أمثلة جديدة
- ترجمة المحتوى

### 🎨 التصميم
- تحسين UI/UX
- إضافة themes جديدة
- تحسين الاستجابة

## 📝 معايير الكود

### JavaScript/Node.js
```javascript
// استخدم const/let بدلاً من var
const apiUrl = 'http://localhost:5000';

// أسماء واضحة للمتغيرات
const userAuthToken = generateToken();

// تعليقات باللغة العربية
// هذه الدالة تقوم بتسجيل دخول المستخدم
function loginUser(credentials) {
  // كود التسجيل
}
```

### React Components
```jsx
// استخدم Functional Components مع Hooks
const VideoPlayer = ({ videoUrl, autoPlay = false }) => {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  
  // تعليق واضح
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };
  
  return (
    <div className="video-player">
      {/* JSX content */}
    </div>
  );
};
```

### Flutter/Dart
```dart
// استخدم PascalCase للكلاسات
class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;
  final bool autoPlay;
  
  const VideoPlayerWidget({
    Key? key,
    required this.videoUrl,
    this.autoPlay = false,
  }) : super(key: key);
  
  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}
```

## 🧪 اختبار التغييرات

### Backend Tests
```bash
cd backend
npm test
```

### Frontend Tests
```bash
cd web_app
npm test

cd admin_panel
npm test
```

### Flutter Tests
```bash
cd mobile_app
flutter test
```

## 📋 قائمة التحقق قبل الإرسال

- [ ] الكود يعمل بدون أخطاء
- [ ] تم اختبار التغييرات محلياً
- [ ] تم إضافة تعليقات مناسبة
- [ ] تم تحديث التوثيق إذا لزم الأمر
- [ ] تم اتباع معايير الكود
- [ ] لا توجد console.log غير ضرورية
- [ ] تم التأكد من الأمان

## 🎯 أولويات المساهمة

### عالية الأولوية
1. إصلاح الأخطاء الأمنية
2. تحسين الأداء
3. إصلاح bugs حرجة

### متوسطة الأولوية
1. ميزات جديدة مطلوبة
2. تحسين UX/UI
3. تحسين التوثيق

### منخفضة الأولوية
1. تحسينات تجميلية
2. refactoring الكود
3. إضافة تعليقات

## 🚫 ما لا نقبله

- كود غير مختبر
- تغييرات تكسر الوظائف الموجودة
- كود بدون تعليقات
- انتهاك معايير الأمان
- نسخ كود من مصادر محمية بحقوق الطبع

## 📞 التواصل

### للأسئلة والاستفسارات
- افتح Issue جديد
- تواصل عبر Discord
- أرسل email للفريق

### للمناقشات
- استخدم Discussions في GitHub
- انضم لقناة Slack
- شارك في اجتماعات المطورين

## 🏆 الاعتراف بالمساهمين

جميع المساهمين سيتم ذكرهم في:
- ملف CONTRIBUTORS.md
- صفحة About في التطبيق
- ملاحظات الإصدار

## 📜 قواعد السلوك

### نتوقع من جميع المساهمين:
- الاحترام المتبادل
- التواصل البناء
- تقبل النقد البناء
- مساعدة المطورين الجدد

### غير مقبول:
- التنمر أو التحرش
- اللغة المسيئة
- التمييز بأي شكل
- نشر محتوى غير لائق

## 🎉 شكراً لك!

مساهمتك تجعل هذا المشروع أفضل للجميع. نحن نقدر وقتك وجهدك في تطوير TikTok Clone.

---

**Happy Coding! 🚀**
